AWSTemplateFormatVersion: '2010-09-09'
Description: |
  Creates a central ECR repository that can be accessed by role ECRAccessRole from QA, Preprod, and Prod accounts

Metadata:
  AWS::CloudFormation::Interface:
    ParameterLabels:
          RepoName:
            default: Repository Name
          QAAccessRoleARN:
            default: QA Access RoleARN
          PreprodAccessRoleARN:
            default: Preprod Access Role ARN
          ProdAccessRoleARN:
            default: Production Access Role ARN
          CICDPushRoleARN:
            default: CICD Push Role ARN

Parameters:
  RepoName:
    Type: String
    Description: Name of the ECR repository

  QAAccessRoleARN:
    Type: String
    Description: ECR Role ARN in QA

  PreprodAccessRoleARN:
    Type: String
    Description: ECR Role ARN in Preprod

  ProdAccessRoleARN:
    Type: String
    Description: ECR Role ARN in Production

  CICDPushRoleARN:
    Type: String
    Description: CICD Push Role ARN

Resources:

  AIAgentECR:
    Type: AWS::ECR::Repository
    Properties:
      RepositoryName: !Ref RepoName
      EncryptionConfiguration:
        EncryptionType: AES256
      ImageScanningConfiguration:
        ScanOnPush: true
      RepositoryPolicyText:
        Fn::Sub: |
          {
            "Version": "2008-10-17",
            "Statement": [
              {
                "Sid": "ECRPullAccess",
                "Effect": "Allow",
                "Principal": {
                  "AWS": [
                    "${QAAccessRoleARN}",
                    "${PreprodAccessRoleARN}",
                    "${ProdAccessRoleARN}"
                  ]
                },
                "Action": [
                  "ecr:GetDownloadUrlForLayer",
                  "ecr:BatchGetImage",
                  "ecr:BatchCheckLayerAvailability",
                  "ecr:GetAuthorizationToken"
                ]
              },
              {
                "Sid": "ECRPushAndPullAccess",
                "Effect": "Allow",
                "Principal": {
                  "AWS": "${CICDPushRoleARN}"
                },
                "Action": [
                  "ecr:GetDownloadUrlForLayer",
                  "ecr:BatchGetImage",
                  "ecr:BatchCheckLayerAvailability",
                  "ecr:GetAuthorizationToken",
                  "ecr:InitiateLayerUpload",
                  "ecr:UploadLayerPart",
                  "ecr:CompleteLayerUpload",
                  "ecr:PutImage"
                ]
              }
            ]
          }
      Tags:
        - Key: Name
          Value: !Ref RepoName

Outputs:
  ECRRepositoryUri:
    Description: URI of the created ECR repository
    Value: !GetAtt AIAgentECR.RepositoryUri
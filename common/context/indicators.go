package contextutils

const (
	TAG_PREFIX = "Tag: "

	NAME_ONLY_PREFIX        = "n:"
	EX_EMPLOYEE_PREFIX      = "[X] "
	INVALID_EMPLOYEE_PREFIX = "[I] "

	IAM_USER_SUFFIX                = " <IAM User>"
	IAM_ROLE_SUFFIX                = " <IAM Role>"
	APP_USER_SUFFIX                = " <Application>"
	SERVICEACCOUNT_USER_SUFFIX     = " <Service Account>"
	PLACEHOLDER_USER_SUFFIX        = " <Placeholder User>"
	RESOURCE_NAME_TEMP_USER_SUFFIX = " <Resource Name>"
	AWSSERVICE_USER_SUFFIX         = " <AWS Service>"
	ACCOUNT_USER_SUFFIX            = " <AWS Account>"
	INCLUDED_USER_SUFFIX           = " <Included User>"

	EXT_KEYWORD            = "#ext#"
	LIVE_MICROSOFT_KEYWORD = "live.com#"
	MAIL_MICROSOFT_KEYWORD = "mail#"

	INTERNAL_PREFIX       = "PrecizeInternal:"
	COMMIT_FILE_SUBSTRING = "commitFile:"

	INVALID_EMAIL_ERROR   = "Invalid Email"
	NO_EMAIL_FORMAT_ERROR = "No email format"

	PARENT_CHILD_INVERSE = " <inverse>"
)

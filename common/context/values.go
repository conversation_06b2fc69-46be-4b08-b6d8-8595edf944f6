package contextutils

var OwnerTagKeys = map[string]struct{}{
	`owner`: {}, `email`: {}, `creator`: {}, `developer`: {}, `user`: {}, `contact`: {}, `approver`: {}, `owners`: {}, `admin`: {}, `support`: {},
}

var OwnerTagKeysRegex = map[string]struct{}{
	`created[-_\s]*by`: {}, `started[-_\s]*by`: {}, `developed[-_\s]*by`: {}, `modified[-_\s]*by`: {}, `approver[-_\s]*name`: {}, `approved[-_\s]*by`: {},
}

var CreatorTagKeys = map[string]struct{}{
	`creator`: {},
}

var CreatorTagKeysRegex = map[string]struct{}{
	`created[-_\s]*by`: {}, `started[-_\s]*by`: {},
}

var EnvTagKeys = map[string]struct{}{
	`env`: {}, `environment`: {}, `stage`: {}, `category`: {},
}

var SoftwareTagKeys = map[string]struct{}{
	`app`: {}, `application`: {}, `software`: {},
}

var AppTagKeys = map[string]struct{}{
	`app`: {}, `application`: {},
}

var CostCenterTagKeys = map[string]struct{}{
	`cost[-_\s]*center`: {}, `business[-_\s]*unit`: {}, `cost[-_\s]*bu`: {}, `^bu$`: {},
}

var TeamTagKeys = map[string]struct{}{
	`team`: {},
}

var RepoNameTagKeys = map[string]struct{}{
	`git-repo`: {}, `gitrepo`: {}, `git_repo`: {}, `repo`: {}, `repository`: {}, `repo-name`: {}, `reponame`: {},
}

var RepoNameTagKeysRegex = map[string]struct{}{
	`(?i)git[-_\s]*(repo|repository)`: {}, `(?i)(repo|repository)[-_\s]*name`: {}, `(?i)(repo|repository)[-_\s]*url`: {}, `(?i)source[-_\s]*(repo|repository)`: {},
}

var FileNameTagKeys = map[string]struct{}{
	`filename`: {}, `file-name`: {}, `gitfilename`: {}, `git-file-name`: {}, `file`: {}, `gitfile`: {},
}

var FileNameTagKeysRegex = map[string]struct{}{
	`(?i)git[-_\s]*file[-_\s]*name`: {}, `(?i)file[-_\s]*name`: {}, `(?i)source[-_\s]*file`: {}, `(?i)code[-_\s]*file`: {}, `(?i)repo[-_\s]*file`: {}, `(?i)git[-_\s]*file`: {},
}

var CommitIDTagKeys = map[string]struct{}{
	`commit`: {}, `commit-id`: {}, `commit_hash`: {}, `commit-hash`: {}, `gitcommit`: {},
}

var CommitIDTagKeysRegex = map[string]struct{}{
	`(?i)commit[-_\s]*id`: {}, `(?i)commit[-_\s]*hash`: {}, `(?i)git[-_\s]*commit`: {}, `(?i)commit[-_\s]*ref`: {},
}

var TTLTagKeys = map[string]struct{}{
	`\bttl\b`: {}, `time[-_\s]*to[-_\s]*live`: {}, `delete[-_\s]*me`: {}, `delete[-_\s]*on`: {}, `delete[-_\s]*by`: {},
}

var DescriptionTagKeys = map[string]struct{}{
	`description`: {}, `desc`: {},
}

var EnvValues = map[string][]string{
	STAGING_ENV: {`test`, `qa`, `stage`, `staging`, `\bstg\b`, `pre[-_\s]*prod`, `non[-_\s]*prod`},
	PROD_ENV:    {`\bprd\b`, `prod`, `production`},
	DEV_ENV:     {`\bdev\b`, `\bpoc\b`, `development`},
	SANDBOX_ENV: {`sandbox`},
}

var EnvNotValues = map[string][]string{
	`test`: {`latest`, `attest`, `contest`, `pentest`, `brightest`, `detest`, `protest`, `testosterone`, `fastest`, `bretttest`},
	`prod`: {`pre[-_\s]*prod`, `non[-_\s]*prod`, `producer`, `productive`, `product`},
}

var TTLDeleteValues = map[string]struct{}{
	`delete[-_\s]*after[-_\s]*use`: {}, `delete[-_\s]*now`: {}, `to[-_\s]*delete`: {},
}

var TTLDoNotDeleteValues = map[string]struct{}{
	`do[-_\s]*not[-_\s]*delete`: {}, `0000[-_\s]*00[-_\s]*00`: {}, `00[-_\s]*00[-_\s]*0000`: {}, `do[-_\s]*no[-_\s]*delete`: {},
}

var SoftwareValues = map[string][]string{
	MONGO_SOFTWARE:                     {`mongo`},
	ELASTIC_SOFTWARE:                   {`elastic[-_\s]*search`},
	POSTGRES_SOFTWARE:                  {`postgres`},
	SQL_SOFTWARE:                       {`mysql`},
	`VPN`:                              {`\bvpn\b`},
	`NAT`:                              {`\bnat\b`},
	`Nginx`:                            {`nginx`},
	`Nodejs`:                           {`node[-_\s]*js`},
	`Redis`:                            {`redis`},
	`Cassandra`:                        {`cassandra`},
	`DynamoDB`:                         {`dynamodb`},
	`CouchDB`:                          {`couchdb`},
	`SQLite`:                           {`sqlite`},
	`InfluxDB`:                         {`influxdb`},
	`Neo4j`:                            {`neo4j`},
	`ClickHouse`:                       {`clickhouse`},
	`MariaDB`:                          {`mariadb`},
	`RocksDB`:                          {`rocksdb`},
	`Apache Kafka`:                     {`kafka`},
	`RabbitMQ`:                         {`rabbitmq`},
	`NATS`:                             {`\bnats\b`},
	`MQTT`:                             {`\bmqtt\b`},
	`ZeroMQ`:                           {`zeromq`},
	`Apache Pulsar`:                    {`pulsar`},
	`Docker`:                           {`docker`},
	`containerd`:                       {`containerd`},
	`Kubernetes`:                       {`kubernetes`},
	`CRI-O`:                            {`cri[-_\s]*o`},
	`Podman`:                           {`podman`},
	`Helm`:                             {`helm`},
	`Istio`:                            {`istio`},
	`Linkerd`:                          {`linkerd`},
	`OpenShift`:                        {`openshift`},
	`kube-proxy`:                       {`kube-proxy`},
	`coredns`:                          {`coredns`},
	`etcd`:                             {`\betcd\b`},
	`Python`:                           {`python`},
	`Java (JRE/JDK)`:                   {`\bjava\b`},
	`Golang`:                           {`\bgo\b`},
	`Rust`:                             {`\brust\b`},
	`Ruby`:                             {`\bruby\b`},
	`Gradle`:                           {`gradle`},
	`Maven`:                            {`maven`},
	`sbt`:                              {`\bsbt\b`},
	`Keycloak`:                         {`keycloak`},
	`Dex`:                              {`\bdex\b`},
	`HashiCorp Vault`:                  {`hashicorp[-_\s]*vault`},
	`Grafana`:                          {`grafana`},
	`Loki`:                             {`\bloki\b`},
	`Fluentd`:                          {`fluentd`},
	`Fluent Bit`:                       {`fluent[-_\s]*bit`},
	`OpenTelemetry Collector`:          {`opentelemetry`},
	`Jaeger`:                           {`jaeger`},
	`Zipkin`:                           {`zipkin`},
	`Logstash`:                         {`logstash`},
	`Kibana`:                           {`kibana`},
	`Envoy`:                            {`envoy`},
	`HAProxy`:                          {`haproxy`},
	`Traefik`:                          {`traefik`},
	`Kong`:                             {`kong`},
	`Ambassador`:                       {`ambassador`},
	`Trivy`:                            {`trivy`},
	`Clair`:                            {`clair`},
	`Anchore Engine`:                   {`anchore[-_\s]*engine`},
	`Aqua`:                             {`aqua`},
	`Snyk CLI`:                         {`snyk[-_\s]*cli`},
	`Falco`:                            {`falco`},
	`OpenSCAP`:                         {`openscap`},
	`Gunicorn`:                         {`gunicorn`},
	`uWSGI`:                            {`uwsgi`},
	`Fastify`:                          {`fastify`},
	`Django`:                           {`django`},
	`Flask`:                            {`flask`},
	`Spring Boot`:                      {`spring[-_\s]*boot`},
	`Celery`:                           {`celery`},
	`Airflow`:                          {`airflow`},
	`Temporal`:                         {`temporal`},
	`Apache NiFi`:                      {`apache nifi`},
	`Prefect`:                          {`prefect`},
	`BullMQ`:                           {`bullmq`},
	`Sidekiq`:                          {`sidekiq`},
	`Quartz Scheduler`:                 {`quartz scheduler`},
	`Memcached`:                        {`memcached`},
	`Varnish`:                          {`varnish`},
	`Ehcache`:                          {`ehcache`},
	`Caffeine`:                         {`caffeine`},
	`Cilium`:                           {`cilium`},
	`Calico`:                           {`calico`},
	`Flannel`:                          {`flannel`},
	`kube-router`:                      {`kube[-_\s]*router`},
	`TensorFlow Serving`:               {`tensorflow`},
	`TorchServe`:                       {`torch[-_\s]*serve`},
	`ONNX Runtime`:                     {`onnx[-_\s]*runtime`},
	`HuggingFace Transformers library`: {`huggingface`},
	`MLflow`:                           {`mlflow`},
	`JUnit`:                            {`junit`},
	`pytest`:                           {`pytest`},
	`Mocha`:                            {`mocha`},
	`Jasmine`:                          {`jasmine`},
	`TestNG`:                           {`testng`},
	`Cypress`:                          {`cypress`},
	`Selenium`:                         {`selenium`},
	`Playwright`:                       {`playwright`},
	`Netperf`:                          {`netperf`},
	`Iperf`:                            {`iperf`},
	`WANPerf`:                          {`wanperf`},
	`CFProxy`:                          {`cfproxy`},
}

var DeploymentKeyOrValues = map[string][]string{
	TERRAFORM_DEPLOYMENT: {`terraform`, `\btf\b`, `terragrunt`, `azdotf`},
	CFT_DEPLOYMENT:       {`cloudformation`, `\bcft\b`},
	KUBERNETES_DEPLOYMENT: {`kubernetes`, `\bk8\b`, `\bk8s\b`, `kubectl`, `kube[-_\s]*config`, `\bhelm\b`, `kubelet`,
		`kubernetes[-_\s]*java[-_\s]*client`, `googlecontainerengine`, `google[-_\s]*connect[-_\s]*gateway`,
		`google[-_\s]*gkehub[-_\s]*controllers`, `google[-_\s]*backup[-_\s]*for[-_\s]*gke`,
		`googlegkegatewaycontroller`, `eks\.amazonaws\.com`, `amazon[-_\s]*vpc[-_\s]*cni`, `elbv2\.k8s\.aws`,
		`eks[-_\s]*nodegroup\.amazonaws\.com`, `otelcol[-_\s]*contrib`},
	DOCKER_DEPLOYMENT:    {`docker`},
	AZDO_DEPLOYMENT:      {`\bazdo\b`, `azure[-_\s]*devops`, `azdotf`},
	ARGO_DEPLOYMENT:      {`\bargo(?:cd)?\b`},
	JENKINS_DEPLOYMENT:   {`jenkins`},
	GITHUB_DEPLOYMENT:    {`github`},
	GITLAB_DEPLOYMENT:    {`gitlab`},
	BITBUCKET_DEPLOYMENT: {`bitbucket`},
	CLI_DEPLOYMENT: {`aws[-_\s]*cli`, `command[-_\s]*line`, `azure[-_\s]*cli`, `\bgcloud\b`, `gsutil`,
		`google[-_\s]*cloud[-_\s]*sdk`, `apitools`, `curl`, `postmanruntime`, `firebasecli`},
	CONSOLE_DEPLOYMENT: {`aws[-_\s]*console`, `gcp[-_\s]*console`, `azure[-_\s]*portal`, `mozilla`, `chrome`,
		`safari`, `webkit`, `applewebkit`, `khtml`, `gecko`, `edg`, `s3console`, `firefox`, `opr`,
		`android.*cloudconsole`, `cloudcode[-_\s]*vscode`},
	AWS_DEPLOYMENT: {`boto3`, `aws[-_\s]*sdk`, `aws[-_\s]*service`, `aiobotocore`, `lambda`, `awslambda[-_\s]*worker`,
		`\.amazonaws\.com`, `awsstepfunctions`, `amazoneventbridgescheduler`, `patchmanager`, `cwagent`,
		`aws[-_\s]*internal`, `aws[-_\s]*sdk[-_\s]*dotnet[-_\s]*coreclr`, `aws[-_\s]*sdk[-_\s]*nodejs`,
		`botocore`, `aws[-_\s]*fluent[-_\s]*bit[-_\s]*plugin`, `aws[-_\s]*sdk[-_\s]*go`,
		`securityhub\.amazonaws\.com`, `autoscaling\.amazonaws\.com`, `datazone\.amazonaws\.com`,
		`internetmonitor\.amazonaws\.com`, `application[-_\s]*insights\.amazonaws\.com`,
		`organizations\.amazonaws\.com`, `datasync\.amazonaws\.com`, `lambda\.amazonaws\.com`,
		`compute[-_\s]*optimizer\.amazonaws\.com`, `events\.amazonaws\.com`, `acm\.amazonaws\.com`,
		`aoss\.amazonaws\.com`, `member\.org\.stacksets\.cloudformation\.amazonaws\.com`,
		`vmie\.amazonaws\.com`, `osis\.amazonaws\.com`, `servicediscovery\.amazonaws\.com`,
		`stacksets\.cloudformation\.amazonaws\.com`, `aws[-_\s]*toolkit[-_\s]*for[-_\s]*jetbrains`},
	GCP_DEPLOYMENT: {`google[-_\s]*api[-_\s]*go[-_\s]*client`, `google[-_\s]*api[-_\s]*python[-_\s]*client`,
		`google[-_\s]*api[-_\s]*java[-_\s]*client`, `gcloud[-_\s]*golang`, `gcloud[-_\s]*python`, `gcloud[-_\s]*java`,
		`gcloud[-_\s]*node`, `google[-_\s]*deployment[-_\s]*manager`, `googlecloudworkflows`,
		`bigquery[-_\s]*data[-_\s]*transfer[-_\s]*service`, `cloud[-_\s]*composer`, `gce[-_\s]*managed[-_\s]*instance[-_\s]*group`,
		`gce[-_\s]*csi[-_\s]*driver`, `google[-_\s]*cloud[-_\s]*dataproc[-_\s]*server`, `stubby[-_\s]*client`,
		`boq[-_\s]*goa[-_\s]*gce`, `app[-_\s]*engine`, `gce[-_\s]*managed[-_\s]*instance[-_\s]*group[-_\s]*for[-_\s]*appengine`,
		`gce[-_\s]*managed[-_\s]*instance[-_\s]*group[-_\s]*for[-_\s]*dataflow`, `gce[-_\s]*managed[-_\s]*instance[-_\s]*group[-_\s]*for[-_\s]*tesseract`,
		`cloud[-_\s]*workflow[-_\s]*service`, `boq[-_\s]*cloud[-_\s]*services[-_\s]*platform[-_\s]*gcesync`,
		`velostrata`, `tesseract`, `google[-_\s]*hybrid[-_\s]*cloud[-_\s]*storage`, `bigstorefile`,
		`grpc[-_\s]*python`, `grpc[-_\s]*c`, `grpc[-_\s]*java[-_\s]*cronet`, `grpc[-_\s]*c\+\+`,
		`loaddata.*bigtable`, `transferservice`, `workflow[-_\s]*service`, `coral/netty`,
		`boq[-_\s]*cloud[-_\s]*aiplatform[-_\s]*pipeline`, `gl[-_\s]*cpp.*dremel`, `python[-_\s]*urllib3`},
	APPLICATION_DEPLOYMENT: {`golang[-_\s]*app`, `go[-_\s]*http[-_\s]*client`, `grpc[-_\s]*go`, `java[-_\s]*sdk`,
		`spring`, `python[-_\s]*app`, `tornado`, `airflow`, `nodejs[-_\s]*app`, `node[-_\s]*fetch`,
		`databricks[-_\s]*api`, `axios`},
	INFRASTRUCTURE_TOOLS_DEPLOYMENT: {`packer`, `blob`, `\bm/[ef]\b`, `\bapn\b`, `\bfog\b`},
}

var UserAgentKeyOrValues = map[string][]string{
	AWS_SDK_JAVA_USER_AGENT:           {`\baws-sdk-java\b`},
	AWS_SDK_GO_USER_AGENT:             {`\baws-sdk-go\b`},
	AWS_CLI_USER_AGENT:                {`\baws-cli\b`},
	AWS_BOTO3_USER_AGENT:              {`\bboto3\b`},
	AWS_SERVICE_USER_AGENT:            {`amazonaws\.com`, `\baws\s*service\b`},
	CLOUDFORMATION_USER_AGENT:         {`\bcloudformation\b`, `cloudformation`, `\bcft\b`},
	KUBERNETES_DEPLOYMENT_USER_AGENT:  {`kubernetes`, `\bk8\b`, `kubectl`, `kube[-_\s]*config`, `kube[-_\s]*config`, `\bhelm\b`},
	TERRAFORM_USER_AGENT:              {`\bterraform\b`, `\bterragrunt\b`},
	GOLANG_APP_USER_AGENT:             {`\bgolang\s*app\b`, `\bgo\b`},
	JAVA_SDK_USER_AGENT:               {`\bjava\s*sdk\b`, `\bjersey\b`},
	MOZILLA_USER_AGENT:                {`\bmozilla\b`},
	CLOUDWATCH_AGENT_USER_AGENT:       {`\bcwagent\b`, `\bcfntools\b`},
	EVENT_BRIDGE_SCHEDULER_USER_AGENT: {`\bamazonEventBridgeScheduler\b`},
	S3_CONSOLE_USER_AGENT:             {`\bs3console\b`},
	AWS_CONSOLE_USER_AGENT:            {`\baws\s*console\b`},
}

var ComplianceEnumToValue = map[string]string{
	`HIPAA_COMPLIANCE`:  `HIPAA`,
	`GDPR_COMPLIANCE`:   `GDPR`,
	`PCIDSS_COMPLIANCE`: `PCIDSS`,
	`PCI_COMPLIANCE`:    `PCI`,
	`NIST_COMPLIANCE`:   `NIST`,
	`SOX_COMPLIANCE `:   `SOX`,
	`FISMA_COMPLIANCE`:  `FISMA`,
	`ISO_COMPLIANCE`:    `ISO`,
	`FERPA_COMPLIANCE`:  `FERPA`,
	`GLBA_COMPLIANCE`:   `GLBA`,
	`CJIS_COMPLIANCE`:   `CJIS`,
	`PII_COMPLIANCE`:    `PII`,
}

var ComplianceKeyOrValues = map[string]string{
	HIPAA_COMPLIANCE:  `\bhipaa\b`,
	GDPR_COMPLIANCE:   `\bgdpr\b`,
	PCIDSS_COMPLIANCE: `pci[-_\s]*dss`,
	NIST_COMPLIANCE:   `\bnist\b`,
	SOX_COMPLIANCE:    `\bsox\b`,
	FISMA_COMPLIANCE:  `\bfisma\b`,
	ISO_COMPLIANCE:    `\biso\b`,
	FERPA_COMPLIANCE:  `\bferpa\b`,
	GLBA_COMPLIANCE:   `\bglba\b`,
	CJIS_COMPLIANCE:   `\bcjis\b`,
}

var SensitivityKeyOrValues = map[string]string{
	CONFIDENTIAL_SENSITIVITY: `confidential`,
	RESTRICTED_SENSITIVITY:   `restricted`,
	PUBLIC_SENSITIVITY:       `^public$`,
	INTERNAL_SENSITIVITY:     `^internal$`,
	PCI_SENSITIVITY:          `\bpci\b`,
	PII_SENSITIVITY:          `\bpii\b`,
	PHI_SENSITIVITY:          `\bphi\b`,
}

var PortToSoftware = map[int]string{
	80:    SERVER_SOFTWARE,
	443:   SERVER_SOFTWARE,
	3306:  SQL_SOFTWARE, // MySQL
	1433:  SQL_SOFTWARE, // MsSQL
	9200:  ELASTIC_SOFTWARE,
	27017: MONGO_SOFTWARE,
	5432:  POSTGRES_SOFTWARE,
	1521:  ORACLE_SOFTWARE,
	6379:  REDIS_SOFTWARE,
	9042:  CASSANDRA_SOFTWARE,
	7687:  NEO4J_SOFTWARE,
}

var ExcludedDepartments = map[string]struct{}{
	`call center`:                    {},
	`bpo`:                            {},
	`support`:                        {},
	`temporary`:                      {},
	`contract`:                       {},
	`sports group`:                   {},
	`call center - monterrey`:        {},
	`american college of cardiology`: {},
	`human resources`:                {},
	`inside sales - anup patnaik`:    {},
	`finance and accounts`:           {},
	`human resource`:                 {},
	`financial crimes security23002`: {},
	`fin ops 13091`:                  {},
	`hr - tag`:                       {},
	`financial operatns - opm 13090`: {},
	`inside sales 31090`:             {},
	`spencer’s gift`:                 {},
	`f&o`:                            {},
	`people talent 44020`:            {},
	`salesforce practice`:            {},
	`marketing 32010`:                {},
	`corp apps finance 22000`:        {},
	`travel desk`:                    {},
	`call center - chsa/cobra 11095`: {},
	`us recruitment`:                 {},
	`custodial operations 13002`:     {},
	`accounting`:                     {},
	`people development 44030`:       {},
	`transfer pricing`:               {},
	`support practice-hr`:            {},
	`call center - cary`:             {},
	`delivery multiple`:              {},
	`legal 43000`:                    {},
	`test`:                           {},
	`support services`:               {},
	`internal - customer success`:    {},
	`call center - manila`:           {},
	`support services 14002`:         {},
	`claims - non classic`:           {},
	`american auto shield`:           {},
	`call center - kochi`:            {},
	`claims classic 12041`:           {},
	`finance operations - 42015`:     {},
	`delivery`:                       {},
	`support function`:               {},
	`strategic client relationships & advisory services`: {},
	`corporate reporting 42014`:                          {},
	`corporate accounting 42010`:                         {},
	`executive 41000`:                                    {},
	`claims team - lexington, ky`:                        {},
	`marketing`:                                          {},
	`fe credit`:                                          {},
	`call center - raleigh`:                              {},
	`up! app`:                                            {},
	`property management`:                                {},
	`pop service delivery 13070`:                         {},
	`service support`:                                    {},
	`internal audit 42020`:                               {},
	`ares health systems`:                                {},
	`call center - lexington`:                            {},
	`medical solutions`:                                  {},
	`delivery practice`:                                  {},
	`call center - chesapeake`:                           {},
	`boch family foundation`:                             {},
	`people 44010`:                                       {},
	`finance`:                                            {},
	`sound exchange`:                                     {},
	`customer service`:                                   {},
	`management`:                                         {},
	`external`:                                           {},
	`bi & reporting`:                                     {},
	`workplace`:                                          {},
	`demand`:                                             {},
	`carrier supply`:                                     {},
	`people ops`:                                         {},
	`general`:                                            {},
	`finance & legal`:                                    {},
	`business insights & enablement`:                     {},
	`commercial`:                                         {},
	`ods legal`:                                          {},
	`people`:                                             {},
	`business apac`:                                      {},
	`business ops`:                                       {},
	`content media`:                                      {},
	`noc team`:                                           {},
	`reporting`:                                          {},
	`revenue`:                                            {},
	`total rewards`:                                      {},
	`sales`:                                              {},
	`senior revenue accountant`:                          {},
	`accountant`:                                         {},
}

var TechKeywords = []string{
	`engineer`, `developer`, `devops`, `operations`, `architect`, `sre`,
	`infra`, `infrastructure`, `technologist`, `platform`, `cloud`, `data`,
	`system`, `systems`, `network`, `it`, `cyber`, `security`, `product`,
}

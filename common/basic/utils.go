package basicutils

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"io"
	"strings"

	"github.com/precize/logger"
)

func DecodeBase64Recursive(s string) string {
	decoded, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to Decode Base64 string")
		return ""
	}
	result := decoded

	if IsBase64(string(result)) {
		if secondDecoded, err := base64.StdEncoding.DecodeString(string(result)); err == nil {
			result = secondDecoded
		}
	}

	if IsGzipped(decoded) {
		reader, err := gzip.NewReader(bytes.NewReader(decoded))
		if err != nil {
			logger.Print(logger.ERROR, "Failed to create Gzip reader")
			return ""
		}
		defer reader.Close()

		var result bytes.Buffer
		if _, err := io.Copy(&result, reader); err != nil {
			logger.Print(logger.ERROR, "Failed to decompress Gzip data")
			return ""
		}
		return result.String()
	}

	return string(result)
}

func IsBase64(str string) bool {
	str = strings.TrimSpace(str)

	if len(str)%4 != 0 {
		return false
	}

	_, err := base64.StdEncoding.DecodeString(str)
	return err == nil
}

func IsGzipped(data []byte) bool {
	return len(data) > 2 && data[0] == 0x1F && data[1] == 0x8B
}

package emailutils

import (
	"strings"
)

// <EMAIL>, <EMAIL> -> true
func EmailsTLDDiff(email1, email2 string) bool {
	parts1 := strings.Split(email1, "@")
	parts2 := strings.Split(email2, "@")

	if len(parts1) != 2 || len(parts2) != 2 {
		return false
	}

	domain1 := strings.Split(parts1[1], ".")
	domain2 := strings.Split(parts2[1], ".")

	if len(domain1) < 2 || len(domain2) < 2 {
		return false
	}

	return parts1[0] == parts2[0] &&
		strings.Join(domain1[:len(domain1)-1], ".") ==
			strings.Join(domain2[:len(domain2)-1], ".")
}

func GetDomainFromEmail(email string) string {
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return ""
	}
	return parts[1]
}

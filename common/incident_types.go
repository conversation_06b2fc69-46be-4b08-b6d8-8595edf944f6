package common

const (
	DEFENDER_SOURCE                = "Azure Defender"
	ORCA_SOURCE                    = "Orca"
	SECURITYHUB_SOURCE             = "AWS SecurityHub"
	SECURITY_COMMAND_CENTER_SOURCE = "GCP Security Command Center"
	WIZ_SOURCE                     = "wiz"
	OKTA_SOURCE                    = "Okta"

	INCIDENT_STATUS_OPEN     = "open"
	INCIDENT_STATUS_RESOLVED = "resolved"
)

// Constants for risk levels
const (
	LOW_RISK      = "Low"
	MEDIUM_RISK   = "Medium"
	MODERATE_RISK = "Moderate"
	HIGH_RISK     = "High"
	CRITICAL_RISK = "Critical"

	NOTEVALUATED_RISK  = "Not Evaluated"
	NONE_RISK          = "None"
	INFORMATIONAL_RISK = "Informational"
)

// Constants for likelihood levels
const (
	VERY_UNLIKELY_LIKELIHOOD = "Very Unlikely"
	UNLIKELY_LIKELIHOOD      = "Unlikely"
	POSSIBLE_LIKELIHOOD      = "Possible"
	LIKELY_LIKELIHOOD        = "Likely"
	VERY_LIKELY_LIKELIHOOD   = "Very Likely"
)

// Constants for impact levels
const (
	NEGLIGIBLE_IMPACT  = "Negligible"
	MINOR_IMPACT       = "Minor"
	MODERATE_IMPACT    = "Moderate"
	SIGNIFICANT_IMPACT = "Significant"
	SEVERE_IMPACT      = "Severe"
)

type Incident struct {
	ID                 string   `json:"id"`
	AlertID            string   `json:"alertId"`
	Issue              string   `json:"issue"`
	AccountID          string   `json:"accountId"`
	AccountName        string   `json:"accountName"`
	EntityID           string   `json:"entityId"`
	EntityType         string   `json:"entityType"`
	ResourceName       string   `json:"resourceName"`
	Source             string   `json:"source"`
	IssueSeverity      string   `json:"issueSeverity"`
	IssueSeverityScore float32  `json:"issueSeverityScore"`
	SourceRisk         string   `json:"sourceRisk"`
	SourceScore        float32  `json:"sourceScore"`
	PrecizeRisk        string   `json:"precizeRisk"`
	SeverityDiff       int      `json:"severityDiff"`
	CreatedAt          string   `json:"createdAt"`
	UpdatedAt          string   `json:"updatedAt"`
	CrsID              string   `json:"crsId"`
	ServiceID          int      `json:"serviceId"`
	Category           string   `json:"category"`
	Description        string   `json:"description"`
	Status             string   `json:"status"`
	Stage              string   `json:"stage"`
	Owner              []string `json:"owner"`
	Environment        []string `json:"environment"`
	TenantID           string   `json:"tenantId"`
	SourceJson         string   `json:"sourceJson"`
	InsertTime         string   `json:"insertTime"`
	AdditionalData     string   `json:"additionalData"`
	PriorityCriteria   []string `json:"priorityCriteria"`
	PrecizeScore       float32  `json:"precizeScore"`
	IsIncident         bool     `json:"isIncident"`
	Type               string   `json:"type"`
	CRSLookUpCount     int      `json:"crsLookUpCount"`
}

type RecommendationFindings struct {
	ID          string  `json:"id"`
	Description string  `json:"description"`
	Impact      string  `json:"impact"`
	Score       float32 `json:"score"`
	Severity    string  `json:"severity"`
}

var ValidRiskLevel = map[string]struct{}{
	LOW_RISK:      {},
	MEDIUM_RISK:   {},
	MODERATE_RISK: {},
	HIGH_RISK:     {},
	CRITICAL_RISK: {},
	NONE_RISK:     {},
}

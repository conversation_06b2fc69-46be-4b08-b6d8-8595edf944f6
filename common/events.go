package common

type EventsDoc struct {
	TenantID                    string          `json:"tenantId"`
	AccountID                   string          `json:"accountId"`
	ServiceCode                 string          `json:"serviceCode"`
	EventID                     string          `json:"eventId"`
	EventName                   string          `json:"eventName"`
	ReadOnly                    bool            `json:"readOnly"`
	AccessKeyID                 string          `json:"accessKeyId"`
	EventTime                   string          `json:"eventTime"`
	EventSource                 string          `json:"eventSource"`
	Username                    string          `json:"username"`
	Resources                   []EventResource `json:"resources"`
	Region                      string          `json:"region"`
	CloudTrailEvent             string          `json:"cloudTrailEvent"`
	SourceApp                   string          `json:"sourceApp"`
	IsUpdateEvent               bool            `json:"isUpdateEvent"`
	AccessKeyPrincipalID        string          `json:"accessKeyPrincipalId"`
	AccessKeyPrincipalType      string          `json:"accessKeyPrincipalType"`
	AccessKeyPrincipalAccountID string          `json:"accessKeyPrincipalAccountId"`
	SiUsername                  string          `json:"siUsername"`
	SourceIP                    string          `json:"sourceIp"`
	EventStatus                 string          `json:"eventStatus"`
}

type EventResource struct {
	ResourceType           string `json:"resourceType"`
	ResourceName           string `json:"resourceName"`
	UnModifiedResourceName string `json:"unmodifiedResourceName"`
}

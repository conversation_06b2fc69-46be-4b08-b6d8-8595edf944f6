package common

import (
	"fmt"
	"strconv"

	"github.com/precize/elastic"
	"github.com/precize/logger"
)

func GetServiceID(tenantID, lastCollectedAt string) (serviceID string) {

	serviceIDQuery := `{"_source":["serviceId"],"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"term":{"collectedAt":` + lastCollectedAt + `}}]}},"size":1}`

	serviceIDDocs, err := elastic.ExecuteSearchQueryWithoutPagination([]string{elastic.CLOUD_RESOURCES_INDEX}, serviceIDQuery)
	if err != nil {
		return
	}

	for _, serviceIDDoc := range serviceIDDocs {

		if serviceIDFloat, ok := serviceIDDoc["serviceId"].(float64); ok {
			serviceID = strconv.FormatFloat(serviceIDFloat, 'f', 0, 64)
			return
		}
	}

	return
}

func GetPreviousCollectedAt(tenantID, lastCollectedAt, serviceID string) (previousCollectedAt string) {

	previousCollectedQuery := `{"_source":["collectedAt"],"sort":[{"collectedAt":{"order":"desc"}}],"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"term":{"serviceId":` + serviceID + `}},{"term":{"scanType":0}},{"term":{"status":2}},{"range":{"collectedAt":{"lt":` + lastCollectedAt + `}}}]}},"size":1}`

	previousCollectedDocs, err := elastic.ExecuteSearchQueryWithoutPagination([]string{elastic.SCANS_INDEX}, previousCollectedQuery)
	if err != nil {
		return
	}

	for _, previousCollectedDoc := range previousCollectedDocs {
		if previousCollectedAtFloat, ok := previousCollectedDoc["collectedAt"].(float64); ok {
			previousCollectedAtInt := int64(previousCollectedAtFloat)
			previousCollectedAt = fmt.Sprintf("%d", previousCollectedAtInt)
		}
	}

	logger.Print(logger.INFO, "Previous collected at", previousCollectedAt)

	return
}

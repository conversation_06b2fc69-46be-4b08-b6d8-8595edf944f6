package common

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"math/big"
	"os"
	"os/exec"
	"path/filepath"
	"reflect"
	"regexp"
	"slices"
	"strconv"
	"strings"
	"time"

	jsoniter "github.com/json-iterator/go"
	"github.com/xanzy/go-gitlab"

	"github.com/google/uuid"
	"github.com/hashicorp/hcl/v2"
	"github.com/hashicorp/hcl/v2/hclsyntax"
	"github.com/hashicorp/hcl/v2/hclwrite"
	"github.com/zclconf/go-cty/cty"

	"github.com/precize/elastic"
	"github.com/precize/logger"
)

type ResourceBlock struct {
	StartingLineNumber int
	EndingLineNumber   int
	Name               string
	Label              string
	SyntaxBlock        *hclsyntax.Block
	IsDeleted          bool
	ResourceContent    string
}

type VariableFile struct {
	fileName string
	content  string
	filePath string
}

type TfCommitsIndex struct {
	CommitId                     string
	ResourceLabelName            string
	StaticResourceProperties     string
	DynamicResourceProperties    string
	Variables                    string
	ServiceCode                  string
	TenantID                     string
	GitClient                    string
	CommitTime                   string
	VariableModifiedCommitDocIds string
}

const ResourceBlockType = "resource"
const VariableBlockType = "variable"

var SupportedBlockTypes = []string{ResourceBlockType, VariableBlockType}

func refreshGitlabToken(token string) *gitlab.Client {
	gitlabClient, err := gitlab.NewOAuthClient(token)
	if err != nil {
		logger.Print(logger.ERROR, "Got error creating gitlab client", err)
		return nil
	} else {
		return gitlabClient
	}

}

func GitIdentity(email, username, tenantID string) string {

	//TODO: same should be done for bitbucket and gitlab
	if strings.HasSuffix(email, "@users.noreply.github.com") {

		// Github private email usecase
		splitPlusSign := strings.Split(email, "+")
		if len(splitPlusSign) > 1 {
			splitEmailsign := strings.Split(splitPlusSign[1], "@")
			if len(splitEmailsign) > 0 {
				username = splitEmailsign[0]

				query := `{
					"query": {
					  "bool": {
						"must": [
						  {
							"match": {
							  "author.keyword": "` + username + `"
							}
						  },
						  {
							"match": {
							  "tenantId.keyword": "` + tenantID + `"
							}
						  }
						],
						"must_not": [
						  {
							"wildcard": {
							  "authorEmail.keyword": "*users.noreply.github.com*"
							}
						  }
						],
						"should": []
					  }
					},
					"size": 1
				  }`

				iacDocs, err := elastic.ExecuteSearchQueryWithoutPagination([]string{elastic.IAC_GIT_COMMITS_INDEX}, query)
				if err == nil {
					if len(iacDocs) > 0 {
						for _, iacDoc := range iacDocs {

							if authorEmail, ok := iacDoc["authorEmail"].(string); ok {
								return authorEmail
							}
						}
					} else {
						return username
					}
				}
			}
		}
	}

	if len(email) > 0 && strings.Contains(email, "@") {
		return email
	}

	return username
}

func GetFileGitHash(fileContent []byte) (gitHash string, err error) {

	tempFile := "/tmp/precize_tmp_" + uuid.New().String()

	if err = os.WriteFile(tempFile, fileContent, 0644); err != nil {
		return
	}

	gitHashResponse, err := exec.Command("git", "hash-object", tempFile).CombinedOutput()
	if err != nil {
		return
	}

	gitHash = strings.TrimSuffix(string(gitHashResponse), "\n")

	os.Remove(tempFile)

	return
}

func FetchGitFileAuthorsForTerraform(tenantID, commitID, filename, resourceID, csp string) (gitUsers []GitUser, err error) {

	var resourceLastCommitTime = elastic.DateTime(time.Time{})

	resourceLastUserEventQuery := `{"_source":["eventTime","commitDocId"],"sort":[{"eventTime":{"order":"desc"}}],"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"resourceId.keyword":"` + resourceID + `"}},{"match":{"derivedFrom":"yor"}},{"terms":{"userType.keyword":["gitlab","github","bitbucket"]}}]}},"size":1}`

	resourceLastUserEventDocs, err := elastic.ExecuteSearchQueryWithoutPagination([]string{elastic.RESOURCE_USER_EVENTS_INDEX}, resourceLastUserEventQuery)
	if err != nil {
		return
	}

	for _, resourceLastUserEventDoc := range resourceLastUserEventDocs {

		if eventTime, ok := resourceLastUserEventDoc["eventTime"].(string); ok {

			resourceLastCommitTime = eventTime

			if len(filename) <= 0 {
				// AWS events give only the tags that got modified, not all tags all the time

				if commitDocId, ok := resourceLastUserEventDoc["commitDocId"].(string); ok {

					resourceLastCommitDoc, err := elastic.GetDocument(elastic.IAC_GIT_COMMITS_INDEX, commitDocId)
					if err != nil {
						return nil, err
					}

					if len(resourceLastCommitDoc) > 0 {
						filename, _ = resourceLastCommitDoc["filename"].(string)
					}
				}
			}

			break
		}
	}

	gitCommitSearchQuery := `{"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"commitId.keyword":"` + commitID + `"}}]}}}`

	initialCommitDocs, err := elastic.ExecuteSearchQuery([]string{elastic.IAC_GIT_COMMITS_INDEX}, gitCommitSearchQuery)
	if err != nil {
		return
	}

	for _, initialCommitDoc := range initialCommitDocs {

		if repoName, ok := initialCommitDoc["repoName"].(string); ok {

			if respFilename, ok := initialCommitDoc["filename"].(string); ok {

				if (respFilename == filename) || (csp == GCP_SERVICE_CODE && filename == ModifyGCPLabelToHandleConstraints(respFilename)) {

					if commitTime, ok := initialCommitDoc["commitTime"].(string); ok {

						gitUsersQuery := `{"sort":[{"commitTime":{"order":"desc"}}],"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"filename.keyword":"` + respFilename + `"}},{"match":{"repoName.keyword":"` + repoName + `"}},{"range":{"commitTime":{"gt":"` + resourceLastCommitTime + `","lte":"` + commitTime + `"}}}]}},"size":1}`

						commitDocs, err := elastic.ExecuteSearchQuery([]string{elastic.IAC_GIT_COMMITS_INDEX}, gitUsersQuery)
						if err != nil {
							continue
						}

						gitUsers = append(gitUsers, fetchGitUsersFromCommitDocs(commitDocs, tenantID)...)
					}
				}
			}
		}
	}

	return
}

func FetchGitFileAuthorsWithGitHash(tenantID, templateGitHash string) (gitUsers []GitUser, err error) {

	gitHashSearchQuery := `{"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"gitFileHash.keyword":"` + templateGitHash + `"}}]}}}`

	commitDocs, err := elastic.ExecuteSearchQuery([]string{elastic.IAC_GIT_COMMITS_INDEX}, gitHashSearchQuery)
	if err != nil {
		return
	}

	gitUsers = fetchGitUsersFromCommitDocs(commitDocs, tenantID)

	return
}

func FetchGitFileAuthorsWithMinifiedHash(tenantID, templateMinifizedJSONHash string) (gitUsers []GitUser, err error) {

	minifiedHashSearchQuery := `{"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"minifiedJSONHash.keyword":"` + templateMinifizedJSONHash + `"}}]}}}`

	commitDocs, err := elastic.ExecuteSearchQuery([]string{elastic.IAC_GIT_COMMITS_INDEX}, minifiedHashSearchQuery)
	if err != nil {
		return
	}

	gitUsers = fetchGitUsersFromCommitDocs(commitDocs, tenantID)

	return
}

func fetchGitUsersFromCommitDocs(commitDocs []map[string]any, tenantID string) (gitUsers []GitUser) {

	for _, commitDoc := range commitDocs {

		var gitUser GitUser
		gitUser.Action = "modified"

		authorEmail, _ := commitDoc["authorEmail"].(string)
		author, _ := commitDoc["author"].(string)

		if authorEmail == "<EMAIL>" || authorEmail == "<EMAIL>" {
			continue
		}

		gitUser.Name = GitIdentity(authorEmail, author, tenantID)

		if gitClient, ok := commitDoc["gitClient"].(string); ok {
			gitUser.Client = gitClient
		}

		if fileStatus, ok := commitDoc["fileStatus"].(string); ok {
			gitUser.Action = fileStatus
		}

		if commitTime, ok := commitDoc["commitTime"].(string); ok {
			gitUser.CommitTime = commitTime
		}

		if docID, ok := commitDoc["_id"].(string); ok {
			gitUser.DocID = docID
		}

		gitUsers = append(gitUsers, gitUser)
	}

	return
}

func CommitCollected(tenantID, commitID string) (collected bool) {

	commitIdQuery := `{"_source":["commitId"],"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"commitId.keyword":"` + commitID + `"}}]}}}`

	commitDocs, err := elastic.ExecuteSearchQuery([]string{elastic.IAC_GIT_COMMITS_INDEX}, commitIdQuery)
	if err != nil {
		return
	}

	if len(commitDocs) > 0 {
		collected = true
	}

	return
}

func MapCommitToTerraformResources(tenantID, commitID, filePath string, gitUser GitUser) (err error) {

	tfQuery := `{"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"commitId.keyword":"` + commitID + `"}}]}}}`

	tfDocs, err := elastic.ExecuteSearchQuery([]string{elastic.TERRAFORM_RESOURCES_INDEX}, tfQuery)
	if err != nil {
		return
	}

	for _, tfDoc := range tfDocs {

		if resourceID, ok := tfDoc["resourceId"].(string); ok {

			if respFilename, ok := tfDoc["filename"].(string); ok {

				csp, _ := tfDoc["csp"].(string)

				if (respFilename == filePath) || (csp == GCP_SERVICE_CODE && ModifyGCPLabelToHandleConstraints(filePath) == respFilename) {

					var resourceEvent = ResourceEvent{
						ResourceID:   resourceID,
						ResourceName: tfDoc["resourceName"].(string),
						ResourceType: tfDoc["resourceType"].(string),
						Region:       tfDoc["region"].(string),
						Account:      tfDoc["account"].(string),
					}

					if _, err = elastic.InsertDocument(tenantID, elastic.RESOURCE_USER_EVENTS_INDEX, ResourceUserEventDoc{
						ResourceEvent: resourceEvent,
						Action:        gitUser.Action,
						TenantID:      tenantID,
						User:          gitUser.Name,
						UserType:      gitUser.Client,
						EventTime:     gitUser.CommitTime,
						DocID:         gitUser.DocID,
					}); err != nil {
						continue
					}
				}

			}
		}
	}

	return
}

func MapCommitToCFTResources(tenantID, gitFileHash string, gitUser GitUser) (err error) {

	cftQuery := `{"_source":["resourceEvents","region","account"],"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"gitFileHash.keyword":"` + gitFileHash + `"}}]}}}`

	cftDocs, err := elastic.ExecuteSearchQuery([]string{elastic.CFSTACK_TEMPLATES_INDEX}, cftQuery)
	if err != nil {
		return
	}

	for _, cftDoc := range cftDocs {

		if resourceEventsJSON, ok := cftDoc["resourceEvents"].(string); ok {

			var resourceEvents []ResourceEvent
			if err = json.Unmarshal([]byte(resourceEventsJSON), &resourceEvents); err != nil {
				continue
			}

			for _, resourceEvent := range resourceEvents {

				resourceEvent.Region = cftDoc["region"].(string)
				resourceEvent.Account = cftDoc["account"].(string)

				if _, err = elastic.InsertDocument(tenantID, elastic.RESOURCE_USER_EVENTS_INDEX, ResourceUserEventDoc{
					ResourceEvent: resourceEvent,
					Action:        gitUser.Action,
					TenantID:      tenantID,
					User:          gitUser.Name,
					UserType:      gitUser.Client,
					EventTime:     gitUser.CommitTime,
					DocID:         gitUser.DocID,
				}); err != nil {
					continue
				}
			}
		}
	}

	return
}

func MapCommitToARMResources(tenantID, minifiedJSONHash string, gitUser GitUser) (err error) {

	armQuery := `{"_source":["resourceEvents","region","account"],"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"minifiedJSONHash.keyword":"` + minifiedJSONHash + `"}}]}}}`

	armDocs, err := elastic.ExecuteSearchQuery([]string{elastic.ARM_TEMPLATES_INDEX}, armQuery)
	if err != nil {
		return
	}

	for _, armDoc := range armDocs {

		if resourceEventsJSON, ok := armDoc["resourceEvents"].(string); ok {

			var resourceEvents []ResourceEvent
			if err = json.Unmarshal([]byte(resourceEventsJSON), &resourceEvents); err != nil {
				continue
			}

			for _, resourceEvent := range resourceEvents {

				resourceEvent.Region = armDoc["region"].(string)
				resourceEvent.Account = armDoc["account"].(string)

				if _, err = elastic.InsertDocument(tenantID, elastic.RESOURCE_USER_EVENTS_INDEX, ResourceUserEventDoc{
					ResourceEvent: resourceEvent,
					Action:        gitUser.Action,
					TenantID:      tenantID,
					User:          gitUser.Name,
					UserType:      gitUser.Client,
					EventTime:     gitUser.CommitTime,
					DocID:         gitUser.DocID,
				}); err != nil {
					continue
				}
			}
		}
	}

	return
}

func ProcessPreCommitEvents(tenantID, csp string, commitTime time.Time) (err error) {

	startTime := elastic.DateTime(commitTime)
	endTimeStr := elastic.DateTime(time.Now().UTC())

	logger.Print(logger.INFO, "Fetching Terraform events from "+startTime+" to "+endTimeStr, []string{tenantID})

	searchQuery := `{"query":{"bool":{"filter":[{"range":{"eventTime":{"gt":"` + startTime + `","lte":"` + endTimeStr + `"}}},{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"serviceCode.keyword":"` + csp + `"}},{"term":{"readOnly":"false"}}]}}}`
	tfEventDocs, err := elastic.ExecuteSearchQuery([]string{elastic.CLOUD_ACTIVITY_INDEX}, searchQuery)
	if err != nil {
		return
	}

	resourceIdToEventDocMap := make(map[string]ResourceEventCopy)
	resourceTypeToIDsMap := make(map[string][]string)
	uniqueResourceIds := make(map[string]struct{})

	for _, tfEventDoc := range tfEventDocs {

		if resourceInterfaces, ok := tfEventDoc["resources"].([]any); ok {
			for _, resourceInterface := range resourceInterfaces {

				if resourceMap, ok := resourceInterface.(map[string]any); ok {

					if resourceType, ok := resourceMap["resourceType"].(string); ok {

						resourceIDs := make([]string, 0)

						if resourceID, ok := resourceMap["resourceName"].(string); ok {
							if strings.Contains(resourceID, "[") && strings.Contains(resourceID, "]") {

								trimmedID := strings.Trim(resourceID, "[]")
								trimmedIDs := strings.Split(trimmedID, ",")

								resourceIDs = append(resourceIDs, trimmedIDs...)

							} else {
								resourceIDs = append(resourceIDs, resourceID)
							}
						}

						for _, resourceID := range resourceIDs {

							if cloudTrailEvent, ok := tfEventDoc["cloudTrailEvent"].(string); ok {

								if eventName, ok := tfEventDoc["eventName"].(string); ok {

									if serviceCode, ok := tfEventDoc["serviceCode"].(string); ok {

										if eventTimeStr, ok := tfEventDoc["eventTime"].(string); ok {

											if region, ok := tfEventDoc["region"].(string); ok {

												if accountID, ok := tfEventDoc["accountId"].(string); ok {

													if resourceGroup, ok := tfEventDoc["resourceGroup"].(string); ok {

														eventTime, err := elastic.ParseDateTime(eventTimeStr)
														if err != nil {
															logger.Print(logger.ERROR, "Error parse date time", eventTime)
															return err
														}

														if resourceType == "" || resourceID == "" {
															continue
														}

														if strings.Contains(strings.ToLower(eventName), "delete") {
															//TODO: Fetch From DB
															continue
														}

														eventResourceId := resourceID
														var cloudTrailEventMap map[string]any

														err = json.Unmarshal([]byte(cloudTrailEvent), &cloudTrailEventMap)
														if err != nil {
															logger.Print(logger.ERROR, "Error unmarshalling JSON", cloudTrailEvent, err)
															return err
														}

														if _, ok := uniqueResourceIds[resourceID]; !ok {
															uniqueResourceIds[resourceID] = struct{}{}
															if resourceIDs, ok := resourceTypeToIDsMap[resourceType]; !ok {
																ids := make([]string, 0)
																ids = append(ids, resourceID)
																resourceTypeToIDsMap[resourceType] = ids

															} else {
																resourceIDs = append(resourceIDs, resourceID)
																resourceTypeToIDsMap[resourceType] = resourceIDs
															}

															resourceIdToEventDocMap[resourceID] = ResourceEventCopy{
																ResourceMap:     cloudTrailEventMap,
																ResourceType:    resourceType,
																ResourceName:    resourceID,
																ServiceCode:     serviceCode,
																EventName:       eventName,
																EventTime:       eventTime,
																ResourceGroup:   resourceGroup,
																Region:          region,
																EventResourceID: eventResourceId,
																AccountID:       accountID,
															}
														}
													}
												}
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}

	_, err = MapEventToTfResources(resourceIdToEventDocMap, resourceTypeToIDsMap, csp, tenantID, "", commitTime)
	if err != nil {
		return err
	}

	return nil
}

func ProcessTerraformCommitResources(fileContent string, gitDiff string, docID string, tenantID string, commitTime string, gitClientName string, gitClient any) (string, error) {

	var (
		tfVariablesMapping      = make(map[string]any)
		tfToCSPResourceType     map[string]string
		tfResourcePropertiesMap = make(map[string]any)
		err                     error
	)

	repoName, filePath, branch := "", "", ""

	switch gitClientName {
	case GITHUB:
		if githubApiClient, ok := gitClient.(*GithubApiClient); ok {
			filePath = githubApiClient.FilePath
			repoName = githubApiClient.RepoName
			branch = githubApiClient.Branch
		}
	case BITBUCKET:
		if bitbucketApiClient, ok := gitClient.(*BitbucketApiClient); ok {
			filePath = bitbucketApiClient.FilePath
			repoName = bitbucketApiClient.RepoName
			branch = bitbucketApiClient.Branch
		}
	case GITLAB:
		if gitlabApiClient, ok := gitClient.(*GitlabApiClient); ok {
			filePath = gitlabApiClient.FilePath
			repoName = gitlabApiClient.RepoName
			branch = gitlabApiClient.Branch
		}
	}

	logger.Print(logger.INFO, "Starting tf Template Processing ", filePath, docID, branch, repoName)

	isVarFile, err := updateVariableMapsBasedOnFilePath(fileContent, gitClientName, gitClient, tfVariablesMapping, gitDiff, docID)
	if err != nil {
		return "", err
	}

	if isVarFile {
		err := fetchTFCommitsFromVarFile(gitClientName, tenantID, repoName, tfVariablesMapping, docID, commitTime)
		if err != nil {
			return "", err
		} else {
			return "", nil
		}
	}

	isCommitsUpdated, err := updateCommitsUsingExtFiles(gitClientName, gitClient, tfVariablesMapping, commitTime)
	if err != nil {
		return "", err
	}

	if isCommitsUpdated {
		return "", nil
	}

	blocks, csp, err := getTfBlocksFromFile(fileContent, false, false, tfVariablesMapping, gitClientName, gitClient, nil, tenantID)
	if err != nil {
		return csp, err
	}

	if len(blocks) == 0 {
		return csp, err
	}

	// for deleted blocks
	if csp == "" {
		if strings.Contains(gitDiff, "resource \"aws_") {
			csp = terraformResourceProviderToCSP["aws"]
		} else if strings.Contains(gitDiff, "resource \"google_") {
			csp = terraformResourceProviderToCSP["google"]
		} else if strings.Contains(gitDiff, "resource \"azurerm_") {
			csp = terraformResourceProviderToCSP["azurerm"]
		}
	}
	tfToCSPResourceType = cspToMapForResourceType[csp]

	modifiedBlocks, err := mapDiffToTfBlocks(blocks, gitDiff, tfVariablesMapping, gitClientName, gitClient, tenantID)
	if err != nil {
		return csp, err
	}

	mappedResources, err := mapChangedResources(modifiedBlocks, tfVariablesMapping, false, gitClientName, gitClient, csp, tfToCSPResourceType, commitTime, tenantID, docID, tfResourcePropertiesMap, filePath, repoName)
	if err != nil {
		return csp, err
	}
	for _, mappedResource := range mappedResources {
		if _, err = elastic.InsertDocument(tenantID, elastic.TF_COMMITS_INDEX, mappedResource); err != nil {
			continue
		}
	}

	return csp, nil

}

func getTfBlocksFromFile(fileContent string, isDeleted bool, isVariableMapping bool, tfVariablesMapping map[string]any, gitClientName string, gitClient any, variableMapping map[string]any, tenantID string) ([]ResourceBlock, string, error) {
	tfResourceBlocks := make([]ResourceBlock, 0)
	csp := ""

	if fileContent == "" {
		return nil, "", nil
	}

	hclFile, diagnostics := hclwrite.ParseConfig([]byte(fileContent), "", hcl.InitialPos)
	if diagnostics != nil && diagnostics.HasErrors() {
		hclErrors := diagnostics.Errs()
		logger.Print(logger.INFO, "failed to parse hcl file ", []string{tenantID},
			fileContent, hclErrors)
		return nil, "", nil
	}
	hclSyntaxFile, diagnostics := hclsyntax.ParseConfig([]byte(fileContent), "", hcl.InitialPos)
	if diagnostics != nil && diagnostics.HasErrors() {
		hclErrors := diagnostics.Errs()
		logger.Print(logger.INFO, "failed to parse hcl syntax file ", []string{tenantID}, fileContent, hclErrors)
		return nil, "", nil
	}

	if hclFile == nil || hclSyntaxFile == nil {
		logger.Print(logger.INFO, "Failed to Parse Either Hcl File or Hcl Syntax file ", []string{tenantID}, fileContent)
		return nil, "", errors.New("error")
	}

	// variable mapping for .tfvars file -> highest prefence
	if isVariableMapping {
		varMapping := make(map[string]any)

		syntaxBlocks := hclSyntaxFile.Body.(*hclsyntax.Body)

		if len(syntaxBlocks.Blocks) == 0 {
			commitIdsUpdatedVars := make(map[string]struct{})
			err := mapAttributes(syntaxBlocks.Attributes, varMapping, tfVariablesMapping, isVariableMapping, gitClientName, gitClient, commitIdsUpdatedVars, "", csp, "", nil, nil, nil, tenantID)
			if err != nil {
				logger.Print(logger.INFO, "failed to parse variables", err)
				return nil, "", err
			}
			for key, value := range varMapping {
				if value != "" {
					if variableMapping != nil {
						if val, ok := tfVariablesMapping[key]; ok {
							if arr, isArray := val.([]any); isArray {
								// For numbers case
								if valArr, isValueArray := value.([]any); isValueArray {
									if _, is2dArray := arr[0].([]any); !is2dArray {
										newArr := []any{arr, valArr}
										tfVariablesMapping[key] = newArr
									} else {
										if !arrContains(arr, valArr) {
											arr = append(arr, valArr)
											tfVariablesMapping[key] = arr
										}
									}

								} else {
									if !contains(arr, value) {
										arr = append(arr, value)
										tfVariablesMapping[key] = arr
									}
								}
							}
						} else {
							newArr := []any{value}
							tfVariablesMapping[key] = newArr
						}
					}
				}
			}
			return nil, "", nil
		}

	}

	syntaxBody := hclSyntaxFile.Body.(*hclsyntax.Body)
	if len(syntaxBody.Blocks) == 0 {
		logger.Print(logger.INFO, "Got error while parsing block. Got zero blocks")
		return nil, "", nil
	}
	syntaxBlocks := syntaxBody.Blocks

	fileContentSplit := strings.Split(fileContent, "\n")
	if fileContentSplit[len(fileContentSplit)-1] == "}" {
		fileContentSplit = append(fileContentSplit, "")
	}

	for i := 0; i < len(syntaxBlocks); i++ {
		resourceContent := ""
		if csp == "" && (syntaxBlocks[i].Type == "provider" || syntaxBlocks[i].Type == "terraform") {
			csp = parseCspType(syntaxBlocks[i])
		}

		if syntaxBlocks[i].Type == "variable" || syntaxBlocks[i].Type == "locals" || syntaxBlocks[i].Type == "data" {
			varMapping := make(map[string]any)
			commitIdsUpdatedVars := make(map[string]struct{})

			err := mapBlocks(syntaxBlocks[i].Body, varMapping, tfVariablesMapping, true, gitClientName, gitClient, commitIdsUpdatedVars, "", csp, "", nil, nil, nil, tenantID)
			if err != nil {
				continue
			}

			if syntaxBlocks[i].Type == "variable" {
				if len(tfVariablesMapping) != 0 {
					if _, ok := tfVariablesMapping[syntaxBlocks[i].Labels[0]]; ok {
						if variableMapping != nil {
							for key, value := range varMapping {
								if value != "" {
									variableMapping[key] = value
								}
							}
						}
						continue
					}
				}
				if val, ok := varMapping["default"]; ok && val != "" && tfVariablesMapping != nil {
					tfVariablesMapping[syntaxBlocks[i].Labels[0]] = val
				}
			} else {
				for key, value := range varMapping {
					if _, ok := tfVariablesMapping[key]; ok {
						continue
					}
					tfVariablesMapping[key] = value
				}
			}

		}

		if !slices.Contains(SupportedBlockTypes, syntaxBlocks[i].Type) {
			continue
		}

		if syntaxBlocks[i].OpenBraceRange.Start.Line > 0 && syntaxBlocks[i].CloseBraceRange.End.Line > 0 && syntaxBlocks[i].OpenBraceRange.Start.Line <= len(fileContentSplit) && syntaxBlocks[i].CloseBraceRange.End.Line <= len(fileContentSplit) {
			resourceContentArr := fileContentSplit[syntaxBlocks[i].OpenBraceRange.Start.Line-1 : syntaxBlocks[i].CloseBraceRange.End.Line]
			resourceContent = strings.Join(resourceContentArr, "\n")
		}

		var (
			name, label string
		)

		if len(syntaxBlocks[i].Labels) > 0 {
			name = syntaxBlocks[i].Labels[0]
			if len(syntaxBlocks[i].Labels) > 1 {
				label = syntaxBlocks[i].Labels[1]
			}
		}

		tfResourceBlocks = append(tfResourceBlocks, ResourceBlock{
			StartingLineNumber: syntaxBlocks[i].OpenBraceRange.Start.Line,
			EndingLineNumber:   syntaxBlocks[i].CloseBraceRange.End.Line,
			Name:               name,
			Label:              label,
			SyntaxBlock:        syntaxBlocks[i],
			IsDeleted:          isDeleted,
			ResourceContent:    resourceContent,
		})

	}
	if csp == "" {
		for i := 0; i < len(syntaxBlocks); i++ {
			if syntaxBlocks[i].Type == "resource" {
				csp = parseCspType(syntaxBlocks[i])
			}
		}
	}
	return tfResourceBlocks, csp, nil
}

func parseCspType(tfBlock *hclsyntax.Block) string {
	if tfBlock.Type == "terraform" {
		for _, block := range tfBlock.Body.Blocks {
			if block.Type == "required_providers" {
				for _, attr := range block.Body.Attributes {
					if attr.Name == "aws" || attr.Name == "azurerm" || attr.Name == "google" {
						return terraformResourceProviderToCSP[attr.Name]
					}
				}
			}
		}
	} else if tfBlock.Type == "provider" {
		return terraformResourceProviderToCSP[tfBlock.Labels[0]]
	} else if tfBlock.Type == "resource" {
		if strings.Contains(tfBlock.Labels[0], "azurerm") {
			return terraformResourceProviderToCSP["azurerm"]
		} else if strings.Contains(tfBlock.Labels[0], "google") {
			return terraformResourceProviderToCSP["google"]
		} else if strings.Contains(tfBlock.Labels[0], "aws") {
			return terraformResourceProviderToCSP["aws"]
		}
	}
	return ""
}

func mapDiffToTfBlocks(tfResourceBlocks []ResourceBlock, gitDiff string, tfVariablesMapping map[string]any, gitClientName string, gitClient any, tenantID string) ([]ResourceBlock, error) {
	var (
		additionLineNumber         int = -999
		deletionLineNumber         int = -999
		err                        error
		modifiedResources          []ResourceBlock
		insertions                 int
		deletions                  int
		deletedBlock               string
		currentResourceType        string
		currentResourceName        string
		braceCount                 int
		isBraceOpenFound           bool
		uniqueModifiedResourcesMap = make(map[string]any)
	)

	diffLines := strings.Split(gitDiff, "\n")

	for _, diffLine := range diffLines {
		re := regexp.MustCompile(`@@ -(\d+),\d+ \+(\d+),\d+ @@`)
		match := re.FindStringSubmatch(diffLine)

		if len(match) == 3 {
			isBraceOpenFound = false
			deletionLineNumber, err = strconv.Atoi(match[1])
			if err != nil {
				logger.Print(logger.ERROR, "Error covnerting string line number to int", match[1], err)
				return nil, err
			}
			additionLineNumber, err = strconv.Atoi(match[2])
			if err != nil {
				logger.Print(logger.ERROR, "Error covnerting string line number to int", match[2], err)
				return nil, err
			}

			deletionLineNumber -= 1
			additionLineNumber -= 1

			insertions, deletions = 0, 0
			if strings.Contains(diffLine, "resource") {
				rscType, rscName := extractResourceInfo(diffLine, additionLineNumber, tfResourceBlocks)
				if rscType != "" && rscName != "" {
					currentResourceName, currentResourceType = rscName, rscType
				}
			}
			continue

		}

		if strings.Contains(diffLine, "resource") {
			rscType, rscName := extractResourceInfo(diffLine, additionLineNumber, tfResourceBlocks)
			if rscType != "" && rscName != "" {
				currentResourceName, currentResourceType = rscName, rscType
			}
		}

		if (strings.HasPrefix(diffLine, "+") || strings.HasPrefix(diffLine, "-")) && (additionLineNumber > -999 && deletionLineNumber > -999) {
			var modifiedTfBlock *ResourceBlock
			if strings.HasPrefix(diffLine, "+") && !strings.HasPrefix(diffLine, "++") {
				additionLineNumber++
				if diffLine == "+" || diffLine == "" {
					insertions++
					continue
				}
				if deletedBlock != "" && strings.Contains(diffLine, "}") && braceCount == 0 && isBraceOpenFound {
					deletedTfBlock := parseDeletedBlock(deletedBlock, tfVariablesMapping, gitClientName, gitClient, tenantID)
					if deletedTfBlock != nil {
						modifiedResources = append(modifiedResources, *deletedTfBlock)
					}
					deletedBlock = ""
				}

				modifiedTfBlock = findBlockForLine(tfResourceBlocks, additionLineNumber, currentResourceType, currentResourceName, uniqueModifiedResourcesMap)
				insertions++
			} else if strings.HasPrefix(diffLine, "-") && !strings.HasPrefix(diffLine, "--") {
				deletionLineNumber++
				if diffLine == "-" || diffLine == "" {
					deletions++
					continue
				}
				if strings.Contains(diffLine, "resource") || deletedBlock != "" {
					if strings.Contains(diffLine, "{") {
						braceCount++
						isBraceOpenFound = true
					} else if strings.Contains(diffLine, "}") {
						braceCount--
					}

					deletedBlock += (diffLine[1:] + "\n")
				}
				if deletedBlock != "" && strings.Contains(diffLine, "}") && braceCount == 0 {
					deletedTfBlock := parseDeletedBlock(deletedBlock, tfVariablesMapping, gitClientName, gitClient, tenantID)
					if deletedTfBlock != nil {
						modifiedResources = append(modifiedResources, *deletedTfBlock)
					}

					deletedBlock = ""
				}
				modifiedTfBlock = findBlockForLine(tfResourceBlocks, deletionLineNumber+insertions-deletions, currentResourceType, currentResourceName, uniqueModifiedResourcesMap)
				deletions++
			}
			if modifiedTfBlock != nil {
				uniqueModifiedResourcesMap[modifiedTfBlock.Name+modifiedTfBlock.Label] = struct{}{}
				modifiedResources = append(modifiedResources, *modifiedTfBlock)
			}
		} else {
			if additionLineNumber > -999 && deletionLineNumber > -999 {
				additionLineNumber++
				deletionLineNumber++
			}
		}
	}
	if deletedBlock != "" && braceCount == 0 && isBraceOpenFound {
		deletedTfBlock := parseDeletedBlock(deletedBlock, tfVariablesMapping, gitClientName, gitClient, tenantID)
		if deletedTfBlock != nil {
			modifiedResources = append(modifiedResources, *deletedTfBlock)
		}
	}
	return modifiedResources, nil
}

func findBlockForLine(blocks []ResourceBlock, lineNumber int, currentResourceType string, currentResourceName string, uniqueModifiedResourcesMap map[string]any) *ResourceBlock {
	if _, ok := uniqueModifiedResourcesMap[currentResourceType+currentResourceName]; ok {
		return nil
	}

	for _, block := range blocks {
		if lineNumber >= block.StartingLineNumber && lineNumber <= block.EndingLineNumber && block.Name == currentResourceType && block.Label == currentResourceName {
			return &block
		}
	}
	return nil
}

func findVarBlockForLine(blocks []ResourceBlock, lineNumber int, currentVarName string, uniqueModifiedResourcesMap map[string]any) *ResourceBlock {
	if _, ok := uniqueModifiedResourcesMap[currentVarName]; ok {
		return nil
	}

	for _, block := range blocks {
		if lineNumber >= block.StartingLineNumber && lineNumber <= block.EndingLineNumber && block.Name == currentVarName {
			return &block
		}
	}
	return nil
}

func findResourceNameAndTypeFromLineNumber(blocks []ResourceBlock, lineNumber int) (string, string) {
	for _, block := range blocks {
		if lineNumber >= block.StartingLineNumber && lineNumber <= block.EndingLineNumber {
			return block.Name, block.Label
		}
	}
	return "", ""
}

func mapChangedResources(tfBlocks []ResourceBlock, tfVariablesMapping map[string]any, isVariableMapping bool, gitClientName string, gitClient any, csp string, tfToCSPResourceType map[string]string, commitTime, tenantID string, commitID string, tfResourcePropertiesMap map[string]any, filePath string, repoName string) ([]TFCommitDoc, error) {
	changedTfResources := make([]TFCommitDoc, 0)
	for _, block := range tfBlocks {
		staticPropertiesJsonDataStr, dynamicPropertiesJsonDataStr, commitIdsUpdatedVarsJsonStr, variablesJsonDataStr, priorityConfigsJsonStr := "", "", "", "", ""
		commitIdsUpdatedVars := make(map[string]struct{})
		externalFiles := make(map[string]struct{})
		result := make(map[string]any)
		priorityConfigs := make(map[string]any)
		if block.IsDeleted {
			result["IsDeleted"] = true
		}
		err := mapBlocks(block.SyntaxBlock.Body, result, tfVariablesMapping, isVariableMapping, gitClientName, gitClient, commitIdsUpdatedVars, block.Name, csp, block.Label, tfResourcePropertiesMap, priorityConfigs, externalFiles, tenantID)
		if err != nil {
			logger.Print(logger.ERROR, "Error parsing tf block arguments")
			continue
		}

		if len(result) != 0 {
			if len(commitIdsUpdatedVars) != 0 {
				commitIdsUpdatedVarsJson, err := json.Marshal(commitIdsUpdatedVars)
				if err != nil {
					logger.Print(logger.ERROR, "Erros Marshalling commitIdsUpdatedVars")
					continue
				}
				commitIdsUpdatedVarsJsonStr = string(commitIdsUpdatedVarsJson)
			}
			if len(priorityConfigs) != 0 {
				priorityConfigsJson, err := json.Marshal(priorityConfigs)
				if err != nil {
					logger.Print(logger.ERROR, "Erros Marshalling commitIdsUpdatedVars")
					continue
				}
				priorityConfigsJsonStr = string(priorityConfigsJson)
			}
			var externalFilesArr []string
			if len(externalFiles) != 0 {
				for key := range externalFiles {
					externalFilesArr = append(externalFilesArr, key)
				}
			}
			if len(tfVariablesMapping) != 0 {
				variablesJsonData, err := json.Marshal(tfVariablesMapping)
				if err != nil {
					logger.Print(logger.ERROR, "Got error marshalling variables map", err)
					continue
				}
				variablesJsonDataStr = string(variablesJsonData)
			}
			staticProperties, dynamicProperties := separateStaticDynamicProperties(result)
			if len(staticProperties) != 0 {
				staticPropertiesJsonData, err := json.Marshal(staticProperties)
				if err != nil {
					logger.Print(logger.ERROR, "Got error marshalling static properties", err)
					continue
				}
				staticPropertiesJsonDataStr = string(staticPropertiesJsonData)
			}

			if len(dynamicProperties) != 0 {
				dynamicPropertiesJsonData, err := json.Marshal(dynamicProperties)
				if err != nil {
					logger.Print(logger.ERROR, "Got error marshalling dynamic properties", err)
					continue
				}
				dynamicPropertiesJsonDataStr = string(dynamicPropertiesJsonData)
			}

			resourceType := block.Name
			val, ok := tfToCSPResourceType[block.Name]
			if ok {
				resourceType = val
			}
			resourceTfCommit := TFCommitDoc{
				DocID:                        commitID,
				ResourceLabelName:            block.Label,
				ResourceType:                 resourceType,
				StaticResourceProperties:     staticPropertiesJsonDataStr,
				DynamicResourceProperties:    dynamicPropertiesJsonDataStr,
				Variables:                    variablesJsonDataStr,
				ServiceCode:                  csp,
				TenantID:                     tenantID,
				GitClient:                    gitClientName,
				CommitTime:                   commitTime,
				VariableModifiedCommitDocIds: commitIdsUpdatedVarsJsonStr,
				FilePath:                     filePath,
				UnprocessedResourceType:      block.Name,
				ResourceTemplate:             block.ResourceContent,
				RepoName:                     repoName,
				PriorityConfigs:              priorityConfigsJsonStr,
				ExternalFiles:                externalFilesArr,
			}

			changedTfResources = append(changedTfResources, resourceTfCommit)
		}
	}

	for index, changedTfResource := range changedTfResources {
		var dynamicResourceMap map[string]any

		if changedTfResource.DynamicResourceProperties == "" {
			continue
		}

		err := json.Unmarshal([]byte(changedTfResource.DynamicResourceProperties), &dynamicResourceMap)
		if err != nil {
			logger.Print(logger.ERROR, "Error unmarshaling dynamic variables", err)
			continue
		}

		if len(dynamicResourceMap) == 0 {
			continue
		}

		for key, val := range dynamicResourceMap {
			updateArr := make([]any, 0)
			isUpdateArr := false
			if valArr, ok := val.([]any); ok {
				for _, valAr := range valArr {
					if valArStr, ok := valAr.(string); ok {

						if strings.Contains(valArStr, "{") && strings.Contains(valArStr, "}") {
							valArStr = strings.TrimSuffix(valArStr, "}")
							valArStr = strings.TrimPrefix(valArStr, "{")

							key := strings.Split(valArStr, ".")
							currentValue := make(map[string]any)
							if key[0] != "var" && key[0] != "data" && key[0] != "local" {
								switch csp {
								case "aws":
									if strings.HasPrefix(key[0], "aws") {
										currentValue = tfResourcePropertiesMap
									}
								case "gcp":
									if strings.HasPrefix(key[0], "google") {
										currentValue = tfResourcePropertiesMap
									}
								case "azure":
									if strings.HasPrefix(key[0], "azurerm") {
										currentValue = tfResourcePropertiesMap
									}
								}
								for _, k := range key {
									val, ok := currentValue[k]
									if val != nil {
										if !ok {
											if valueArr, ok := terraformResourceLinking[k]; ok {
												for _, valAr := range valueArr {
													if val, ok = currentValue[valAr]; ok {
														break
													}
												}
											}
										}

										switch v := val.(type) {
										case map[string]any:
											currentValue = v
										default:
											if valArLoc, ok := val.([]any); ok {
												isUpdateArr = true
												updateArr = append(updateArr, valArLoc...)
											} else if val == "" || val == nil {
												isUpdateArr = true
												updateArr = append(updateArr, val)
											}
										}
									}
								}
							}
						}
					} else if valArr1, ok := valAr.([]any); ok {

						for _, valAr1 := range valArr1 {
							if valArStr, ok := valAr1.(string); ok {

								if strings.Contains(valArStr, "{") && strings.Contains(valArStr, "}") {
									valArStr = strings.TrimSuffix(valArStr, "}")
									valArStr = strings.TrimPrefix(valArStr, "{")

									key := strings.Split(valArStr, ".")
									currentValue := make(map[string]any)
									if key[0] != "var" && key[0] != "data" && key[0] != "local" {
										switch csp {
										case "aws":
											if strings.HasPrefix(key[0], "aws") {
												currentValue = tfResourcePropertiesMap
											}
										case "gcp":
											if strings.HasPrefix(key[0], "google") {
												currentValue = tfResourcePropertiesMap
											}
										case "azure":
											if strings.HasPrefix(key[0], "azurerm") {
												currentValue = tfResourcePropertiesMap
											}
										}
										for _, k := range key {
											val, ok := currentValue[k]
											if val != nil {
												if !ok {
													if valueArr, ok := terraformResourceLinking[k]; ok {
														for _, valAr := range valueArr {
															if val, ok = currentValue[valAr]; ok {
																break
															}
														}
													}
												}

												switch v := val.(type) {
												case map[string]any:
													currentValue = v
												default:
													if valArLoc, ok := val.([]any); ok {
														isUpdateArr = true
														updateArr = append(updateArr, valArLoc...)
													} else if val == "" || val == nil {
														isUpdateArr = true
														updateArr = append(updateArr, val)
													}
												}
											}
										}
									}
								}
							}
						}

					}
					if !isUpdateArr {
						updateArr = append(updateArr, valAr)
					}
				}
				dynamicResourceMap[key] = updateArr
			}
		}

		if len(dynamicResourceMap) != 0 {
			dynamicPropertiesJsonDataStr := ""
			dynamicPropertiesJsonData, err := json.Marshal(dynamicResourceMap)
			if err != nil {
				logger.Print(logger.ERROR, "Got error marshalling dynamic properties", err)
				continue
			}
			dynamicPropertiesJsonDataStr = string(dynamicPropertiesJsonData)
			changedTfResource.DynamicResourceProperties = dynamicPropertiesJsonDataStr
		}

		changedTfResources[index] = changedTfResource
	}
	return changedTfResources, nil
}

func mapBlocks(block *hclsyntax.Body, result map[string]any, tfVariablesMapping map[string]any, isVariableMapping bool, gitClientName string, gitClient any, commitIdsUpdatedVars map[string]struct{}, resourceType string, csp string, blockLabel string, tfResourcePropertiesMap map[string]any, priorityConfigs map[string]any, externalFiles map[string]struct{}, tenantID string) error {
	err := mapAttributes(block.Attributes, result, tfVariablesMapping, isVariableMapping, gitClientName, gitClient, commitIdsUpdatedVars, resourceType, csp, blockLabel, tfResourcePropertiesMap, priorityConfigs, externalFiles, tenantID)
	if err != nil {
		return err
	}
	for i := 0; i < len(block.Blocks); i++ {
		nestedResult := make(map[string]any)
		err := mapBlocks(block.Blocks[i].Body, nestedResult, tfVariablesMapping, isVariableMapping, gitClientName, gitClient, commitIdsUpdatedVars, resourceType, csp, blockLabel, tfResourcePropertiesMap, priorityConfigs, externalFiles, tenantID)
		if err != nil {
			return err
		}
		for key, value := range nestedResult {

			if val, ok := result[key]; ok {
				if valArr, ok := val.([]any); ok {
					if valueArr, ok := value.([]any); ok {
						for _, valArrVal := range valueArr {
							valArr = append(valArr, valArrVal)
						}
					}
					value = valArr
				} else {
					valArr := make([]any, 0)
					valArr = append(valArr, value)
					value = valArr
				}
			}
			result[key] = value
		}
	}
	return nil
}

func mapAttributes(attributes hclsyntax.Attributes, result map[string]any, tfVariablesMapping map[string]any, isVariableMapping bool, gitClientName string, gitClient any, commitIdsUpdatedVars map[string]struct{}, resourceType string, csp string, blockLabel string, tfResourcePropertiesMap map[string]any, priorityConfigs map[string]any, externalFiles map[string]struct{}, tenantID string) error {
	for _, attr := range attributes {
		isAttrInserted := false
		var isDynamic bool = false
		expressionNodes, _ := attr.Expr.(hclsyntax.Node)
		var (
			value         []any
			valueVariable any
			err           error
		)
		valueString := ""

		switch expressionNodes := expressionNodes.(type) {
		case *hclsyntax.ScopeTraversalExpr:

			ctx := &hcl.EvalContext{}
			val, diags := expressionNodes.Value(ctx)

			err = parseTerraformFields(&value, &valueString, val, diags, tfVariablesMapping, &isDynamic, gitClientName, gitClient, isVariableMapping, &valueVariable, commitIdsUpdatedVars, tfResourcePropertiesMap, csp)
			if err != nil {
				return err
			}
			if valueString != "" {
				value = append(value, valueString)
			}
		case *hclsyntax.TemplateExpr:

			//name = "${var.bucketName}"

			for _, expressionNode := range expressionNodes.Parts {
				switch expressionNode.(type) {
				case *hclsyntax.ConditionalExpr:
					continue
				}
				ctx := &hcl.EvalContext{}
				val, diags := expressionNode.Value(ctx)

				err = parseTerraformFields(&value, &valueString, val, diags, tfVariablesMapping, &isDynamic, gitClientName, gitClient, isVariableMapping, &valueVariable, commitIdsUpdatedVars, tfResourcePropertiesMap, csp)
				if err != nil {
					return err
				}
			}

		case *hclsyntax.LiteralValueExpr:
			//subnet_id     = "subnet-abcdef12"
			//allocated_storage = 5

			val, _ := expressionNodes.Value(nil)
			underlyingValue, err := extractUnderlyingValue(val)
			if err != nil {
				return err
			}

			if isVariableMapping {
				valueVariable = underlyingValue
			} else {
				value = append(value, underlyingValue)
			}
		case *hclsyntax.FunctionCallExpr:
			//value = max(args1, args2)
			//value = jsonencode("___")
			if len(expressionNodes.Args) > 0 {
				argsNodes, ok := expressionNodes.Args[0].(hclsyntax.Node)
				if !ok {
					logger.Print(logger.ERROR, "unexpected argument type", expressionNodes.Args[0])
					return fmt.Errorf("error")
				}

				var functionParameters []any

				switch expressionNodes := argsNodes.(type) {
				case *hclsyntax.TemplateExpr:

					for _, expressionNode := range expressionNodes.Parts {
						switch expressionNode.(type) {
						case *hclsyntax.ConditionalExpr:
							continue
						}
						ctx := &hcl.EvalContext{}
						val, diags := expressionNode.Value(ctx)

						err = parseTerraformFields(&value, &valueString, val, diags, tfVariablesMapping, &isDynamic, gitClientName, gitClient, isVariableMapping, &valueVariable, commitIdsUpdatedVars, tfResourcePropertiesMap, csp)
						if err != nil {
							return err
						}
					}
					if valueString != "" {
						value = append(value, valueString)
					}
					functionParameters = append(functionParameters, value)
				case *hclsyntax.ObjectConsExpr:
					jsonVal, err := extractKeysAndValues(expressionNodes, tfVariablesMapping, &isDynamic, gitClientName, gitClient, isVariableMapping, &valueVariable, commitIdsUpdatedVars, tfResourcePropertiesMap, csp)
					if err != nil {
						logger.Print(logger.INFO, "Error parsing json")
						return err
					}
					functionParameters = append(functionParameters, jsonVal)

				case *hclsyntax.ScopeTraversalExpr:
					ctx := &hcl.EvalContext{}
					val, diags := expressionNodes.Value(ctx)

					err := parseTerraformFields(&value, &valueString, val, diags, tfVariablesMapping, &isDynamic, gitClientName, gitClient, isVariableMapping, &valueVariable, commitIdsUpdatedVars, tfResourcePropertiesMap, csp)
					if err != nil {
						return err
					}
					if valueString != "" {
						value = append(value, valueString)
					}
					if traversableValues, err := processTraversableValue(value); err == nil {
						functionParameters = append(functionParameters, traversableValues...)
					} else {
						return err
					}

				case *hclsyntax.TupleConsExpr:

					expressions := expressionNodes.Exprs
					for _, expression := range expressions {
						ctx := &hcl.EvalContext{}
						val, diags := expression.Value(ctx)
						err := parseTerraformFields(&value, &valueString, val, diags, tfVariablesMapping, &isDynamic, gitClientName, gitClient, isVariableMapping, &valueVariable, commitIdsUpdatedVars, tfResourcePropertiesMap, csp)
						if err != nil {
							return err
						}
						if valueString != "" {
							value = append(value, valueString)
						}
						functionParameters = append(functionParameters, value)
						value = nil
						valueString = ""
					}
				}
				var funcRes []any
				for _, functionParameter := range functionParameters {
					res, err := parseTfFunctions(expressionNodes.Name, functionParameter)
					if err != nil {
						logger.Print(logger.ERROR, "Error parsing function", err)
						return err
					}
					if res != nil {
						funcRes = append(funcRes, res)
					}
				}
				if isVariableMapping {
					valueVariable = funcRes
				} else {
					value = funcRes
				}
			}

		case *hclsyntax.TupleConsExpr:
			//default = (42, "hello", true)

			argsNodes := expressionNodes.Exprs
			var tupleValues []any
			for _, expression := range argsNodes {
				switch expressionNodes := expression.(type) {
				case *hclsyntax.ObjectConsExpr:
					jsonVal, err := extractKeysAndValues(expressionNodes, tfVariablesMapping, &isDynamic, gitClientName, gitClient, isVariableMapping, &valueVariable, commitIdsUpdatedVars, tfResourcePropertiesMap, csp)
					if err != nil {
						logger.Print(logger.INFO, "Error parsing json")
						return err
					}
					tupleValues = append(tupleValues, jsonVal)
				default:
					ctx := &hcl.EvalContext{}
					val, diags := expression.Value(ctx)
					err := parseTerraformFields(&value, &valueString, val, diags, tfVariablesMapping, &isDynamic, gitClientName, gitClient, isVariableMapping, &valueVariable, commitIdsUpdatedVars, tfResourcePropertiesMap, csp)
					if err != nil {
						return err
					}
					if valueString != "" {
						value = append(value, valueString)
					}
					tupleValues = append(tupleValues, value)
				}
				value = nil
				valueString = ""
			}
			if isVariableMapping {
				valueVariable = tupleValues
			} else {
				value = tupleValues
			}
		case *hclsyntax.ConditionalExpr:
			//value = var.use_production ? var.production_value : var.development_value

			var (
				lhsVal      []any
				rhsVal      []any
				trueResult  []any
				falseResult []any
			)
			ctx := &hcl.EvalContext{}
			val, diags := expressionNodes.TrueResult.Value(ctx)
			err := parseTerraformFields(&value, &valueString, val, diags, tfVariablesMapping, &isDynamic, gitClientName, gitClient, isVariableMapping, &valueVariable, commitIdsUpdatedVars, tfResourcePropertiesMap, csp)
			if err != nil {
				return err
			}
			if valueString != "" {
				value = append(value, valueString)
			}
			trueResult = value
			value = nil
			valueString = ""
			val, diags = expressionNodes.FalseResult.Value(ctx)
			err = parseTerraformFields(&value, &valueString, val, diags, tfVariablesMapping, &isDynamic, gitClientName, gitClient, isVariableMapping, &valueVariable, commitIdsUpdatedVars, tfResourcePropertiesMap, csp)
			if err != nil {
				return err
			}
			if valueString != "" {
				value = append(value, valueString)
			}
			falseResult = value
			value = nil
			valueString = ""
			switch condExpr := expressionNodes.Condition.(type) {
			case *hclsyntax.BinaryOpExpr:
				lhs := condExpr.LHS
				rhs := condExpr.RHS
				ops := condExpr.Op

				val, diags := lhs.Value(ctx)
				err := parseTerraformFields(&lhsVal, &valueString, val, diags, tfVariablesMapping, &isDynamic, gitClientName, gitClient, isVariableMapping, &valueVariable, commitIdsUpdatedVars, tfResourcePropertiesMap, csp)
				if err != nil {
					return err
				}
				if valueString != "" {
					lhsVal = append(lhsVal, valueString)
					valueString = ""
				}

				val, diags = rhs.Value(ctx)
				err = parseTerraformFields(&rhsVal, &valueString, val, diags, tfVariablesMapping, &isDynamic, gitClientName, gitClient, isVariableMapping, &valueVariable, commitIdsUpdatedVars, tfResourcePropertiesMap, csp)
				if err != nil {
					return err
				}
				if valueString != "" {
					rhsVal = append(rhsVal, valueString)
					valueString = ""
				}

				if ops == hclsyntax.OpEqual && lhsVal != nil && rhsVal != nil {
					foundMatch := false
					for _, lhs := range lhsVal {
						for _, rhs := range rhsVal {
							if equalVal(lhs, rhs) {
								value = trueResult
								foundMatch = true
								break
							}
						}
						if foundMatch {
							break
						}
					}

					if !foundMatch {
						value = falseResult
					}
				}

			default:
				val, diags := expressionNodes.Value(ctx)
				err := parseTerraformFields(&value, &valueString, val, diags, tfVariablesMapping, &isDynamic, gitClientName, gitClient, isVariableMapping, &valueVariable, commitIdsUpdatedVars, tfResourcePropertiesMap, csp)
				if err != nil {
					return err
				}
			}
		case *hclsyntax.TemplateWrapExpr:
			wrapNode := expressionNodes.Wrapped
			switch expressionNodes := wrapNode.(type) {
			case *hclsyntax.FunctionCallExpr:
				if len(expressionNodes.Args) > 0 {
					argsNodes, ok := expressionNodes.Args[0].(hclsyntax.Node)
					if !ok {
						logger.Print(logger.ERROR, "unexpected argument type", expressionNodes.Args[0])
						return fmt.Errorf("error")
					}
					funcName := expressionNodes.Name

					if funcName == "file" {

						switch expressionNodes := argsNodes.(type) {
						case *hclsyntax.TemplateExpr:

							for _, expressionNode := range expressionNodes.Parts {
								switch expressionNode.(type) {
								case *hclsyntax.ConditionalExpr:
									continue
								}
								ctx := &hcl.EvalContext{}
								val, diags := expressionNode.Value(ctx)

								err = parseTerraformFields(&value, &valueString, val, diags, tfVariablesMapping, &isDynamic, gitClientName, gitClient, isVariableMapping, &valueVariable, commitIdsUpdatedVars, tfResourcePropertiesMap, csp)
								if err != nil {
									return err
								}
							}
							if valueString != "" {
								value = append(value, valueString)
							}

							variableFiles := make([]VariableFile, 0)

							if len(value) > 0 {
								destPath := value[0].(string)
								fileName := filepath.Base(destPath)
								srcPath := fetchFilePath(gitClientName, gitClient)
								filePath := GetAbsoluteFilePath(srcPath, destPath)

								err = fetchGitFilesWithType(gitClientName, gitClient, &variableFiles, fileName, filePath)
								if err != nil {
									return err
								}

								if len(variableFiles) == 0 {
									err := fetchGitFilesWithType(gitClientName, gitClient, &variableFiles, fileName, "")
									if err != nil {
										return err
									}
								}
								value = nil
								for _, variableFile := range variableFiles {
									value = append(value, variableFile.content)
									if externalFiles != nil {
										externalFiles[variableFile.filePath] = struct{}{}
									}
								}
								if externalFiles != nil {
									externalFiles[filePath] = struct{}{}
								}

								switch csp {
								case "aws":
									pattern := `^s3:\/\/([^\/]+)\/(.+)$`

									re := regexp.MustCompile(pattern)
									matches := re.FindStringSubmatch(destPath)

									if len(matches) >= 3 {
										bucketName := matches[1]
										fileName := matches[2]

										accountIds, err := FetchTenantAccountIds(tenantID, AWS_SERVICE_ID)
										if err != nil {
											return nil
										}

										batch := make([]string, 0)

										for _, accountId := range accountIds {
											url := fmt.Sprintf("/accountId/" + accountId + "/fileName/" + fileName + "/" + bucketName)
											batch = append(batch, url)
										}

										var resourcesProperties []ResourceResponse
										resourcesProperties, err = FetchResourceDetails(SDCRequest{
											EntityType: "S3",
											EntityIds:  batch,
										}, csp, tenantID, nil)
										if err != nil {
											return err
										}

										for _, resourceProperty := range resourcesProperties {

											if resourcePropertyStr, ok := resourceProperty.ResourceProperties["entityJson"].(string); ok {
												value = append(value, resourcePropertyStr)
											}
										}
									}
								case "gcp":
								case "azure":
								}
							}
						}
					}
				}
			}

		case *hclsyntax.ObjectConsExpr:
			// tags = {
			// 		Name = "HelloWorld"
			// }

			// type = object({
			// 	ami           = string
			// 	instance_type = string
			// 	key_name      = string
			// 	subnet_id     = string
			// 	tags          = map(string)
			//   })

			jsonVal, err := extractKeysAndValues(expressionNodes, tfVariablesMapping, &isDynamic, gitClientName, gitClient, isVariableMapping, &valueVariable, commitIdsUpdatedVars, tfResourcePropertiesMap, csp)
			if err != nil {
				logger.Print(logger.INFO, "Error parsing json")
				return err
			}
			if attr.Name == "tags" {

				for k, v := range jsonVal {
					if !isDynamic {
						if val, ok := result["static:key"]; !ok {
							keyArr := make([]any, 0)
							keyArr = append(keyArr, k)
							result["static:key"] = keyArr
						} else {
							if valArr, ok := val.([]any); ok {
								valArr = append(valArr, k)
								result["static:key"] = valArr
							}
						}

						if val, ok := result["static:value"]; !ok {
							valueArr := make([]any, 0)
							if vArr, ok := v.([]any); ok {
								for _, vAr := range vArr {
									valueArr = append(valueArr, vAr)
								}
							}
							result["static:value"] = valueArr
						} else {
							if val, ok := val.([]any); ok {
								if valArr, ok := v.([]any); ok {
									for _, valAr := range valArr {
										val = append(val, valAr)
									}
									result["static:value"] = val
								}
							}
						}
					} else {
						if val, ok := result["dynamic:key"]; !ok {
							keyArr := make([]any, 0)
							keyArr = append(keyArr, k)
							result["dynamic:key"] = keyArr
						} else {
							if valArr, ok := val.([]any); ok {
								valArr = append(valArr, k)
								result["dynamic:key"] = valArr
							}
						}

						if val, ok := result["dynamic:value"]; !ok {
							valueArr := make([]any, 0)
							if vArr, ok := v.([]any); ok {
								for _, vAr := range vArr {
									valueArr = append(valueArr, vAr)
								}
							}
							result["dynamic:value"] = valueArr
						} else {
							if val, ok := val.([]any); ok {
								if valArr, ok := v.([]any); ok {
									for _, valAr := range valArr {
										val = append(val, valAr)
									}
									result["dynamic:value"] = val
								}
							}
						}
					}
				}
				value = nil
				valueString = ""
				isAttrInserted = true
			} else {
				value = append(value, jsonVal)
			}

		default:
			value = nil
		}
		if !isAttrInserted {
			if !isDynamic {
				if isVariableMapping {
					result[attr.Name] = valueVariable
				} else {
					val, ok := tfResourcePropertiesMap[resourceType]
					if !ok {
						if len(value) <= 0 {
							continue
						}

						if len(resourceType) > 0 && tfResourcePropertiesMap != nil {
							if resourceType == "locals" {
								resourceType = "local"
							}

							tfResourcePropertiesMap[resourceType] = make(map[string]any)

							if len(blockLabel) > 0 {
								tfResourcePropertiesMap[resourceType].(map[string]any)[blockLabel] = make(map[string]any)

								if val, ok := tfResourcePropertiesMap[resourceType].(map[string]any)[blockLabel].(map[string]any)[attr.Name]; ok {
									value = append(value, val)
								}
								tfResourcePropertiesMap[resourceType].(map[string]any)[blockLabel].(map[string]any)[attr.Name] = value
							} else {
								if val, ok := tfResourcePropertiesMap[resourceType].(map[string]any)[attr.Name]; ok {
									value = append(value, val)
								}
								tfResourcePropertiesMap[resourceType].(map[string]any)[attr.Name] = value
							}
						}

					} else if _, ok := val.(map[string]any)[blockLabel]; !ok {
						if len(blockLabel) > 0 {
							val.(map[string]any)[blockLabel] = make(map[string]any)
							val.(map[string]any)[blockLabel].(map[string]any)[attr.Name] = value
						} else {
							if val, ok := val.(map[string]any)[attr.Name]; ok {
								value = append(value, val)
							}
							val.(map[string]any)[attr.Name] = value
						}
					} else {
						val.(map[string]any)[blockLabel].(map[string]any)[attr.Name] = value
					}
					attrName := findAttrNameAlias(attr.Name, resourceType, csp)
					result["static:"+ParseString(attrName)] = value

					if priorityConfigs != nil {
						if _, ok := cspToMapForPriorityConfigs[csp][attr.Name]; ok {
							priorityConfigs[ParseString(attr.Name)] = value
						}
					}
				}
			} else {
				if isVariableMapping {
					result[attr.Name] = valueVariable
				} else {
					if len(value) <= 0 {
						continue
					}

					attrName := findAttrNameAlias(attr.Name, resourceType, csp)
					result["dynamic:"+ParseString(attrName)] = value

					//Special case for inputs block (terragrunt)
					if attrName == "inputs" {
						for _, v := range value {
							if vMap, ok := v.(map[string]any); ok {
								if tfResourcePropertiesMap != nil {
									tfResourcePropertiesMap["outputs"] = make(map[string]any)
									outputsMap := make(map[string]any)
									for k, value := range vMap {
										outputsMap[k] = value
									}
									tfResourcePropertiesMap["outputs"] = outputsMap
								}
							}
						}
					}

					if priorityConfigs != nil {
						if _, ok := cspToMapForPriorityConfigs[csp][attr.Name]; ok {
							priorityConfigs[ParseString(attr.Name)] = value
						}
					}
				}
			}
			value = nil
			valueString = ""
		}
	}
	return nil
}

func parseVal(val cty.Value) string {
	ty := val.Type()
	switch {
	case val.IsNull():
		return "null"
	case !val.IsKnown():
		return "(not yet known)"
	case ty == cty.Bool:
		if val.True() {
			return "true"
		}
		return "false"
	case ty == cty.Number:
		bf := val.AsBigFloat()
		return bf.Text('g', 10)
	case ty == cty.String:
		return fmt.Sprintf("%q", val.AsString())
	case ty.IsCollectionType() || ty.IsTupleType():
		// l := val.LengthInt()
		// switch l {
		// case 0:
		// 	return "empty " + ty.FriendlyName()
		// case 1:
		// 	return ty.FriendlyName() + " with 1 element"
		// default:
		// 	return fmt.Sprintf("%s with %d elements", ty.FriendlyName(), l)
		// }
		return ""
	case ty.IsObjectType():
		// atys := ty.AttributeTypes()
		// l := len(atys)
		// switch l {
		// case 0:
		// 	return "object with no attributes"
		// case 1:
		// 	var name string
		// 	for k := range atys {
		// 		name = k
		// 	}
		// 	return fmt.Sprintf("object with 1 attribute %q", name)
		// default:
		// 	return fmt.Sprintf("object with %d attributes", l)
		// }
		return ""
	default:
		return ty.FriendlyName()
	}
}

func extractUnderlyingValue(val cty.Value) (any, error) {

	if val.CanIterateElements() {
		mp := make(map[string]any)
		var arr []any

		it := val.ElementIterator()
		for it.Next() {
			key, value := it.Element()
			val, err := extractUnderlyingValue(value)
			if err != nil {
				return nil, err
			}
			if key.Type().GoString() == "cty.String" {
				mp[key.AsString()] = val

			} else {
				arr = append(arr, val)

			}
		}
		if arr != nil {
			return arr, nil
		}
		return mp, nil
	}

	switch val.Type().GoString() {
	case "cty.String":
		return val.AsString(), nil
	case "cty.Bool":
		return val.True(), nil
	case "cty.Number":
		intVal, acc := val.AsBigFloat().Int64()
		if acc != big.Exact {
			logger.Print(logger.ERROR, "conversion to int64 not exact")
			return nil, fmt.Errorf("error")
		}
		return intVal, nil
	default:
		logger.Print(logger.INFO, "Unsupported cty value type", val.Type().GoString())
		return nil, nil
	}
}

func parseDeletedBlock(deletedBlock string, tfVariablesMapping map[string]any, gitClientName string, gitClient any, tenantID string) *ResourceBlock {
	blocks, _, err := getTfBlocksFromFile(deletedBlock, true, false, tfVariablesMapping, gitClientName, gitClient, nil, tenantID)

	if err != nil {
		deleteBlockSlice := strings.Split(deletedBlock, "\n")
		for _, str := range deleteBlockSlice {
			currentResourceType, currentResourceName := extractResourceInfo(str, -1, nil)
			if currentResourceName != "" && currentResourceType != "" {
				return &ResourceBlock{
					StartingLineNumber: 0,
					EndingLineNumber:   0,
					Name:               currentResourceName,
					Label:              currentResourceType,
					SyntaxBlock:        &hclsyntax.Block{},
					IsDeleted:          true,
					ResourceContent:    "",
				}
			}
		}
		logger.Print(logger.ERROR, "failed to parse deleted block")
		return nil
	}
	return &blocks[0]
}

func extractResourceInfo(input string, lineNumber int, tfResourceBlocks []ResourceBlock) (string, string) {
	re := regexp.MustCompile(`resource.*?"([^"]+)".*?"([^"]+)"`)

	matches := re.FindStringSubmatch(input)

	if len(matches) == 3 {
		if strings.HasSuffix(matches[1], `\`) || strings.HasSuffix(matches[2], `\`) {

			matches[1] = strings.TrimSuffix(matches[1], `\`)
			matches[2] = strings.TrimSuffix(matches[2], `\`)
		}
		return matches[1], matches[2]
	} else if lineNumber != -1 && tfResourceBlocks != nil {

		re := regexp.MustCompile(`resource.*?"([^"]+)"`)

		matches := re.FindStringSubmatch(input)

		if len(matches) == 2 {
			rscType, rscLabelName := findResourceNameAndTypeFromLineNumber(tfResourceBlocks, lineNumber)
			if rscType != "" && rscLabelName != "" {
				return rscType, rscLabelName
			}
		}
	}

	return "", ""
}

func parseVariableJsonBlock(fileContent string, variableMap map[string]any, gitDiff string, fileStatus string, commitID string, tenantID string) error {
	varMapping := make(map[string]any)

	if fileStatus == "deleted" {
		err := fetchUpdatedVarJsonForDeletedFile(gitDiff, variableMap, commitID, tenantID)
		if err != nil {
			logger.Print(logger.ERROR, "Failed to parse deleted json variable file", err)
			return err
		}
		return nil
	}

	if fileStatus == "modified" {
		err := fetchUpdatedVarJsonForUpdatedFile(gitDiff, variableMap, commitID, tenantID)
		if err != nil {
			logger.Print(logger.ERROR, "Failed to parse deleted json variable file", err)
			return err
		}
		return nil
	}

	err := json.Unmarshal([]byte(fileContent), &varMapping)
	if err != nil {
		logger.Print(logger.ERROR, "failed to unmarshal variable json while parsing jsonm block: %s", []string{commitID, tenantID}, fileContent)
		return err
	}

	for key, value := range varMapping {
		if value != "" {
			variableMap[key] = value
		}
	}
	return nil
}

func parseTFVariableInsideModule(tfVariablesMapping map[string]any, gitClientName string, gitClient any) (map[string][]string, error) {
	tenantID, repoName := "", ""
	switch gitClientName {
	case GITHUB:
		if githubApiClient, ok := gitClient.(*GithubApiClient); ok {
			tenantID = githubApiClient.TenantID
			repoName = githubApiClient.RepoName
		}
	case BITBUCKET:
		if bitbucketApiClient, ok := gitClient.(*BitbucketApiClient); ok {
			tenantID = bitbucketApiClient.TenantID
			repoName = bitbucketApiClient.RepoName
		}

	case GITLAB:
		if gitlabApiClient, ok := gitClient.(*GitlabApiClient); ok {
			tenantID = gitlabApiClient.TenantID
			repoName = gitlabApiClient.RepoName
		}
	}

	recentCodeEventsQuery := `{
		"query": {
				"bool": {
					"must": [
						{"match": {"tenantId.keyword": "` + tenantID + `"}},
						{"match": {"repoName.keyword": "` + repoName + `"}}
					]
				}
			}
		}`

	tfVarDocs, err := elastic.ExecuteSearchQuery([]string{elastic.TF_VARIABLES_INDEX}, recentCodeEventsQuery)
	if err != nil {
		logger.Print(logger.ERROR, "Got error while fetching existing module var record", tenantID, repoName)
		return nil, err
	}
	varUpdatedCommitIds := make(map[string][]string)

	if len(tfVarDocs) > 0 {
		for _, tfVarDoc := range tfVarDocs {

			if variableName, ok := tfVarDoc["varName"].(string); ok {
				if _, ok := tfVariablesMapping[variableName]; !ok {
					if variablesJSONStr, ok := tfVarDoc["variables"].(string); ok {
						variableMap := make(map[string]any)
						err := json.Unmarshal([]byte(variablesJSONStr), &variableMap)
						if err != nil {
							logger.Print(logger.ERROR, "Got error while unmarshaling variables json")
							continue
						}

						commitIDs := make(map[string][]string)

						for commitID, value := range variableMap {
							if valueMap, isValueMap := value.([]any); isValueMap {
								for _, valueMapValues := range valueMap {
									if commitID != "unknown" {
										commitIDs[variableName] = append(commitIDs[variableName], commitID)
									}
									if val, ok := tfVariablesMapping[variableName]; ok {
										if valArr, isValArr := val.([]any); isValArr {
											if !contains(valArr, valueMapValues) {
												valArr = append(valArr, valueMapValues)
												tfVariablesMapping[variableName] = valArr
											}
										}
									} else {
										valArr := make([]any, 0)
										valArr = append(valArr, valueMapValues)
										tfVariablesMapping[variableName] = valArr
									}
								}

							}
						}

						for key, value := range commitIDs {
							if val, ok := varUpdatedCommitIds[key]; ok {
								val = append(val, value...)
								varUpdatedCommitIds[key] = val
							} else {
								varUpdatedCommitIds[key] = value
							}
						}
					}
				}
			}
		}
	} else {
		moduleVariablesMap, err := fetchAndUpdateTfVarFiles(gitClientName, gitClient, "unknown", "", "./", tenantID, repoName)
		if err != nil {
			return nil, err
		}

		if moduleVariablesMap == nil {
			return nil, nil
		}
		commitIDs := make(map[string][]string)

		for key, value := range moduleVariablesMap {
			if _, ok := tfVariablesMapping[key]; !ok {
				if valueMap, isValueMap := value.(map[any]any); isValueMap {
					for commitID, valueMapValues := range valueMap {
						if commitID, ok := commitID.(string); ok {
							if commitID != "unknown" {
								commitIDs[key] = append(commitIDs[key], commitID)
							}
							if valueMapValuesArr, isValueMapValuesArr := valueMapValues.([]any); isValueMapValuesArr {
								for _, v := range valueMapValuesArr {
									if val, ok := tfVariablesMapping[key]; ok {
										if valArr, isValArr := val.([]any); isValArr {
											if !contains(valArr, v) {
												valArr = append(valArr, v)
												tfVariablesMapping[key] = valArr
											}
										}
									} else {
										valArr := make([]any, 0)
										valArr = append(valArr, v)
										tfVariablesMapping[key] = valArr
									}
								}
							}
						}
					}
				}
			}
		}

		for key, value := range commitIDs {
			if val, ok := varUpdatedCommitIds[key]; ok {
				val = append(val, value...)
				varUpdatedCommitIds[key] = val
			} else {
				varUpdatedCommitIds[key] = value
			}
		}
	}
	return varUpdatedCommitIds, nil
}

func parseVariableFields(vars []hcl.Traversal, tfVariablesMapping map[string]any, gitClientName string, gitClient any, isVariableMapping bool, commitIdsUpdatedVars map[string]struct{}, tfResourcePropertiesMap map[string]any, csp string) any {
	var updatedCommitIDs map[string][]string
	var err error
	var isResourceLinking bool
	if !isVariableMapping {
		if strings.Contains(vars[0].RootName(), "var") {
			updatedCommitIDs, err = parseTFVariableInsideModule(tfVariablesMapping, gitClientName, gitClient)
			if err != nil {
				return ""
			}
		}
	}
	for _, traversal := range vars {

		var buf bytes.Buffer
		for _, step := range traversal {
			switch tStep := step.(type) {
			case hcl.TraverseRoot:
				buf.WriteString(tStep.Name)
			case hcl.TraverseAttr:
				buf.WriteByte('.')
				buf.WriteString(tStep.Name)
			case hcl.TraverseIndex:
				buf.WriteByte('[')
				if keyTy := tStep.Key.Type(); keyTy.IsPrimitiveType() {
					buf.WriteString(parseVal(tStep.Key))
				} else {
					buf.WriteString("...")
				}
				buf.WriteByte(']')
			}
		}
		keys := strings.Split(buf.String(), ".")
		currentValue := make(map[string]any)
		if keys[0] == "var" || keys[0] == "data" || keys[0] == "local" || keys[0] == "dependency" || keys[0] == "include" || keys[0] == "dependencies" {
			currentValue = tfVariablesMapping
			if keys[0] == "var" {
				keys = keys[1:]
			}
		} else {
			switch csp {
			case "aws":
				if strings.HasPrefix(keys[0], "aws") {
					isResourceLinking = true
					currentValue = tfResourcePropertiesMap
				}
			case "gcp":
				if strings.HasPrefix(keys[0], "google") {
					isResourceLinking = true
					currentValue = tfResourcePropertiesMap
				}
			case "azure":
				if strings.HasPrefix(keys[0], "azurerm") {
					isResourceLinking = true
					currentValue = tfResourcePropertiesMap
				}
			}
		}

		for index, key := range keys {
			if key == "locals" {
				// Special case for terragrunt where locals and local are interchangeable
				key = "local"
			}
			val, ok := currentValue[key]
			if !ok && isResourceLinking {
				if valueArr, ok := terraformResourceLinking[key]; ok {
					for _, valAr := range valueArr {
						if val, ok = currentValue[valAr]; ok {
							break
						}
					}
				}
			}

			switch v := val.(type) {
			case map[string]any:
				currentValue = v
			default:
				if val == "" || val == nil && index == len(keys)-1 {
					str := "{" + buf.String() + "}"
					return str
				} else if val == "" || val == nil {
					continue
				}
				if updatedCommitIDs != nil {
					if val, ok := updatedCommitIDs[key]; ok {
						for _, commitId := range val {
							if _, ok := commitIdsUpdatedVars[commitId]; !ok {
								commitIdsUpdatedVars[commitId] = struct{}{}
							}
						}
					}
				}
				return val
			}
		}

	}
	return ""
}

func parseTerraformFields(value *[]any, valueString *string, val cty.Value, diags hcl.Diagnostics, tfVariablesMapping map[string]any, isDynamic *bool, gitClientName string, gitClient any, isVariableMapping bool, valueVariable *any, commitIdsUpdatedVars map[string]struct{}, tfResourcePropertiesMap map[string]any, csp string) error {
	if diags.HasErrors() {
		if diags[0].Detail == "Variables may not be used here." {
			*isDynamic = true
			expr := diags[0].Expression
			varValue := parseVariableFields(expr.Variables(), tfVariablesMapping, gitClientName, gitClient, isVariableMapping, commitIdsUpdatedVars, tfResourcePropertiesMap, csp)

			switch v := varValue.(type) {
			case []any:
				var varElementType string
				for _, element := range v {
					switch element.(type) {
					case string:
						varElementType = "string"
						break
					case nil:
						varElementType = "unknown"
					default:
						varElementType = "interface"
						break
					}
				}
				switch varElementType {
				case "unknown":
					return nil
				case "string":
					if len(*value) == 0 {
						for _, vVal := range v {
							*value = append(*value, vVal)
						}
					} else {
						var result []any
						for _, val := range *value {
							for _, vVal := range v {
								result = append(result, val.(string)+vVal.(string))
							}
						}
						*value = result
					}
					break
				default:
					if len(*value) == 0 {
						for _, vVal := range v {
							*value = append(*value, vVal)
						}
					} else {
						var result []any
						for _, val := range *value {
							for _, vVal := range v {
								if valArr, ok := val.([]any); ok {
									newVal := append(valArr, vVal)
									result = append(result, newVal)
								}
							}
						}
						*value = result
					}
					break
				}
				break
			default:
				switch v := varValue.(type) {
				case string:
					if len(*value) > 0 {
						for i := 0; i < len(*value); i++ {
							switch (*value)[i].(type) {
							case string:
								(*value)[i] = (*value)[i].(string) + v
							}
						}
					} else {
						*value = append(*value, varValue)
					}
				default:
					*value = append(*value, varValue)
				}

			}
			return nil
		} else {
			if len(diags) > 0 {
				logger.Print(logger.INFO, "failed to evaluate expression: ", diags[0].Detail)
			}
			return nil
		}
	} else {
		underlyingValue, err := extractUnderlyingValue(val)
		if err != nil {
			return err
		}
		if isVariableMapping {
			switch underlyingValue := underlyingValue.(type) {
			case string:
				*valueString += underlyingValue
			default:
				*valueVariable = underlyingValue
			}
			if *valueString != "" {
				*valueVariable = *valueString
				*valueString = ""
			}
		} else {

			switch underlyingValue := underlyingValue.(type) {
			case string:
				underlyingValue = strings.ToLower(strings.ReplaceAll(underlyingValue, " ", ""))
				if len(*value) == 0 {
					*value = append(*value, underlyingValue)
				} else {
					var result []any
					for _, val := range *value {
						if str, ok := val.(string); ok {
							result = append(result, str+underlyingValue)
						}
					}
					*value = result
				}
			default:
				*value = append(*value, underlyingValue)
			}
		}

	}
	return nil
}

func separateStaticDynamicProperties(resource map[string]any) (map[string]any, map[string]any) {
	var (
		staticProperties  = make(map[string]any)
		dynamicProperties = make(map[string]any)
	)

	for key, value := range resource {
		if strings.HasPrefix(key, "static:") {
			staticProperties[strings.TrimPrefix(key, "static:")] = value
		} else {
			var isDynamic bool = false
			switch value := value.(type) {
			case map[string]any:
				updatedValue := removePrefixFromKeys(value, &isDynamic)
				if isDynamic {
					dynamicProperties[key] = updatedValue
				} else {
					staticProperties[key] = updatedValue
				}
			default:
				if strings.HasPrefix(key, "dynamic:") {
					dynamicProperties[strings.TrimPrefix(key, "dynamic:")] = value
				}
			}

		}
	}
	return staticProperties, dynamicProperties
}

func removePrefixFromKeys(mappedProperties any, isDynamic *bool) any {
	switch value := mappedProperties.(type) {
	case map[string]any:
		result := make(map[string]any)
		for key, val := range value {
			newKey := key
			if strings.HasPrefix(key, "static:") {
				newKey = strings.TrimPrefix(key, "static:")
			} else if strings.HasPrefix(key, "dynamic:") {
				*isDynamic = true
				newKey = strings.TrimPrefix(key, "dynamic:")
			}
			result[newKey] = removePrefixFromKeys(val, isDynamic)
		}
		return result
	default:
		return value
	}
}

func updateVariableMapsBasedOnFilePath(fileContent string, gitClientName string, gitClient any, tfVariablesMapping map[string]any, gitDiff string, commitID string) (bool, error) {
	tenantID, filePath, repoName, fileStatus := "", "", "", ""
	switch gitClientName {
	case GITHUB:
		if githubApiClient, ok := gitClient.(*GithubApiClient); ok {
			tenantID = githubApiClient.TenantID
			filePath = githubApiClient.FilePath
			repoName = githubApiClient.RepoName
			fileStatus = githubApiClient.FileStatus
		}
	case BITBUCKET:
		if bitbucketApiClient, ok := gitClient.(*BitbucketApiClient); ok {
			tenantID = bitbucketApiClient.TenantID
			filePath = bitbucketApiClient.FilePath
			repoName = bitbucketApiClient.RepoName
			fileStatus = bitbucketApiClient.FileStatus
		}

	case GITLAB:
		if gitlabApiClient, ok := gitClient.(*GitlabApiClient); ok {
			tenantID = gitlabApiClient.TenantID
			filePath = gitlabApiClient.FilePath
			repoName = gitlabApiClient.RepoName
			fileStatus = gitlabApiClient.FileStatus
		}
	}

	pathSlice := strings.Split(filePath, "/")
	fileName := pathSlice[len(pathSlice)-1]
	if strings.HasSuffix(fileName, "variables.tf") || strings.HasSuffix(fileName, "var.tf") || strings.HasSuffix(fileName, ".tfvars") || strings.HasSuffix(fileName, ".tfvars.json") || strings.HasSuffix(fileName, "variable.tf") || strings.HasSuffix(fileName, "vars.tf") {
		recentCodeEventsQuery := `{
			"query": {
					"bool": {
						"must": [
							{"match": {"tenantId.keyword": "` + tenantID + `"}},
							{"match": {"repoName.keyword": "` + repoName + `"}}
						]
					}
				}
			}`

		tfVarDocs, err := elastic.ExecuteSearchQuery([]string{elastic.TF_VARIABLES_INDEX}, recentCodeEventsQuery)
		if err != nil {
			logger.Print(logger.ERROR, "Got error fetching variable record for a module", []string{commitID, tenantID}, err)
			return true, err
		}

		var (
			moduleVariablesMap = make(map[string]any)
			bulkUpdateQuery    = ""
		)

		if len(tfVarDocs) > 0 {
			for _, tfVarDoc := range tfVarDocs {

				if variableName, ok := tfVarDoc["varName"].(string); ok {
					if _, ok := tfVariablesMapping[variableName]; !ok {
						if variablesJSONStr, ok := tfVarDoc["variables"].(string); ok {
							variableMap := make(map[string]any)
							err := json.Unmarshal([]byte(variablesJSONStr), &variableMap)
							if err != nil {
								logger.Print(logger.ERROR, "Got error while unmarshaling variables json")
								continue
							}
							moduleVariablesMap[variableName] = variableMap
						}
					}
				}
			}

		} else {
			moduleVariablesMap, err = fetchAndUpdateTfVarFiles(gitClientName, gitClient, commitID, fileStatus, "./", tenantID, repoName)
			if err != nil {
				return true, err
			}

			if moduleVariablesMap == nil {
				return true, nil
			}

		}

		varMap := make(map[string]any)
		if fileStatus == "created" || fileStatus == "deleted" || fileStatus == "modified" {
			if len(tfVarDocs) == 0 && (fileStatus == "created" || fileStatus == "modified") {
				return true, nil
			} else {
				if strings.HasSuffix(fileName, "json") {
					parseVariableJsonBlock(fileContent, varMap, gitDiff, fileStatus, commitID, tenantID)
					if err != nil {
						return true, err
					}
					for key, value := range varMap {
						if value != "" || value != nil {
							if val, ok := moduleVariablesMap[key]; ok {
								if valMap, isMap := val.(map[string]any); isMap {
									if valMapVal, ok := valMap[commitID]; ok {
										if valMapArr, isValMapArr := valMapVal.([]any); isValMapArr {
											valMapArr = append(valMapArr, value)
											valMap[commitID] = valMapArr
										}
									} else {
										valMapArr := make([]any, 0)
										valMapArr = append(valMapArr, value)
										valMap[commitID] = valMapArr

									}
									moduleVariablesMap[key] = valMap
								}
							} else {
								valMap := make(map[any]any)
								valMapValArr := make([]any, 0)
								valMapValArr = append(valMapValArr, value)
								valMap[commitID] = valMapValArr
								moduleVariablesMap[key] = valMap
							}
						}
					}
				} else if strings.Contains(fileName, "variable") || strings.HasSuffix(fileName, "var.tf") {
					updatedVariables := make([]string, 0)
					err := fetchUpdatedVariablesFromGitDiff(gitDiff, fileContent, &updatedVariables, gitClientName, gitClient, tenantID)
					if err != nil {
						return true, err
					}
					if fileStatus == "deleted" {
						_, _, err := getTfBlocksFromFile(fileContent, false, true, varMap, gitClientName, gitClient, moduleVariablesMap, tenantID)
						if err != nil {
							return true, err
						}
					} else if fileStatus == "modified" {
						_, _, err := getTfBlocksFromFile(fileContent, false, true, varMap, gitClientName, gitClient, moduleVariablesMap, tenantID)
						if err != nil {
							return true, err
						}

					} else {
						_, _, err = getTfBlocksFromFile(fileContent, false, true, varMap, gitClientName, gitClient, varMap, tenantID)
						if err != nil {
							return true, err
						}
					}

					for _, key := range updatedVariables {
						if value, ok := varMap[key]; ok {
							if value != "" || value != nil {
								if val, ok := moduleVariablesMap[key]; ok {
									if valMap, isMap := val.(map[string]any); isMap {
										if valMapVal, ok := valMap[commitID]; ok {
											if valMapArr, isValMapArr := valMapVal.([]any); isValMapArr {
												if !contains(valMapArr, value) {
													valMapArr = append(valMapArr, value)
													valMap[commitID] = valMapArr
												}
											}

										} else {
											valMapArr := []any{value}
											valMap[commitID] = valMapArr
										}
										moduleVariablesMap[key] = valMap
									}
								} else {
									valMap := make(map[any]any)
									valMapArr := []any{value}
									valMap[commitID] = valMapArr
									moduleVariablesMap[key] = valMap
								}
							}
						}
					}
				} else {
					if fileStatus == "deleted" {
						err := fetchUpdatedVarForDeletedTFVarFile(gitDiff, varMap, gitClientName, gitClient, tenantID)
						if err != nil {
							return true, err
						}
					} else if fileStatus == "modified" {
						err := fetchUpdatedVarForUpdatedTFVarFile(gitDiff, varMap, gitClientName, gitClient, tenantID)
						if err != nil {
							return true, err
						}
					} else {
						_, _, err = getTfBlocksFromFile(fileContent, false, true, varMap, gitClientName, gitClient, varMap, tenantID)
						if err != nil {
							return true, err
						}
					}
					for key, value := range varMap {
						if value != "" || value != nil {
							if val, ok := moduleVariablesMap[key]; ok {
								if valMap, isMap := val.(map[string]any); isMap {
									if valMapVal, ok := valMap[commitID]; ok {
										if valMapArr, isValMapArr := valMapVal.([]any); isValMapArr {
											if valArr, isArr := value.([]any); isArr {
												for _, v := range valArr {
													if !contains(valMapArr, v) {
														valMapArr = append(valMapArr, v)
													}
												}
												valMap[commitID] = valMapArr
											}
										}
									} else {
										valMapArr := make([]any, 0)
										if valArr, isArr := value.([]any); isArr {
											for _, v := range valArr {
												if !contains(valMapArr, v) {
													valMapArr = append(valMapArr, v)
												}
											}
										}
										valMap[commitID] = valMapArr
									}
									moduleVariablesMap[key] = valMap
								}
							} else {
								valMap := make(map[any]any)
								if valArr, isArr := value.([]any); isArr {
									valMapValArr := make([]any, 0)
									for _, v := range valArr {
										valMapValArr = append(valMapValArr, v)
									}
									valMap[commitID] = valMapValArr
								}
								moduleVariablesMap[key] = valMap
							}
						}
					}
				}
			}

		} else {
			return true, nil
		}
		for key, value := range moduleVariablesMap {
			variablesJsonData, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(value)
			if err != nil {
				logger.Print(logger.ERROR, "Error marshalling variables", err)
				return true, err
			}

			tfVarDoc := TFVarDoc{
				TenantID:     tenantID,
				Variables:    string(variablesJsonData),
				VariableName: key,
				RepoName:     repoName,
				GitClient:    gitClientName,
			}
			tfVarDocID := GenerateCombinedHashID(key, repoName, tenantID)
			updateMetadata := map[string]any{
				"update": map[string]string{
					"_id": tfVarDocID,
				},
			}

			bulkRequestBody := map[string]any{
				"doc": tfVarDoc,
			}
			updateMetadataJSON, err := json.Marshal(updateMetadata)
			if err != nil {
				logger.Print(logger.ERROR, "Error marshalling update metadata", err)
				return true, err
			}

			bulkRequestBodyJSON, err := json.Marshal(bulkRequestBody)
			if err != nil {
				logger.Print(logger.ERROR, "Error marshalling bulk request body", err)
				return true, err
			}

			bulkUpdateQuery += string(updateMetadataJSON) + "\n" + string(bulkRequestBodyJSON) + "\n"
		}

		if err := elastic.BulkDocumentsAPI(tenantID, elastic.TF_VARIABLES_INDEX, bulkUpdateQuery); err != nil {
			return true, err
		}

		logger.Print(logger.INFO, "TF Variables bulk Update API Successful", []string{tenantID})
		return true, nil
	} else {
		return false, nil
	}

}

func fetchUpdatedVarJsonForDeletedFile(gitDiff string, variableMap map[string]any, commitID, tenantID string) error {
	re := regexp.MustCompile(`\{[\s\S]+?\}`)

	matches := re.FindStringSubmatch(gitDiff)
	if len(matches) < 1 {
		logger.Print(logger.ERROR, "No JSON content found in the Git diff for deleted file", []string{commitID, tenantID, gitDiff})
		return nil
	}

	lines := strings.Split(matches[0], "\n")
	jsonContent := ""
	for _, line := range lines {
		if strings.HasPrefix(line, "-") {
			line = strings.TrimPrefix(line, "-")
		}
		jsonContent += line
	}

	var parsedMap map[string]any
	err := json.Unmarshal([]byte(jsonContent), &parsedMap)
	if err != nil {
		logger.Print(logger.ERROR, "Got error unmarshalling json content for deleted file", []string{commitID, tenantID, gitDiff}, err)
		return err
	}

	for key, value := range parsedMap {
		variableMap[key] = value
	}
	return nil
}

func fetchUpdatedVarJsonForUpdatedFile(gitDiff string, variableMap map[string]any, commitID, tenantID string) error {
	lines := strings.Split(gitDiff, "\n")

	var deletedLines string
	var updatedLines []string
	deletedLines += "{ "
	for _, line := range lines {
		if strings.HasPrefix(line, "+") && !strings.HasPrefix(line, "++") {
			str := line[1:]
			updatedLines = append(updatedLines, str)
		}
	}

	for i, line := range updatedLines {
		if i == len(updatedLines)-1 {
			line = strings.TrimSuffix(line, ",")
		}
		deletedLines += line
	}
	deletedLines += "}"

	var parsedMap map[string]any
	err := json.Unmarshal([]byte(deletedLines), &parsedMap)
	if err != nil {
		logger.Print(logger.ERROR, "Got error unmarshalling json content for updated file", []string{commitID, tenantID, gitDiff}, err)
		return err
	}

	for key, value := range parsedMap {
		variableMap[key] = value
	}
	return nil
}

func fetchUpdatedVarForDeletedTFVarFile(gitDiff string, varMap map[string]any, gitClientName string, gitClient any, tenantID string) error {
	lines := strings.Split(gitDiff, "\n")

	var deletedLines []string
	for _, line := range lines {
		if strings.HasPrefix(line, "-") && !strings.HasPrefix(line, "--") {
			deletedLines = append(deletedLines, line[1:])
		}
	}

	varFileContent := strings.Join(deletedLines, "\n")
	variableMapping, varMapCopy := make(map[string]any), make(map[string]any)

	_, _, err := getTfBlocksFromFile(varFileContent, false, true, varMapCopy, gitClientName, gitClient, variableMapping, tenantID)
	if err != nil {
		return err
	}

	for key, value := range varMapCopy {
		varMap[key] = value
	}
	return nil
}

func fetchUpdatedVarForUpdatedTFVarFile(gitDiff string, moduleVariablesMap map[string]any, gitClientName string, gitClient any, tenantID string) error {
	lines := strings.Split(gitDiff, "\n")

	var updatedLines []string
	for _, line := range lines {
		if strings.HasPrefix(line, "+") && !strings.HasPrefix(line, "++") {
			updatedLines = append(updatedLines, line[1:])
		}
	}

	if len(updatedLines) > 0 {
		lastLine := updatedLines[len(updatedLines)-1]

		if strings.Contains(lastLine, ",") {
			updatedLines[len(updatedLines)-1] = strings.Trim(lastLine, ",")
		}
	}

	varFileContent := strings.Join(updatedLines, "\n")

	_, _, err := getTfBlocksFromFile(varFileContent, false, true, moduleVariablesMap, gitClientName, gitClient, moduleVariablesMap, tenantID)
	if err != nil {
		return err
	}
	return nil
}

func processTraversableValue(value any) ([]any, error) {
	var functionParameter []any

	valueType := reflect.TypeOf(value)
	if valueType.Kind() != reflect.Slice && valueType.Kind() != reflect.Map {
		logger.Print(logger.ERROR, "value is not traversable", value)
		return nil, errors.New("error")
	}

	valueElem := reflect.ValueOf(value)

	switch valueType.Kind() {
	case reflect.Slice:
		for i := 0; i < valueElem.Len(); i++ {
			functionParameter = append(functionParameter, valueElem.Index(i).Interface())
		}
	case reflect.Map:
		keys := valueElem.MapKeys()
		for _, key := range keys {
			functionParameter = append(functionParameter, valueElem.MapIndex(key).Interface())
		}
	}

	return functionParameter, nil
}

func extractKeysAndValues(expr *hclsyntax.ObjectConsExpr, tfVariablesMapping map[string]any, isDynamic *bool, gitClientName string, gitClient any, isVariableMapping bool, valueVariable *any, commitIdsUpdatedVars map[string]struct{}, tfResourcePropertiesMap map[string]any, csp string) (map[string]any, error) {
	result := make(map[string]any)
	ctx := &hcl.EvalContext{}

	for _, item := range expr.Items {
		var (
			valueJSON []any
		)
		valueStringJSON := ""

		key, _ := item.KeyExpr.Value(ctx)
		val, diags := item.ValueExpr.Value(ctx)

		expressionNodes, _ := item.ValueExpr.(hclsyntax.Node)

		switch expressionNodes := expressionNodes.(type) {
		case *hclsyntax.TemplateExpr:

			//name = "${var.bucketName}"

			for _, expressionNode := range expressionNodes.Parts {
				switch expressionNode.(type) {
				case *hclsyntax.ConditionalExpr:
					continue
				}
				ctx := &hcl.EvalContext{}
				val, diags := expressionNode.Value(ctx)

				err := parseTerraformFields(&valueJSON, &valueStringJSON, val, diags, tfVariablesMapping, isDynamic, gitClientName, gitClient, isVariableMapping, valueVariable, commitIdsUpdatedVars, tfResourcePropertiesMap, csp)
				if err != nil {
					return nil, err
				}
			}
		default:
			err := parseTerraformFields(&valueJSON, &valueStringJSON, val, diags, tfVariablesMapping, isDynamic, gitClientName, gitClient, isVariableMapping, valueVariable, commitIdsUpdatedVars, tfResourcePropertiesMap, csp)

			if err != nil {
				return nil, err
			}
		}

		if isVariableMapping {
			result[key.AsString()] = *valueVariable
		} else {
			result[key.AsString()] = valueJSON
		}

		*valueVariable = nil
	}
	return result, nil
}

// not being used
// func removeConsistentVarsFromMap(gitDiff string, varMap map[string]any) error {

// 	lines := strings.Split(gitDiff, "\n")

// 	var modifiedVariables []string

// 	variableRegex := regexp.MustCompile(`^\s*variable\s+"([^"]+)"\s*{`)
// 	varName := ""

// 	for _, line := range lines {
// 		if match := variableRegex.FindStringSubmatch(line); match != nil {
// 			varName = match[1]
// 		}

// 		if (strings.HasPrefix(line, "+") && !strings.HasPrefix(line, "++")) || (strings.HasPrefix(line, "-") && !strings.HasPrefix(line, "--")) {
// 			if !slices.Contains(modifiedVariables, varName) {
// 				modifiedVariables = append(modifiedVariables, varName)
// 			}
// 		}
// 	}

// 	for key := range varMap {
// 		if !slices.Contains(modifiedVariables, key) {
// 			delete(varMap, key)
// 		}
// 	}

// 	return nil

// }

func fetchAndUpdateTfVarFiles(gitClientName string, gitClient any, docId, fileStatus, filePath, tenantID, repoName string) (map[string]any, error) {

	variableFiles := make([]VariableFile, 0)

	err := fetchTerraformVariableFiles(gitClientName, gitClient, &variableFiles)
	if err != nil {
		return nil, err
	}

	moduleVariablesMap := make(map[string]any)
	bulkInsertQuery := ""

	for _, variableFile := range variableFiles {

		commitID := "unknown"

		if (fileStatus == "created" || fileStatus == "modified") && variableFile.filePath == filePath {
			commitID = docId
		}
		variableMapping := make(map[string]any)
		if strings.HasSuffix(variableFile.fileName, "json") {

			parseVariableJsonBlock(variableFile.content, variableMapping, "", "", docId, tenantID)
			if err != nil {
				return nil, err
			}
			for key, value := range variableMapping {
				if value != "" {
					if val, ok := moduleVariablesMap[key]; ok {
						if valMap, isMap := val.(map[any]any); isMap {
							if valMapVal, ok := valMap[commitID]; ok {
								if valMapArr, isValMapArr := valMapVal.([]any); isValMapArr {
									valMapArr = append(valMapArr, value)
									valMap[commitID] = valMapArr
								}
							} else {
								valMapArr := make([]any, 0)
								valMapArr = append(valMapArr, value)
								valMap[docId] = valMapArr
							}
							moduleVariablesMap[key] = valMap
						}
					} else {
						valMap := make(map[any]any)
						valMapValArr := make([]any, 0)
						valMapValArr = append(valMapValArr, value)
						valMap[commitID] = valMapValArr
						moduleVariablesMap[key] = valMap
					}
				}
			}
		} else {
			varMap := make(map[string]any)
			if strings.Contains(variableFile.fileName, "variables") || strings.HasSuffix(variableFile.fileName, "var.tf") || strings.Contains(variableFile.fileName, "variable") {
				_, _, err = getTfBlocksFromFile(variableFile.content, false, false, varMap, gitClientName, gitClient, variableMapping, tenantID)
				for key, value := range varMap {
					if value != "" {
						if val, ok := moduleVariablesMap[key]; ok {
							if valMap, isMap := val.(map[any]any); isMap {
								if valMapVal, ok := valMap[commitID]; ok {
									if valMapArr, isValMapArr := valMapVal.([]any); isValMapArr {
										valMapArr = append(valMapArr, value)
										valMap[commitID] = valMapArr
									}

								} else {
									valMapArr := []any{value}
									valMap[commitID] = valMapArr
								}
								moduleVariablesMap[key] = valMap
							}
						} else {
							valMap := make(map[any]any)
							valMapArr := []any{value}
							valMap[commitID] = valMapArr
							moduleVariablesMap[key] = valMap
						}
					}
				}
			} else {
				// for tfvars

				_, _, err = getTfBlocksFromFile(variableFile.content, false, true, varMap, gitClientName, gitClient, variableMapping, tenantID)
				if err != nil {
					return nil, err
				}
				for key, value := range varMap {
					if value != "" {
						if val, ok := moduleVariablesMap[key]; ok {
							if valMap, isMap := val.(map[any]any); isMap {
								if valMapVal, ok := valMap[commitID]; ok {
									if valMapArr, isValMapArr := valMapVal.([]any); isValMapArr {
										if valArr, isArr := value.([]any); isArr {
											for _, v := range valArr {
												valMapArr = append(valMapArr, v)
											}
											valMap[commitID] = valMapArr
										}
									}

								} else {
									valMapArr := make([]any, 0)
									if valArr, isArr := value.([]any); isArr {
										for _, v := range valArr {
											valMapArr = append(valMapArr, v)
										}
									}
									valMap[commitID] = valMapArr
								}
								moduleVariablesMap[key] = valMap
							}
						} else {
							valMap := make(map[any]any)
							if valArr, isArr := value.([]any); isArr {
								valMapValArr := make([]any, 0)
								for _, v := range valArr {
									valMapValArr = append(valMapValArr, v)
								}
								valMap[commitID] = valMapValArr
							}
							moduleVariablesMap[key] = valMap
						}
					}
				}
			}
			if err != nil {
				return nil, err
			}
		}
	}

	if len(moduleVariablesMap) == 0 {
		return nil, nil
	}

	for key, value := range moduleVariablesMap {
		var jsonMarshal = jsoniter.ConfigCompatibleWithStandardLibrary
		variablesJsonData, err := jsonMarshal.Marshal(value)
		if err != nil {
			return nil, err
		}

		tfVarDoc := TFVarDoc{
			TenantID:     tenantID,
			Variables:    string(variablesJsonData),
			VariableName: key,
			RepoName:     repoName,
			GitClient:    gitClientName,
		}
		tfVarDocID := GenerateCombinedHashID(key, repoName, tenantID)
		tfVariablesInsertDocMetadata := `{"index": {"_id": "` + tfVarDocID + `"}}`
		tfVarInsertDoc, err := json.Marshal(tfVarDoc)
		if err != nil {
			logger.Print(logger.ERROR, "Got error marshalling document", err)
			return nil, err
		}

		bulkInsertQuery = bulkInsertQuery + tfVariablesInsertDocMetadata + "\n" + string(tfVarInsertDoc) + "\n"

	}

	if err := elastic.BulkDocumentsAPI(tenantID, elastic.TF_VARIABLES_INDEX, bulkInsertQuery); err != nil {
		return nil, err
	}

	logger.Print(logger.INFO, "TF Variables bulk Insert API Successful", []string{tenantID})

	time.Sleep(1 * time.Second)
	return moduleVariablesMap, nil
}

func findAttrNameAlias(name, resourceType, csp string) string {
	if resourceType != "" && csp != "" {
		if val, ok := cspToMapForResourceProperties[csp][name]; ok {
			return val
		} else if val, ok := cspToMapForResourceProperties[csp][resourceType+":"+name]; ok {
			return val
		}
	}
	return name
}

func fetchTFCommitsFromVarFile(gitClientName, tenantID, repoName string, tfVariablesMapping map[string]any, varDocID string, commitTime string) error {
	commitDateTime, err := elastic.ParseDateTime(commitTime)
	if err != nil {
		logger.Print(logger.ERROR, "Error parse date time", commitTime)
		return err
	}

	//Update Commit of Last 20 days
	commitDateTime = commitDateTime.Add(-2 * 24 * time.Hour)

	aggregatedTfCommitsQuery := `{
		"query": {
			"bool": {
				"must": [
					{"match": {"gitClient": "` + gitClientName + `"}},
					{"match": {"tenantId.keyword": "` + tenantID + `"}},
					{"match": {"repoName.keyword": "` + repoName + `"}},
					{"range":  {"commitTime":{"gt": "` + elastic.DateTime(commitDateTime) + `"}}}            
				]
			}
		},
		"from": 0,
		"size": 0,
		"aggs": {
			"byFilePath": {
				"terms": {
					"field": "filePath.keyword",
					"size": 10000
				},
				"aggs": {           
					"byResourceType": {
						"terms": {
							"field": "resourceType.keyword"
						},
						"aggs": {
							"byResourceLabelName": {
								"terms": {
									"field": "resourceLabelName.keyword"
								},						                   
								"aggs": {
									"matchedDoc": {
										"top_hits": {
											"size": 1,
											"sort": {
												"commitTime": {
													"order": "desc"
												}
											},
											"_source": true
										}
									}
								}	
							}
						}
					}
				}
			}
		}
	}`
	commitsAggregation, err := elastic.ExecuteSearchForAggregation([]string{elastic.TF_COMMITS_INDEX}, aggregatedTfCommitsQuery)
	if err != nil {
		return err
	}

	changedTfResources := make([]TFCommitDoc, 0)
	if filePathAggregation, ok := commitsAggregation["byFilePath"].(map[string]any); ok {

		if filePathBuckets, ok := filePathAggregation["buckets"].([]any); ok {

			for _, filePathBucket := range filePathBuckets {

				if filePathBucket, ok := filePathBucket.(map[string]any); ok {

					if resourceTypeAggregation, ok := filePathBucket["byResourceType"].(map[string]any); ok {

						if resourceTypeBuckets, ok := resourceTypeAggregation["buckets"].([]any); ok {

							for _, resourceTypeBucket := range resourceTypeBuckets {

								if resourceTypeBucket, ok := resourceTypeBucket.(map[string]any); ok {

									if resourceLabelNameAggregation, ok := resourceTypeBucket["byResourceLabelName"].(map[string]any); ok {

										if resourceLabelNameBuckets, ok := resourceLabelNameAggregation["buckets"].([]any); ok {

											for _, resourceLabelNameBucket := range resourceLabelNameBuckets {
												if resourceLabelNameMatchedDoc, ok := resourceLabelNameBucket.(map[string]any)["matchedDoc"]; ok {

													if resourceLabelNameMatchedDocHits, ok := resourceLabelNameMatchedDoc.(map[string]any)["hits"]; ok {

														if resourceLabelNameInnerHits, ok := resourceLabelNameMatchedDocHits.(map[string]any)["hits"]; ok {

															if resourceLabelNameInnerHitsArr, ok := resourceLabelNameInnerHits.([]any); ok {

																for _, resourceLabelNameInnerHit := range resourceLabelNameInnerHitsArr {

																	if commitDocs, ok := resourceLabelNameInnerHit.(map[string]any)["_source"]; ok {

																		if commitDocsMap, ok := commitDocs.(map[string]any); ok {

																			if resourceTemplate, ok := commitDocsMap["resourceTemplate"].(string); ok {

																				if commitID, ok := commitDocsMap["commitDocId"].(string); ok {

																					if gitClientName, ok := commitDocsMap["gitClient"].(string); ok {

																						if commitRepoName, ok := commitDocsMap["repoName"].(string); ok {

																							if commitFilePath, ok := commitDocsMap["filePath"].(string); ok {

																								var gitClient any

																								switch gitClientName {
																								case GITHUB:
																									gitClient = &GithubApiClient{
																										RepoName: commitRepoName,
																										TenantID: tenantID,
																										FilePath: commitFilePath,
																									}
																								case BITBUCKET:
																									gitClient = &BitbucketApiClient{
																										FilePath: commitFilePath,
																										TenantID: tenantID,
																										RepoName: commitRepoName,
																									}
																								case GITLAB:
																									gitClient = &GitlabApiClient{
																										FilePath: commitFilePath,
																										TenantID: tenantID,
																										RepoName: repoName,
																									}
																								}

																								blocks, csp, err := getTfBlocksFromFile(resourceTemplate, false, false, tfVariablesMapping, gitClientName, gitClientName, nil, tenantID)
																								if err != nil {
																									return err
																								}

																								tfToCSPResourceType := cspToMapForResourceType[csp]
																								tfResourcePropertiesMap := make(map[string]any)
																								for _, block := range blocks {
																									staticPropertiesJsonDataStr, dynamicPropertiesJsonDataStr, commitIdsUpdatedVarsJsonStr, variablesJsonDataStr := "", "", "", ""
																									commitIdsUpdatedVars := make(map[string]struct{})
																									result := make(map[string]any)
																									if block.IsDeleted {
																										result["IsDeleted"] = true
																									}
																									err := mapBlocks(block.SyntaxBlock.Body, result, tfVariablesMapping, false, gitClientName, gitClient, commitIdsUpdatedVars, block.Name, csp, block.Label, tfResourcePropertiesMap, nil, nil, tenantID)
																									if err != nil {
																										logger.Print(logger.ERROR, "Error parsing tf block arguments")
																										continue
																									}

																									if _, ok := commitIdsUpdatedVars[varDocID]; !ok {
																										continue
																									}

																									if len(result) != 0 {
																										if len(commitIdsUpdatedVars) != 0 {

																											commitIdsUpdatedVarsJson, err := json.Marshal(commitIdsUpdatedVars)
																											if err != nil {
																												logger.Print(logger.ERROR, "Erros Marshalling commitIdsUpdatedVars")
																												continue
																											}
																											commitIdsUpdatedVarsJsonStr = string(commitIdsUpdatedVarsJson)
																										}
																										if len(tfVariablesMapping) != 0 {
																											variablesJsonData, err := json.Marshal(tfVariablesMapping)
																											if err != nil {
																												logger.Print(logger.ERROR, "Got error marshalling variables map", err)
																												continue
																											}
																											variablesJsonDataStr = string(variablesJsonData)
																										}
																										staticProperties, dynamicProperties := separateStaticDynamicProperties(result)
																										if len(staticProperties) != 0 {
																											staticPropertiesJsonData, err := json.Marshal(staticProperties)
																											if err != nil {
																												logger.Print(logger.ERROR, "Got error marshalling static properties", err)
																												continue
																											}
																											staticPropertiesJsonDataStr = string(staticPropertiesJsonData)
																										}

																										if len(dynamicProperties) != 0 {
																											dynamicPropertiesJsonData, err := json.Marshal(dynamicProperties)
																											if err != nil {
																												logger.Print(logger.ERROR, "Got error marshalling dynamic properties", err)
																												continue
																											}
																											dynamicPropertiesJsonDataStr = string(dynamicPropertiesJsonData)
																										}

																										resourceType := block.Name
																										val, ok := tfToCSPResourceType[block.Name]
																										if ok {
																											resourceType = val
																										}
																										resourceTfCommit := TFCommitDoc{
																											DocID:                        commitID,
																											ResourceLabelName:            block.Label,
																											ResourceType:                 resourceType,
																											StaticResourceProperties:     staticPropertiesJsonDataStr,
																											DynamicResourceProperties:    dynamicPropertiesJsonDataStr,
																											Variables:                    variablesJsonDataStr,
																											ServiceCode:                  csp,
																											TenantID:                     tenantID,
																											GitClient:                    gitClientName,
																											CommitTime:                   commitTime,
																											VariableModifiedCommitDocIds: commitIdsUpdatedVarsJsonStr,
																											FilePath:                     commitFilePath,
																											UnprocessedResourceType:      block.Name,
																											ResourceTemplate:             block.ResourceContent,
																											RepoName:                     commitRepoName,
																										}
																										changedTfResources = append(changedTfResources, resourceTfCommit)
																									}
																								}
																							}
																						}
																					}
																				}
																			}
																		}
																	}
																}
															}
														}
													}
												}
											}
										}
									}
								}
							}
						}
					}
				}

			}
		}
	}

	for _, changedTfResource := range changedTfResources {
		logger.Print(logger.INFO, "Inserting", changedTfResource.StaticResourceProperties, changedTfResource.DynamicResourceProperties)
		if _, err = elastic.InsertDocument(tenantID, elastic.TF_COMMITS_INDEX, changedTfResource); err != nil {
			continue
		}
	}
	return nil
}

func fetchFilePath(provider string, gitClient any) string {
	switch provider {
	case GITHUB:
		if githubApiClient, ok := gitClient.(*GithubApiClient); ok {
			return githubApiClient.FilePath
		}
	case BITBUCKET:
		if bitbucketApiClient, ok := gitClient.(*BitbucketApiClient); ok {
			return bitbucketApiClient.FilePath
		}

	case GITLAB:
		if gitlabApiClient, ok := gitClient.(*GitlabApiClient); ok {
			return gitlabApiClient.FilePath
		}
	default:
		logger.Print(logger.ERROR, "Provider not supported", provider)
	}

	return ""
}

func updateCommitsUsingExtFiles(gitClientName string, gitClient any, tfVariablesMapping map[string]any, commitTime string) (bool, error) {
	tenantID, filePath, repoName := "", "", ""
	switch gitClientName {
	case GITHUB:
		if githubApiClient, ok := gitClient.(*GithubApiClient); ok {
			tenantID = githubApiClient.TenantID
			filePath = githubApiClient.FilePath
			repoName = githubApiClient.RepoName
		}
	case BITBUCKET:
		if bitbucketApiClient, ok := gitClient.(*BitbucketApiClient); ok {
			tenantID = bitbucketApiClient.TenantID
			filePath = bitbucketApiClient.FilePath
			repoName = bitbucketApiClient.RepoName
		}

	case GITLAB:
		if gitlabApiClient, ok := gitClient.(*GitlabApiClient); ok {
			tenantID = gitlabApiClient.TenantID
			filePath = gitlabApiClient.FilePath
			repoName = gitlabApiClient.RepoName
		}
	}

	pathSlice := strings.Split(filePath, "/")
	fileName := pathSlice[len(pathSlice)-1]
	if strings.HasSuffix(fileName, ".sh") {
		aggregatedTfCommitsQuery := `{
			"query": {
				"bool": {
					"must": [
						{"match": {"gitClient": "` + gitClientName + `"}},
						{"match": {"tenantId.keyword": "` + tenantID + `"}},
						{"match": {"repoName.keyword": "` + repoName + `"}},
						{"match": {"externalFiles.keyword": "` + filePath + `"}}      
					]
				}
			},
			"from": 0,
			"size": 0,
			"aggs": {
				"byFilePath": {
					"terms": {
						"field": "filePath.keyword"
					},
					"aggs": {           
						"byResourceType": {
							"terms": {
								"field": "resourceType.keyword"
							},
							"aggs": {
								"byResourceLabelName": {
									"terms": {
										"field": "resourceLabelName.keyword"
									},						                   
									"aggs": {
										"matchedDoc": {
											"top_hits": {
												"size": 1,
												"sort": {
													"commitTime": {
														"order": "desc"
													}
												},
												"_source": true
											}
										}
									}
									
								}
							}
						}
					}
				}
			}
		}`
		commitsAggregation, err := elastic.ExecuteSearchForAggregation([]string{elastic.TF_COMMITS_INDEX}, aggregatedTfCommitsQuery)
		if err != nil {
			return true, err
		}

		changedTfResources := make([]TFCommitDoc, 0)
		if filePathAggregation, ok := commitsAggregation["byFilePath"].(map[string]any); ok {

			if filePathBuckets, ok := filePathAggregation["buckets"].([]any); ok {

				for _, filePathBucket := range filePathBuckets {

					if filePathBucket, ok := filePathBucket.(map[string]any); ok {

						if resourceTypeAggregation, ok := filePathBucket["byResourceType"].(map[string]any); ok {

							if resourceTypeBuckets, ok := resourceTypeAggregation["buckets"].([]any); ok {

								for _, resourceTypeBucket := range resourceTypeBuckets {

									if resourceTypeBucket, ok := resourceTypeBucket.(map[string]any); ok {

										if resourceLabelNameAggregation, ok := resourceTypeBucket["byResourceLabelName"].(map[string]any); ok {

											if resourceLabelNameBuckets, ok := resourceLabelNameAggregation["buckets"].([]any); ok {

												for _, resourceLabelNameBucket := range resourceLabelNameBuckets {
													if resourceLabelNameMatchedDoc, ok := resourceLabelNameBucket.(map[string]any)["matchedDoc"]; ok {

														if resourceLabelNameMatchedDocHits, ok := resourceLabelNameMatchedDoc.(map[string]any)["hits"]; ok {

															if resourceLabelNameInnerHits, ok := resourceLabelNameMatchedDocHits.(map[string]any)["hits"]; ok {

																if resourceLabelNameInnerHitsArr, ok := resourceLabelNameInnerHits.([]any); ok {

																	for _, resourceLabelNameInnerHit := range resourceLabelNameInnerHitsArr {

																		if commitDocs, ok := resourceLabelNameInnerHit.(map[string]any)["_source"]; ok {

																			if commitDocsMap, ok := commitDocs.(map[string]any); ok {

																				if resourceTemplate, ok := commitDocsMap["resourceTemplate"].(string); ok {

																					if commitID, ok := commitDocsMap["commitDocId"].(string); ok {

																						if gitClientName, ok := commitDocsMap["gitClient"].(string); ok {

																							if commitRepoName, ok := commitDocsMap["repoName"].(string); ok {

																								if commitFilePath, ok := commitDocsMap["filePath"].(string); ok {

																									blocks, csp, err := getTfBlocksFromFile(resourceTemplate, false, false, tfVariablesMapping, gitClientName, gitClientName, nil, tenantID)
																									if err != nil {
																										return true, err
																									}

																									tfToCSPResourceType := cspToMapForResourceType[csp]
																									tfResourcePropertiesMap := make(map[string]any)
																									for _, block := range blocks {
																										staticPropertiesJsonDataStr, dynamicPropertiesJsonDataStr, commitIdsUpdatedVarsJsonStr, variablesJsonDataStr, priorityConfigsJsonStr := "", "", "", "", ""
																										commitIdsUpdatedVars := make(map[string]struct{})
																										result := make(map[string]any)
																										externalFiles := make(map[string]struct{})
																										priorityConfigs := make(map[string]any)

																										if block.IsDeleted {
																											result["IsDeleted"] = true
																										}
																										err := mapBlocks(block.SyntaxBlock.Body, result, tfVariablesMapping, false, gitClientName, gitClient, commitIdsUpdatedVars, block.Name, csp, block.Label, tfResourcePropertiesMap, priorityConfigs, externalFiles, tenantID)
																										if err != nil {
																											logger.Print(logger.ERROR, "Error parsing tf block arguments")
																											continue
																										}

																										if len(result) != 0 {
																											if len(commitIdsUpdatedVars) != 0 {

																												commitIdsUpdatedVarsJson, err := json.Marshal(commitIdsUpdatedVars)
																												if err != nil {
																													logger.Print(logger.ERROR, "Erros Marshalling commitIdsUpdatedVars")
																													continue
																												}
																												commitIdsUpdatedVarsJsonStr = string(commitIdsUpdatedVarsJson)
																											}
																											if len(priorityConfigs) != 0 {
																												priorityConfigsJson, err := json.Marshal(priorityConfigs)
																												if err != nil {
																													logger.Print(logger.ERROR, "Erros Marshalling commitIdsUpdatedVars")
																													continue
																												}
																												priorityConfigsJsonStr = string(priorityConfigsJson)
																											}
																											var externalFilesArr []string
																											if len(externalFiles) != 0 {
																												for key := range externalFiles {
																													externalFilesArr = append(externalFilesArr, key)
																												}
																											}
																											if len(tfVariablesMapping) != 0 {
																												variablesJsonData, err := json.Marshal(tfVariablesMapping)
																												if err != nil {
																													logger.Print(logger.ERROR, "Got error marshalling variables map", err)
																													continue
																												}
																												variablesJsonDataStr = string(variablesJsonData)
																											}
																											staticProperties, dynamicProperties := separateStaticDynamicProperties(result)
																											if len(staticProperties) != 0 {
																												staticPropertiesJsonData, err := json.Marshal(staticProperties)
																												if err != nil {
																													logger.Print(logger.ERROR, "Got error marshalling static properties", err)
																													continue
																												}
																												staticPropertiesJsonDataStr = string(staticPropertiesJsonData)
																											}

																											if len(dynamicProperties) != 0 {
																												dynamicPropertiesJsonData, err := json.Marshal(dynamicProperties)
																												if err != nil {
																													logger.Print(logger.ERROR, "Got error marshalling dynamic properties", err)
																													continue
																												}
																												dynamicPropertiesJsonDataStr = string(dynamicPropertiesJsonData)
																											}

																											resourceType := block.Name
																											val, ok := tfToCSPResourceType[block.Name]
																											if ok {
																												resourceType = val
																											}
																											resourceTfCommit := TFCommitDoc{
																												DocID:                        commitID,
																												ResourceLabelName:            block.Label,
																												ResourceType:                 resourceType,
																												StaticResourceProperties:     staticPropertiesJsonDataStr,
																												DynamicResourceProperties:    dynamicPropertiesJsonDataStr,
																												Variables:                    variablesJsonDataStr,
																												ServiceCode:                  csp,
																												TenantID:                     tenantID,
																												GitClient:                    gitClientName,
																												CommitTime:                   commitTime,
																												VariableModifiedCommitDocIds: commitIdsUpdatedVarsJsonStr,
																												FilePath:                     commitFilePath,
																												UnprocessedResourceType:      block.Name,
																												ResourceTemplate:             block.ResourceContent,
																												RepoName:                     commitRepoName,
																												PriorityConfigs:              priorityConfigsJsonStr,
																												ExternalFiles:                externalFilesArr,
																											}
																											changedTfResources = append(changedTfResources, resourceTfCommit)
																										}
																									}
																								}
																							}
																						}
																					}
																				}
																			}
																		}
																	}
																}
															}
														}
													}
												}
											}
										}
									}
								}
							}
						}
					}

				}
			}
		}

		for _, changedTfResource := range changedTfResources {
			logger.Print(logger.INFO, "Inserting", changedTfResource.StaticResourceProperties, changedTfResource.DynamicResourceProperties)
			if _, err = elastic.InsertDocument(tenantID, elastic.TF_COMMITS_INDEX, changedTfResource); err != nil {
				continue
			}
		}

	} else {
		return false, nil
	}
	return true, nil
}

func mapDiffToVariableBlocks(tfResourceBlocks []ResourceBlock, gitDiff string) ([]ResourceBlock, error) {
	var (
		additionLineNumber        int = -999
		deletionLineNumber        int = -999
		err                       error
		modifiedResources         []ResourceBlock
		insertions                int
		deletions                 int
		deletedBlock              string
		currentVariableName       string
		braceCount                int
		isBraceOpenFound          bool
		uniqueModifiedVariableMap = make(map[string]any)
	)

	diffLines := strings.Split(gitDiff, "\n")

	for _, diffLine := range diffLines {
		re := regexp.MustCompile(`@@ -(\d+),\d+ \+(\d+),\d+ @@`)
		match := re.FindStringSubmatch(diffLine)

		if len(match) == 3 {
			isBraceOpenFound = false
			deletionLineNumber, err = strconv.Atoi(match[1])
			if err != nil {
				logger.Print(logger.ERROR, "Error covnerting string line number to int", match[1], err)
				return nil, err
			}
			additionLineNumber, err = strconv.Atoi(match[2])
			if err != nil {
				logger.Print(logger.ERROR, "Error covnerting string line number to int", match[2], err)
				return nil, err
			}

			deletionLineNumber -= 1
			additionLineNumber -= 1

			insertions, deletions = 0, 0
			if strings.Contains(diffLine, "variable") {
				rscName := extractVariableInfo(diffLine, additionLineNumber, tfResourceBlocks)
				if rscName != "" {
					currentVariableName = rscName
				}
			}
			continue

		}

		if strings.Contains(diffLine, "variable") {
			rscName := extractVariableInfo(diffLine, additionLineNumber, tfResourceBlocks)
			if rscName != "" {
				currentVariableName = rscName
			}
		}

		if (strings.HasPrefix(diffLine, "+") || strings.HasPrefix(diffLine, "-")) && (additionLineNumber > -999 && deletionLineNumber > -999) {
			var modifiedTfBlock *ResourceBlock
			if strings.HasPrefix(diffLine, "+") && !strings.HasPrefix(diffLine, "++") {
				additionLineNumber++
				if diffLine == "+" || diffLine == "" {
					insertions++
					continue
				}
				if deletedBlock != "" && strings.Contains(diffLine, "}") && braceCount == 0 && isBraceOpenFound {
					deletedBlock = ""
				}

				modifiedTfBlock = findVarBlockForLine(tfResourceBlocks, additionLineNumber, currentVariableName, uniqueModifiedVariableMap)
				insertions++
			} else if strings.HasPrefix(diffLine, "-") && !strings.HasPrefix(diffLine, "--") {
				deletionLineNumber++
				if diffLine == "-" || diffLine == "" {
					deletions++
					continue
				}
				if strings.Contains(diffLine, "variable") || deletedBlock != "" {
					if strings.Contains(diffLine, "{") {
						braceCount++
						isBraceOpenFound = true
					} else if strings.Contains(diffLine, "}") {
						braceCount--
					}

					deletedBlock += (diffLine[1:] + "\n")
				}
				if deletedBlock != "" && strings.Contains(diffLine, "}") && braceCount == 0 {
					deletedBlock = ""
				}
				modifiedTfBlock = findVarBlockForLine(tfResourceBlocks, deletionLineNumber+insertions-deletions, currentVariableName, uniqueModifiedVariableMap)
				deletions++
			}
			if modifiedTfBlock != nil {
				uniqueModifiedVariableMap[modifiedTfBlock.Name] = struct{}{}
				modifiedResources = append(modifiedResources, *modifiedTfBlock)
			}
		} else {
			if additionLineNumber > -999 && deletionLineNumber > -999 {
				additionLineNumber++
				deletionLineNumber++
			}
		}
	}
	return modifiedResources, nil
}

func extractVariableInfo(input string, lineNumber int, tfResourceBlocks []ResourceBlock) string {
	re := regexp.MustCompile(`variable.*?"([^"]+)"`)

	matches := re.FindStringSubmatch(input)

	if len(matches) == 2 {
		matches[1] = strings.TrimSuffix(matches[1], `\`)
		return matches[1]
	} else if lineNumber != -1 && tfResourceBlocks != nil {

		re := regexp.MustCompile(`resource.*?"([^"]+)"`)

		matches := re.FindStringSubmatch(input)

		if len(matches) == 2 {
			rscType, rscLabelName := findResourceNameAndTypeFromLineNumber(tfResourceBlocks, lineNumber)
			if rscType != "" && rscLabelName != "" {
				return rscLabelName
			}
		}
	}

	return ""
}

func fetchUpdatedVariablesFromGitDiff(gitDiff string, fileContent string, updatedVariables *[]string, gitClientName string, gitClient any, tenantID string) error {
	resourceBlocks, _, err := getTfBlocksFromFile(fileContent, false, false, nil, gitClientName, gitClient, nil, tenantID)
	if err != nil {
		return err
	}
	updateVarBlocks, err := mapDiffToVariableBlocks(resourceBlocks, gitDiff)
	if err != nil {
		return err
	}
	for _, updatedVarBlock := range updateVarBlocks {
		*updatedVariables = append(*updatedVariables, updatedVarBlock.Name)
	}
	return nil
}

func ExtractRepoNameFromGitUrl(source, gitClientName string, gitClient any) (repoName string) {
	switch gitClientName {
	case GITHUB:
		if githubApiClient, ok := gitClient.(*GithubApiClient); ok {
			repoName = githubApiClient.RepoName
		}
	case GITLAB:
		if gitlabApiClient, ok := gitClient.(*GitlabApiClient); ok {
			repoName = gitlabApiClient.RepoName
		}
	case BITBUCKET:
		if bitbucketApiClient, ok := gitClient.(*BitbucketApiClient); ok {
			repoName = bitbucketApiClient.RepoName
		}
	default:
		logger.Print(logger.INFO, "Git Provider not supported", gitClientName)
	}

	if strings.Contains(source, "bitbucket.org") {
		parts := strings.Split(source, "//?ref")
		if len(parts) > 0 {
			cleanedSource := parts[0]
			cleanedSource = strings.TrimSuffix(cleanedSource, "/")
			repoName = filepath.Base(cleanedSource)
			if strings.Contains(repoName, ".git") {
				parts := strings.Split(repoName, ".git")
				repoName = parts[0]
			}
		}
	} else {
		parts := strings.Split(source, ".git")

		if len(parts) == 1 {
			return repoName
		}

		beforeGit := parts[0]
		pathParts := strings.Split(beforeGit, "/")
		for i := len(pathParts) - 1; i >= 0; i-- {
			if pathParts[i] != "" {
				return pathParts[i]
			}
		}
	}

	return repoName
}

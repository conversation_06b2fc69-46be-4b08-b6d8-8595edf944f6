package common

import (
	"fmt"
	"strconv"
	"strings"
	"time"
)

var MAX_TIME = time.Date(2999, 01, 01, 00, 00, 00, 000000000, time.UTC)

var timeFormats = []string{
	time.RFC3339,
	"2006-01-02T15:04:05.000-0700",
	"2006-01-02T15:04:05.000Z",
	"Jan 2, 2006 03:04 PM MST",
	"2006-01-02",
	"2006-01-02 15:04:05",
	"2006-01-02T15:04:05",
	"2006-01-02T15:04:05Z07:00",
	"2006-01-02T15:04:05.000Z07:00",
	"2006-01-02 15:04",
	"2006-01-02 15:04:05.000",
	"2006-01-02 15:04:05 MST",
	"2006/01/02",
	"2006-1-2",
	"2006/1/2",
	"01/02/2006",
	"01/02/2006 15:04",
	"01/02/2006 15:04:05",
	"1/2/2006",
	"1/2/2006 15:04",
	"1/2/2006 15:04:05",
	"02/01/2006",
	"02/01/2006 15:04",
	"2/1/2006",
	"2/1/2006 15:04:05",
	"01-02-2006",
	"01-02-2006 15:04",
	"01-02-2006 15:04:05",
	"02 Jan 2006",
	"02 Jan 2006 15:04",
	"02 Jan 2006 15:04:05",
	"2 Jan 2006",
	"2 Jan 2006 15:04",
	"2 Jan 2006 15:04:05",
	"02 Jan 06",
	"02 Jan 06 15:04",
	"02 Jan 06 15:04:05",
	time.RFC3339Nano,
	time.RFC1123,
	time.RFC1123Z,
	time.RFC822,
	time.RFC822Z,
	time.RFC850,
	time.ANSIC,
	time.UnixDate,
	time.RubyDate,
}

func TryParseTime(value any) (time.Time, bool) {

	var s string

	if valString, ok := value.(string); ok {
		s = valString
	} else if valInt, ok := value.(int); ok {
		s = strconv.Itoa(valInt)
	} else if valFloat, ok := value.(float64); ok {
		valInt := int64(valFloat)
		s = fmt.Sprintf("%d", valInt)
	} else {
		return time.Time{}, false
	}

	s = strings.TrimSpace(s)

	if ts, err := strconv.ParseInt(s, 10, 64); err == nil {
		switch {
		case len(s) == 10:
			return time.Unix(ts, 0), true // epoch seconds
		case len(s) == 13:
			return time.UnixMilli(ts), true // epoch milliseconds
		}
	}

	for _, layout := range timeFormats {
		if t, err := time.Parse(layout, s); err == nil {
			return t, true
		}
	}

	return time.Time{}, false
}

package jirautils

type JiraTicketContext struct {
	Resources []JiraResource `json:"Resources"`
}

type JiraContextBucket struct {
	Key        string `json:"key"`
	ByResource struct {
		Hits struct {
			Hits []struct {
				Source JiraResource `json:"_source"`
			} `json:"hits"`
		} `json:"hits"`
	} `json:"resource_details"`
}

type JiraResource struct {
	ID                string   `json:"id"`
	IssueDocID        string   `json:"issueDocId"`
	TenantID          string   `json:"tenantId"`
	AccountID         string   `json:"accountId"`
	InsertTime        string   `json:"insertTime"`
	Deleted           bool     `json:"deleted"`
	Assignee          string   `json:"assignee"`
	EntityType        string   `json:"entityType"`
	Region            string   `json:"region"`
	ServiceID         string   `json:"serviceId"`
	EntityID          string   `json:"entityId"`
	Environment       string   `json:"environment"`
	Deployment        []string `json:"deployment"`
	OSType            string   `json:"osType"`
	DataSensitivity   []string `json:"dataSensitivity"`
	Applications      []string `json:"applications"`
	Software          []string `json:"softwareComponents"`
	AdditionalDetails string   `json:"additionalDetails"`
}

package jira

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/common/jira"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
	"github.com/precize/transport"
)

const (
	MAX_RECORDS = 10000
)

func processJiraDataCenterIssues(tenantID string, jiraEnv tenant.JiraEnvironment, jiraStartTime, jiraEndTime time.Time) error {

	var (
		issueSearchUrl = jiraEnv.URL + "/rest/api/2/search"
		bearerToken    = strings.TrimSpace(jiraEnv.Token)
		maxResults     = 50
		fieldIdToName  = make(map[string]string)
	)

	projects, err := getSelectedJiraDataCenterProjects(tenantID, jiraEnv)
	if err != nil {
		return err
	}

	logger.Print(logger.INFO, "Fetching Data Center Jira Ops issues from "+common.DateTime(jiraStartTime)+" to "+common.DateTime(jiraEndTime), []string{tenantID}, jiraEnv.ID)

	for _, project := range projects {

		startAt := 0

		for {

			issuesResp, err := transport.SendRequest(
				"GET",
				issueSearchUrl,
				map[string]string{
					"jql":        "project = " + project.ID + " AND updated > '" + formatJiraTimeForQuery(jiraStartTime) + "' AND updated < '" + formatJiraTimeForQuery(jiraEndTime) + "'",
					"startAt":    strconv.Itoa(startAt),
					"maxResults": strconv.Itoa(maxResults),
				},
				map[string]string{"Authorization": "Bearer " + bearerToken},
				nil,
			)
			if err != nil {
				break
			}

			var jiraIssueSearchResp common.JiraDataCenterIssueSearchResponse

			if err = json.Unmarshal(issuesResp, &jiraIssueSearchResp); err != nil {
				logger.Print(logger.ERROR, "Failed to unmarshal", []string{tenantID}, err)
				break
			}

			var jiraFields jiraIssueFieldsResponse

			if err = json.Unmarshal(issuesResp, &jiraFields); err != nil {
				logger.Print(logger.ERROR, "Failed to unmarshal", []string{tenantID}, err)
				break
			}

			if len(jiraIssueSearchResp.Issues) <= 0 {
				break
			}

			startAt = startAt + maxResults

			if len(jiraIssueSearchResp.Issues) > 0 {
				fieldQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"type.keyword":"field"}},{"match":{"deleted":"false"}}]}}}`
				jiraFieldDocs, err := elastic.ExecuteSearchQuery([]string{elastic.JIRA_DATA_INDEX}, fieldQuery)
				if err != nil {
					return err
				}

				for _, jiraFieldDoc := range jiraFieldDocs {
					if accountId, ok := jiraFieldDoc["accountId"].(string); ok {
						if jiraID, ok := jiraFieldDoc["jiraId"].(string); ok {
							if name, ok := jiraFieldDoc["name"].(string); ok {
								fieldIdToName[accountId+"/"+jiraID] = name
							}
						}
					}
				}
			}

			for i, issue := range jiraIssueSearchResp.Issues {

				issueDescription := issue.Fields.Description
				assignee := issue.Fields.Assignee.EmailAddress
				reporter := issue.Fields.Reporter.EmailAddress

				if len(assignee) <= 0 {
					assignee = issue.Fields.Assignee.Name
					if len(assignee) <= 0 {
						assignee = issue.Fields.Assignee.DisplayName
					}
				}

				if len(reporter) <= 0 {
					reporter = issue.Fields.Reporter.Name
					if len(reporter) <= 0 {
						reporter = issue.Fields.Reporter.DisplayName
					}
				}

				createdTime, err := common.ConvertTime(issue.Fields.Created, JIRA_TIME_FORMAT, elastic.DATE_FORMAT)
				if err != nil {
					continue
				}

				modifiedTime, err := common.ConvertTime(issue.Fields.Updated, JIRA_TIME_FORMAT, elastic.DATE_FORMAT)
				if err != nil {
					continue
				}

				issueDocID := common.GenerateCombinedHashID(issue.ID, assignee, tenantID)
				active := true

				if strings.Contains(strings.ToLower(issue.Fields.Status.Name), "complete") || strings.Contains(strings.ToLower(issue.Fields.Status.Name), "done") || strings.Contains(strings.ToLower(issue.Fields.Status.Name), "fixed") {
					active = false
				}

				jiraIssueDoc := common.JiraIssuesDoc{
					ID:           issueDocID,
					IssueID:      issue.ID,
					Summary:      issue.Fields.Summary,
					Assignee:     assignee,
					Description:  issueDescription,
					Reporter:     reporter,
					CreatedTime:  createdTime,
					ModifiedTime: modifiedTime,
					Status:       issue.Fields.Status.Name,
					ProjectName:  project.Name,
					ProjectID:    project.ID,
					TenantID:     tenantID,
					InsertTime:   elastic.DateTime(time.Now()),
					Active:       active,
				}

				if _, err = elastic.InsertDocument(tenantID, elastic.JIRA_ISSUES_INDEX, jiraIssueDoc, issueDocID); err != nil {
					continue
				}

				var (
					issueCommentUrl = jiraEnv.URL + "/rest/api/2/issue/" + issue.ID + "/comment"
					bearerToken     = strings.TrimSpace(jiraEnv.Token)
					maxResults      = 50
					comments        = make([]common.Comment, 0)
				)

				startAt := 0

				for {

					commentsResp, err := transport.SendRequest(
						"GET",
						issueCommentUrl,
						map[string]string{
							"startAt":    strconv.Itoa(startAt),
							"maxResults": strconv.Itoa(maxResults),
						},
						map[string]string{"Authorization": "Bearer " + bearerToken},
						nil,
					)
					if err != nil {
						break
					}

					var jiraIssueCommentResp common.JiraDataCenterCommentResponse

					if err = json.Unmarshal(commentsResp, &jiraIssueCommentResp); err != nil {
						logger.Print(logger.ERROR, "Failed to unmarshal", []string{tenantID}, err)
						break
					}

					if len(jiraIssueCommentResp.Comments) <= 0 {
						break
					}

					startAt = startAt + maxResults

					for _, comment := range jiraIssueCommentResp.Comments {
						comments = append(comments, common.Comment{
							Author: comment.Author.DisplayName,
							Body:   comment.Body,
						})
					}
				}

				// Jira resources will have details of the ticket.
				jiraIssueDoc.Comments = comments

				jiraTicketContext, err := analyzeTicketDescription(jiraIssueDoc, tenantID)
				if err != nil {
					return err
				}

				if jiraTicketContext == nil || len(jiraTicketContext.Resources) == 0 {
					continue
				}

				err = getTicketContextFromField(tenantID, jiraIssueDoc.ProjectID, issue.Fields.IssueType.ID, jiraFields.Issues[i].Fields, jiraTicketContext, fieldIdToName)
				if err != nil {
					continue
				}

				err = getDCTicketChangeLog(tenantID, issue, jiraTicketContext)
				if err != nil {
					continue
				}

				for _, resourceDoc := range jiraTicketContext.Resources {

					if len(resourceDoc.EntityID) == 0 {
						continue
					}

					docID := common.GenerateCombinedHashID(issue.ID, resourceDoc.EntityID, resourceDoc.EntityType, tenantID)
					resourceDoc.ID = docID
					resourceDoc.TenantID = tenantID
					resourceDoc.IssueDocID = issueDocID
					resourceDoc.InsertTime = elastic.DateTime(time.Now())
					resourceDoc.Deleted = !active
					resourceDoc.Assignee = assignee

					if _, err = elastic.InsertDocument(tenantID, elastic.JIRA_RESOURCES_INDEX, resourceDoc, docID); err != nil {
						continue
					}
				}
			}
		}
	}

	return nil
}

func processJiraDataCenterData(tenantID string, jiraEnv tenant.JiraEnvironment, jiraEndTime time.Time) (err error) {

	if err = processJiraDataCenterProjects(tenantID, jiraEnv, jiraEndTime); err != nil {
		return
	}

	return
}

func processJiraDataCenterProjects(tenantID string, jiraEnv tenant.JiraEnvironment, jiraEndTime time.Time) error {

	logger.Print(logger.INFO, "Fetching selected Jira Projects", []string{tenantID})

	var (
		recordsCount        int
		bulkJiraDataRequest string
		parentID            = tenantID
		accountID           = jiraEnv.URL
	)

	projects, err := getSelectedJiraDataCenterProjects(tenantID, jiraEnv)
	if err != nil {
		return err
	}

	deleteTrueQuery := `{"query":{"bool":{"must":[{"term":{"tenantId.keyword":"` + tenantID + `"}},{"term":{"type.keyword":"` + common.PROJECT_JIRADATA_TYPE + `"}},{"term":{"parentId.keyword":"` + parentID + `"}},{"term":{"accountId.keyword":"` + accountID + `"}}]}},"script":"ctx._source.deleted = true;"}`

	// Every iteration, we set deleted as true so that when all are collected, we get to know which actually got deleted
	logger.Print(logger.INFO, "Identifying deleted jira projects", []string{tenantID})
	elastic.UpdateByQuery(elastic.JIRA_DATA_INDEX, deleteTrueQuery)

	for _, project := range projects {

		docID := common.GenerateCombinedHashID(project.ID, common.PROJECT_JIRADATA_TYPE, parentID, tenantID, accountID)

		jiraDataDoc := common.JiraDataDoc{
			ID:         docID,
			JiraID:     project.ID,
			Name:       project.Name,
			Type:       common.PROJECT_JIRADATA_TYPE,
			ParentID:   parentID,
			AccountID:  accountID,
			TenantID:   tenantID,
			InsertTime: elastic.DateTime(jiraEndTime),
			Deleted:    false,
		}

		jiraDataInsertMetadata := `{"index": {"_id": "` + docID + `"}}`
		jiraDataInsertDoc, err := json.Marshal(jiraDataDoc)
		if err != nil {
			logger.Print(logger.ERROR, "Failed to marshal document", err)
			continue
		}

		recordsCount++
		bulkJiraDataRequest = bulkJiraDataRequest + jiraDataInsertMetadata + "\n" + string(jiraDataInsertDoc) + "\n"

		if err = processJiraDataCenterUsersOfProject(project, tenantID, jiraEnv, jiraEndTime); err != nil {
			continue
		}

		if err = processJiraDataCenterIssueTypesOfProject(project, tenantID, jiraEnv, jiraEndTime); err != nil {
			continue
		}
	}

	if recordsCount > 0 {
		if err := elastic.BulkDocumentsAPI(tenantID, elastic.JIRA_DATA_INDEX, bulkJiraDataRequest); err != nil {
			return err
		}

		logger.Print(logger.INFO, "Jira data bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID})
	}

	return nil
}

func getSelectedJiraDataCenterProjects(tenantID string, jiraEnv tenant.JiraEnvironment) (selectedProjects []common.JiraProject, err error) {

	var (
		projectMap       = make(map[string]struct{})
		projectSearchUrl = jiraEnv.URL + "/rest/api/2/project"
		bearerToken      = strings.TrimSpace(jiraEnv.Token)
	)

	for _, project := range jiraEnv.Projects {
		projectMap[project] = struct{}{}
	}

	projectsResp, err := transport.SendRequest(
		"GET",
		projectSearchUrl,
		map[string]string{"expand": "issueTypes"},
		map[string]string{"Authorization": "Bearer " + bearerToken},
		nil,
	)
	if err != nil {
		return
	}

	var jiraProjects []common.JiraProject

	if err = json.Unmarshal(projectsResp, &jiraProjects); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal", []string{tenantID}, err)
		return
	}

	if len(jiraProjects) <= 0 {
		return
	}

	for i, project := range jiraProjects {

		selected := true

		if _, ok := projectMap[project.Name]; !ok {
			if _, ok := projectMap[project.ID]; !ok {
				if _, ok := projectMap[project.Key]; !ok {
					selected = false
				}
			}
		}

		if selected {
			if len(project.IssueTypes) <= 0 {
				issueTypes, _ := getJiraDataCenterProjectIssueTypes(jiraEnv, tenantID, project.ID)
				jiraProjects[i].IssueTypes = append(jiraProjects[i].IssueTypes, issueTypes...)
			}

			selectedProjects = append(selectedProjects, jiraProjects[i])
		}
	}

	return
}

func getJiraDataCenterProjectIssueTypes(jiraEnv tenant.JiraEnvironment, tenantID, projKey string) ([]common.JiraIssueType, error) {
	var (
		projectSearchUrl = jiraEnv.URL + "/rest/api/2/project/" + projKey + "/statuses"
		bearerToken      = strings.TrimSpace(jiraEnv.Token)
	)

	projectsResp, err := transport.SendRequest(
		"GET",
		projectSearchUrl,
		map[string]string{"expand": "issueTypes"},
		map[string]string{"Authorization": "Bearer " + bearerToken},
		nil,
	)
	if err != nil {
		return nil, err
	}

	var jiraListIssueTypeResp []common.JiraIssueType

	if err = json.Unmarshal(projectsResp, &jiraListIssueTypeResp); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal", []string{tenantID}, err)
		return nil, err
	}

	return jiraListIssueTypeResp, nil
}

func processJiraDataCenterIssueTypesOfProject(project common.JiraProject, tenantID string, jiraEnv tenant.JiraEnvironment, jiraEndTime time.Time) error {

	var (
		recordsCount        int
		bulkJiraDataRequest string
		parentID            = project.ID
		accountID           = project.ID
	)

	logger.Print(logger.INFO, "Fetching Jira Issuetypes of project "+parentID, []string{tenantID})

	deleteTrueQuery := `{"query":{"bool":{"must":[{"term":{"tenantId.keyword":"` + tenantID + `"}},{"term":{"type.keyword":"` + common.ISSUETYPE_JIRADATA_TYPE + `"}},{"term":{"parentId.keyword":"` + parentID + `"}},{"term":{"accountId.keyword":"` + accountID + `"}}]}},"script":"ctx._source.deleted = true;"}`

	// Every iteration, we set deleted as true so that when all are collected, we get to know which actually got deleted
	logger.Print(logger.INFO, "Identifying deleted jira issuetypes", []string{tenantID})
	elastic.UpdateByQuery(elastic.JIRA_DATA_INDEX, deleteTrueQuery)

	for _, issueType := range project.IssueTypes {

		docID := common.GenerateCombinedHashID(issueType.ID, common.ISSUETYPE_JIRADATA_TYPE, parentID, tenantID, accountID)

		jiraDataDoc := common.JiraDataDoc{
			ID:         docID,
			JiraID:     issueType.ID,
			Name:       issueType.Name,
			Type:       common.ISSUETYPE_JIRADATA_TYPE,
			ParentID:   parentID,
			AccountID:  accountID,
			TenantID:   tenantID,
			InsertTime: elastic.DateTime(jiraEndTime),
			Deleted:    false,
		}

		jiraDataInsertMetadata := `{"index": {"_id": "` + docID + `"}}`
		jiraDataInsertDoc, err := json.Marshal(jiraDataDoc)
		if err != nil {
			logger.Print(logger.ERROR, "Failed to marshal document", err)
			continue
		}

		recordsCount++
		bulkJiraDataRequest = bulkJiraDataRequest + jiraDataInsertMetadata + "\n" + string(jiraDataInsertDoc) + "\n"

		if err = processJiraDataCenterFieldsOfIssueType(issueType, project, tenantID, jiraEnv, jiraEndTime); err != nil {
			continue
		}

	}

	if recordsCount > 0 {
		if err := elastic.BulkDocumentsAPI(tenantID, elastic.JIRA_DATA_INDEX, bulkJiraDataRequest); err != nil {
			return err
		}

		logger.Print(logger.INFO, "Jira data bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID})
	}

	return nil
}

func processJiraDataCenterFieldsOfIssueType(issueType common.JiraIssueType, project common.JiraProject, tenantID string,
	jiraEnv tenant.JiraEnvironment, jiraEndTime time.Time) error {

	var (
		createMetaUrl = jiraEnv.URL + "/rest/api/2/issue/createmeta/" + project.ID + "/issuetypes/" + issueType.ID
		bearerToken   = strings.TrimSpace(jiraEnv.Token)
		maxResults    = 50
		startAt       = 0
		parentID      = issueType.ID
		accountID     = project.ID + "/" + issueType.ID
	)

	logger.Print(logger.INFO, "Fetching Jira Issuetypefields of issuetype "+parentID, []string{tenantID})

	for {

		fieldsResp, err := transport.SendRequest(
			"GET",
			createMetaUrl,
			map[string]string{"startAt": strconv.Itoa(startAt), "maxResults": strconv.Itoa(maxResults)},
			map[string]string{"Authorization": "Bearer " + bearerToken},
			nil,
		)
		if err != nil {
			break
		}

		if startAt == 0 {

			deleteTrueQuery := `{"query":{"bool":{"must":[{"term":{"tenantId.keyword":"` + tenantID + `"}},{"term":{"type.keyword":"` + common.ISSUEFIELD_JIRADATA_TYPE + `"}},{"term":{"parentId.keyword":"` + parentID + `"}},{"term":{"accountId.keyword":"` + accountID + `"}}]}},"script":"ctx._source.deleted = true;"}`

			// Every iteration, we set deleted as true so that when all are collected, we get to know which actually got deleted
			logger.Print(logger.INFO, "Identifying deleted jira issuetype fields", []string{tenantID})
			elastic.UpdateByQuery(elastic.JIRA_DATA_INDEX, deleteTrueQuery)
		}

		var jiraCreateMetaResp common.JiraCreateMetaResponse

		if err = json.Unmarshal(fieldsResp, &jiraCreateMetaResp); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", []string{tenantID}, err)
			break
		}

		if len(jiraCreateMetaResp.Values) <= 0 {
			break
		}

		startAt = startAt + maxResults

		var (
			recordsCount        int
			bulkJiraDataRequest string
		)

		for _, issueTypeField := range jiraCreateMetaResp.Values {

			docID := common.GenerateCombinedHashID(issueTypeField.FieldID, common.ISSUEFIELD_JIRADATA_TYPE, parentID, tenantID, accountID)

			additionalDetails := make(map[string]any)

			additionalDetails["required"] = issueTypeField.Required

			if issueTypeField.HasDefaultValue {
				additionalDetails["defaultValue"] = issueTypeField.DefaultValue
			}

			if len(issueTypeField.Schema.Type) > 0 {
				additionalDetails["schemaType"] = issueTypeField.Schema.Type
			}

			if len(issueTypeField.Schema.Items) > 0 {
				additionalDetails["schemaItems"] = issueTypeField.Schema.Items
			}

			if len(issueTypeField.Schema.Custom) > 0 {
				additionalDetails["schemaCustom"] = issueTypeField.Schema.Custom
			}

			if len(issueTypeField.Schema.System) > 0 {
				additionalDetails["schemaSystem"] = issueTypeField.Schema.System
			}

			additionalDetailsString, err := json.Marshal(additionalDetails)
			if err != nil {
				continue
			}

			jiraDataDoc := common.JiraDataDoc{
				ID:                docID,
				JiraID:            issueTypeField.FieldID,
				Name:              issueTypeField.Name,
				Type:              common.ISSUEFIELD_JIRADATA_TYPE,
				ParentID:          parentID,
				AccountID:         accountID,
				TenantID:          tenantID,
				AdditionalDetails: string(additionalDetailsString),
				InsertTime:        elastic.DateTime(jiraEndTime),
				Deleted:           false,
			}

			jiraDataInsertMetadata := `{"index": {"_id": "` + docID + `"}}`
			jiraDataInsertDoc, err := json.Marshal(jiraDataDoc)
			if err != nil {
				logger.Print(logger.ERROR, "Failed to marshal document", err)
				continue
			}

			recordsCount++
			bulkJiraDataRequest = bulkJiraDataRequest + jiraDataInsertMetadata + "\n" + string(jiraDataInsertDoc) + "\n"

			if err = processJiraDataCenterFieldValues(issueType, issueTypeField, project, tenantID, jiraEnv, jiraEndTime); err != nil {
				continue
			}
		}

		if recordsCount > 0 {
			if err := elastic.BulkDocumentsAPI(tenantID, elastic.JIRA_DATA_INDEX, bulkJiraDataRequest); err != nil {
				return err
			}

			logger.Print(logger.INFO, "Jira data bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID})
		}
	}

	return nil
}

func processJiraDataCenterFieldValues(issueType common.JiraIssueType, issueTypeField common.JiraIssueTypeField,
	project common.JiraProject, tenantID string, jiraEnv tenant.JiraEnvironment, jiraEndTime time.Time) error {

	var (
		recordsCount        int
		bulkJiraDataRequest string
		maxRecords          = 500
		parentID            = issueTypeField.FieldID
		accountID           = project.ID + "/" + issueType.ID + "/" + issueTypeField.FieldID
	)

	switch issueTypeField.Schema.Type {
	case "issuetype", "project", "user":
		return nil
	}

	switch issueTypeField.Schema.Items {
	case "user", "issuelinks":
		return nil
	}

	switch issueTypeField.Schema.Custom {
	case "com.pyxis.greenhopper.jira:gh-sprint":
		logger.Print(logger.INFO, "Fetching Jira values of sprint field "+parentID, []string{tenantID})
		if err := processJiraSprintValues(issueTypeField, project, tenantID, jiraEnv, jiraEndTime); err != nil {
			return err
		}

		return nil
	}

	if len(issueTypeField.AllowedValues) > 0 {

		logger.Print(logger.INFO, "Fetching Jira allowed values of field "+parentID, []string{tenantID})

		deleteTrueQuery := `{"query":{"bool":{"must":[{"term":{"tenantId.keyword":"` + tenantID + `"}},{"term":{"type.keyword":"` + common.FIELDVALUE_JIRADATA_TYPE + `"}},{"term":{"parentId.keyword":"` + parentID + `"}},{"term":{"accountId.keyword":"` + accountID + `"}}]}},"script":"ctx._source.deleted = true;"}`

		// Every iteration, we set deleted as true so that when all are collected, we get to know which actually got deleted
		logger.Print(logger.INFO, "Identifying deleted jira issuetype field values", []string{tenantID})
		elastic.UpdateByQuery(elastic.JIRA_DATA_INDEX, deleteTrueQuery)

		for _, allowedValue := range issueTypeField.AllowedValues {

			valueID, _ := allowedValue["id"].(string)
			valueName, _ := allowedValue["name"].(string)

			if len(valueName) <= 0 {
				valueName, _ = allowedValue["value"].(string)
			}

			if len(valueID) > 0 && len(valueName) > 0 {

				docID := common.GenerateCombinedHashID(valueID, common.FIELDVALUE_JIRADATA_TYPE, parentID, tenantID, accountID)

				jiraDataDoc := common.JiraDataDoc{
					ID:         docID,
					JiraID:     valueID,
					Name:       valueName,
					Type:       common.FIELDVALUE_JIRADATA_TYPE,
					ParentID:   parentID,
					AccountID:  accountID,
					TenantID:   tenantID,
					InsertTime: elastic.DateTime(jiraEndTime),
					Deleted:    false,
				}

				jiraDataInsertMetadata := `{"index": {"_id": "` + docID + `"}}`
				jiraDataInsertDoc, err := json.Marshal(jiraDataDoc)
				if err != nil {
					logger.Print(logger.ERROR, "Failed to marshal document", err)
					continue
				}

				recordsCount++
				bulkJiraDataRequest = bulkJiraDataRequest + jiraDataInsertMetadata + "\n" + string(jiraDataInsertDoc) + "\n"

				if recordsCount > maxRecords {
					if err := elastic.BulkDocumentsAPI(tenantID, elastic.JIRA_DATA_INDEX, bulkJiraDataRequest); err != nil {
						return err
					}

					logger.Print(logger.INFO, "Jira data bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID})

					recordsCount = 0
					bulkJiraDataRequest = ""
				}
			}
		}
	} else if len(issueTypeField.AutoCompleteUrl) > 0 {

		// var (
		// 	bearerToken = strings.TrimSpace(jiraEnv.Token)
		// 	values      = make(map[string]struct{})
		// )

		// autoCompleteResp, err := transport.SendRequest(
		// 	"GET",
		// 	issueTypeField.AutoCompleteUrl,
		// 	map[string]string{},
		// 	map[string]string{"Authorization": "Bearer " + bearerToken},
		// 	nil,
		// )
		// if err != nil {
		// 	return err
		// }

		// deleteTrueQuery := `{"query":{"bool":{"must":[{"term":{"tenantId.keyword":"` + tenantID + `"}},{"term":{"type.keyword":"` + common.FIELDVALUE_JIRADATA_TYPE + `"}},{"term":{"parentId.keyword":"` + parentID + `"}},{"term":{"accountId.keyword":"` + accountID + `"}}]}},"script":"ctx._source.deleted = true;"}`

		// // Every iteration, we set deleted as true so that when all are collected, we get to know which actually got deleted
		// logger.Print(logger.INFO, "Identifying deleted jira issuetype field values", []string{tenantID})
		// elastic.UpdateByQuery(elastic.JIRA_DATA_INDEX, deleteTrueQuery)

		// switch parentID {
		// case "labels":

		// 	var labelsResp struct {
		// 		Suggestions []struct {
		// 			Label string `json:"label"`
		// 		} `json:"suggestions"`
		// 	}

		// 	if err = json.Unmarshal(autoCompleteResp, &labelsResp); err != nil {
		// 		logger.Print(logger.ERROR, "Failed to unmarshal", []string{tenantID}, err)
		// 		return err
		// 	}

		// 	for _, suggestion := range labelsResp.Suggestions {
		// 		values[suggestion.Label] = struct{}{}
		// 	}
		// }

		// for value:= range values {

		// 	docID := common.GenerateCombinedHashID(value, common.FIELDVALUE_JIRADATA_TYPE, parentID, tenantID, accountID)

		// 	jiraDataDoc := common.JiraDataDoc{
		// 		ID:         docID,
		// 		JiraID:     value,
		// 		Name:       value,
		// 		Type:       common.FIELDVALUE_JIRADATA_TYPE,
		// 		ParentID:   parentID,
		//		AccountID: accountID,
		// 		TenantID:   tenantID,
		// 		InsertTime: elastic.DateTime(jiraEndTime),
		// 		Deleted:    false,
		// 	}

		// 	jiraDataInsertMetadata := `{"index": {"_id": "` + docID + `"}}`
		// 	jiraDataInsertDoc, err := json.Marshal(jiraDataDoc)
		// 	if err != nil {
		// 		logger.Print(logger.ERROR, "Failed to marshal document", err)
		// 		continue
		// 	}

		// 	recordsCount++
		// 	bulkJiraDataRequest = bulkJiraDataRequest + jiraDataInsertMetadata + "\n" + string(jiraDataInsertDoc) + "\n"

		// 	if recordsCount > maxRecords {
		// 		if err := elastic.BulkDocumentsAPI(tenantID, elastic.JIRA_DATA_INDEX, bulkJiraDataRequest); err != nil {
		// 			return err
		// 		}

		// 		logger.Print(logger.INFO, "Jira data bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID})

		// 		recordsCount = 0
		// 		bulkJiraDataRequest = ""
		// 	}
		// }
	}

	if recordsCount > 0 {
		if err := elastic.BulkDocumentsAPI(tenantID, elastic.JIRA_DATA_INDEX, bulkJiraDataRequest); err != nil {
			return err
		}

		logger.Print(logger.INFO, "Jira data bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID})
	}

	return nil
}

func processJiraDataCenterUsersOfProject(project common.JiraProject, tenantID string, jiraEnv tenant.JiraEnvironment, jiraEndTime time.Time) error {

	var (
		usersUrl    = jiraEnv.URL + "/rest/api/2/user/assignable/multiProjectSearch"
		bearerToken = strings.TrimSpace(jiraEnv.Token)
		maxResults  = 50
		parentID    = project.ID
		accountID   = project.ID
	)

	logger.Print(logger.INFO, "Fetching Jira Users of project "+parentID, []string{tenantID})

	var (
		jiraUsers              []common.JiraUser
		recordsCount           int
		bulkJiraDataRequest    string
		jiraUsersMap           = make(map[string]struct{})
		existingRecordsDeleted bool
	)

	for prefixStart := 'A'; prefixStart <= 'Z'; prefixStart++ {
		prefixQueue := []string{string(prefixStart)}

		for len(prefixQueue) > 0 {
			prefix := prefixQueue[0]
			prefixQueue = prefixQueue[1:]

			usersResp, err := fetchAssignableUsers(usersUrl, bearerToken, maxResults, project.Key, prefix, tenantID)
			if err != nil {
				logger.Print(logger.ERROR, "Error fetching Users of Project", []string{tenantID}, err)
				return err
			}

			// mark existing users as deleted true
			if prefixStart == 'A' && !existingRecordsDeleted {
				deleteTrueQuery := `{"query":{"bool":{"must":[{"term":{"tenantId.keyword":"` + tenantID + `"}},{"term":{"type.keyword":"` + common.USER_JIRADATA_TYPE + `"}},{"term":{"parentId.keyword":"` + parentID + `"}},{"term":{"accountId.keyword":"` + accountID + `"}}]}},"script":"ctx._source.deleted = true;"}`

				// Every iteration, we set deleted as true so that when all are collected, we get to know which actually got deleted
				logger.Print(logger.INFO, "Identifying deleted jira users of project", []string{tenantID})
				elastic.UpdateByQuery(elastic.JIRA_DATA_INDEX, deleteTrueQuery)

				existingRecordsDeleted = true
			}

			if err = json.Unmarshal(usersResp, &jiraUsers); err != nil {
				logger.Print(logger.ERROR, "Failed to unmarshal", []string{tenantID}, err)
				return err
			}

			var (
				newPrefix, lastChar string
				isMoreUserPresent   bool
			)

			if len(jiraUsers) <= 0 {
				continue
			}

			lastName := jiraUsers[len(jiraUsers)-1].DisplayName
			if len(jiraUsers) >= maxResults && len(prefix) < 2 {
				isMoreUserPresent = true
			}

			for _, jiraUser := range jiraUsers {
				if isMoreUserPresent {
					if strings.HasPrefix(jiraUser.DisplayName, prefix) {
						if len(lastName) >= 2 {
							newPrefix = lastName[:1]
							lastChar = lastName[1:2]
						}
					}
				}

				if _, ok := jiraUsersMap[jiraUser.Key]; !ok {
					if jiraUser.AccountType != "app" {
						docID := common.GenerateCombinedHashID(jiraUser.Name, common.USER_JIRADATA_TYPE, parentID, tenantID, accountID)
						additionalDetails := make(map[string]any)
						if len(jiraUser.EmailAddress) > 0 {
							additionalDetails["email"] = jiraUser.EmailAddress
						}

						if len(jiraUser.Key) > 0 {
							additionalDetails["key"] = jiraUser.Key
						}

						additionalDetailsString, err := json.Marshal(additionalDetails)
						if err != nil {
							continue
						}

						jiraDataDoc := common.JiraDataDoc{
							ID:                docID,
							JiraID:            jiraUser.Name,
							Name:              jiraUser.DisplayName,
							Type:              common.USER_JIRADATA_TYPE,
							ParentID:          parentID,
							AccountID:         accountID,
							TenantID:          tenantID,
							AdditionalDetails: string(additionalDetailsString),
							InsertTime:        elastic.DateTime(jiraEndTime),
							Deleted:           !jiraUser.Active,
						}

						jiraDataInsertMetadata := `{"index": {"_id": "` + docID + `"}}`
						jiraDataInsertDoc, err := json.Marshal(jiraDataDoc)
						if err != nil {
							logger.Print(logger.ERROR, "Failed to marshal document", err)
							continue
						}

						jiraUsersMap[jiraUser.Key] = struct{}{}

						recordsCount++
						bulkJiraDataRequest = bulkJiraDataRequest + jiraDataInsertMetadata + "\n" + string(jiraDataInsertDoc) + "\n"

						if recordsCount > MAX_RECORDS {
							if err := elastic.BulkDocumentsAPI(tenantID, elastic.JIRA_DATA_INDEX, bulkJiraDataRequest); err != nil {
								return err
							}

							logger.Print(logger.INFO, "Jira data bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID})
							bulkJiraDataRequest = ""
							recordsCount = 0
						}
					}
				}
			}

			if isMoreUserPresent {
				if len(lastChar) == 0 {
					newPrefix = prefix
					lastChar = string('a')
				}

				for i := lastChar[0]; i <= 'z'; i++ {
					prefixQueue = append(prefixQueue, newPrefix+string(i))
				}
			}
		}
	}

	if recordsCount > 0 {
		if err := elastic.BulkDocumentsAPI(tenantID, elastic.JIRA_DATA_INDEX, bulkJiraDataRequest); err != nil {
			return err
		}

		logger.Print(logger.INFO, "Jira data bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID})
	}

	return nil
}

func fetchAssignableUsers(usersUrl, bearerToken string, maxResults int, projectKey, prefix, tenantID string) ([]byte, error) {
	var (
		backoffTime = time.Second * 1
		maxRetries  = 5
	)

	for attempt := 1; attempt <= maxRetries; attempt++ {
		usersResp, err := transport.SendRequest(
			"GET",
			usersUrl,
			map[string]string{"maxResults": strconv.Itoa(maxResults), "projectKeys": projectKey, "username": prefix},
			map[string]string{"Authorization": "Bearer " + bearerToken},
			nil,
		)
		if err != nil {
			if strings.Contains(err.Error(), "429") && attempt < maxRetries {
				logger.Print(logger.INFO, "Rate limit reached. Sleeping for ", []string{tenantID}, backoffTime)
				time.Sleep(backoffTime)
				backoffTime *= 2
				continue
			}
			return nil, err
		}
		return usersResp, nil
	}

	return nil, fmt.Errorf("Max retries exceeded for API call")
}

func getDCTicketChangeLog(tenantID string, issue common.JiraDCIssue, jiraTicketContext *jirautils.JiraTicketContext) (err error) {
	var allChangelogs []common.ChangeLogValue

	allChangelogs = append(allChangelogs, issue.Changelog.Values...)

	return processChangelogAndUpdateTicket(tenantID, allChangelogs, jiraTicketContext)
}

package jira

import (
	"encoding/json"
	"strconv"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/common/jira"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
	"github.com/precize/transport"
)

type jiraIssueFieldsResponse struct {
	Issues []jiraFields `json:"issues"`
}

type jiraFields struct {
	Fields map[string]any `json:"fields"`
}

type jiraCloudIssueSearchResponse struct {
	Issues        []common.JiraCloudIssue `json:"issues"`
	NextPageToken string                  `json:"nextPageToken"`
}

func processJiraCloudIssues(tenantID string, jiraEnv tenant.JiraEnvironment, jiraStartTime, jiraEndTime time.Time) error {

	// TODO: Handle closing of tickets
	var (
		basicAuth      = strings.TrimSpace(jiraEnv.Username) + ":" + strings.TrimSpace(jiraEnv.Token)
		issueSearchUrl = jiraEnv.URL + "/rest/api/3/search/jql"
		maxResults     = 50
		nextPageToken  string
		fieldIdToName  = make(map[string]string)
	)

	projects, err := getSelectedJiraCloudProjects(tenantID, jiraEnv)
	if err != nil {
		return err
	}

	logger.Print(logger.INFO, "Fetching Cloud Jira Ops issues from "+common.DateTime(jiraStartTime)+" to "+common.DateTime(jiraEndTime), []string{tenantID}, jiraEnv.ID)

	for _, project := range projects {

		for {

			queryParams := make(map[string]string)
			queryParams["jql"] = "project = " + project.ID + " AND updated > '" + formatJiraTimeForQuery(jiraStartTime) + "' AND updated < '" + formatJiraTimeForQuery(jiraEndTime) + "'"
			queryParams["maxResults"] = strconv.Itoa(maxResults)
			queryParams["fields"] = "*all"

			if len(nextPageToken) > 0 {
				queryParams["nextPageToken"] = nextPageToken
			}

			issuesResp, err := transport.SendRequest(
				"GET",
				issueSearchUrl,
				queryParams,
				map[string]string{"Authorization": "Basic " + common.EncodeBase64(basicAuth)},
				nil,
			)
			if err != nil {
				break
			}

			var jiraIssueSearchResp jiraCloudIssueSearchResponse

			if err = json.Unmarshal(issuesResp, &jiraIssueSearchResp); err != nil {
				logger.Print(logger.ERROR, "Failed to unmarshal", []string{tenantID}, err)
				break
			}

			var jiraFields jiraIssueFieldsResponse

			if err = json.Unmarshal(issuesResp, &jiraFields); err != nil {
				logger.Print(logger.ERROR, "Failed to unmarshal", []string{tenantID}, err)
				break
			}

			nextPageToken = jiraIssueSearchResp.NextPageToken

			if len(jiraIssueSearchResp.Issues) > 0 {
				fieldQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"type.keyword":"field"}},{"match":{"deleted":"false"}}]}}}`
				jiraFieldDocs, err := elastic.ExecuteSearchQuery([]string{elastic.JIRA_DATA_INDEX}, fieldQuery)
				if err != nil {
					return err
				}

				for _, jiraFieldDoc := range jiraFieldDocs {
					if accountId, ok := jiraFieldDoc["accountId"].(string); ok {
						if jiraID, ok := jiraFieldDoc["jiraId"].(string); ok {
							if name, ok := jiraFieldDoc["name"].(string); ok {
								fieldIdToName[accountId+"/"+jiraID] = name
							}
						}
					}
				}
			}

			logger.Print(logger.INFO, "Issues Fetched ", []string{tenantID, project.ID}, len(jiraIssueSearchResp.Issues))

			for i, issue := range jiraIssueSearchResp.Issues {

				var (
					issueDescription string
					comments         = make([]common.Comment, 0)
				)

				for _, content := range issue.Fields.Description.Content {
					if content.Type == "text" {
						issueDescription += content.Text
					} else if content.Type == "paragraph" {
						for _, contentBlock := range content.Content {
							if contentBlock.Type == "text" {
								issueDescription += contentBlock.Text
							}
						}
					}
				}

				assignee := issue.Fields.Assignee.EmailAddress
				reporter := issue.Fields.Reporter.EmailAddress

				if len(assignee) <= 0 {
					assignee = issue.Fields.Assignee.Name
					if len(assignee) <= 0 {
						assignee = issue.Fields.Assignee.DisplayName
					}
				}

				if len(reporter) <= 0 {
					reporter = issue.Fields.Reporter.Name
					if len(reporter) <= 0 {
						reporter = issue.Fields.Reporter.DisplayName
					}
				}

				createdTime, err := common.ConvertTime(issue.Fields.Created, JIRA_TIME_FORMAT, elastic.DATE_FORMAT)
				if err != nil {
					continue
				}

				modifiedTime, err := common.ConvertTime(issue.Fields.Updated, JIRA_TIME_FORMAT, elastic.DATE_FORMAT)
				if err != nil {
					continue
				}

				issueDocID := common.GenerateCombinedHashID(issue.ID, assignee, tenantID)
				active := true

				if strings.Contains(strings.ToLower(issue.Fields.Status.Name), "complete") || strings.Contains(strings.ToLower(issue.Fields.Status.Name), "done") || strings.Contains(strings.ToLower(issue.Fields.Status.Name), "fixed") {
					active = false
				}

				jiraIssueDoc := common.JiraIssuesDoc{
					ID:           issueDocID,
					IssueID:      issue.ID,
					Summary:      issue.Fields.Summary,
					Assignee:     assignee,
					Description:  issueDescription,
					Reporter:     reporter,
					CreatedTime:  createdTime,
					ModifiedTime: modifiedTime,
					Status:       issue.Fields.Status.Name,
					ProjectName:  project.Name,
					ProjectID:    project.ID,
					TenantID:     tenantID,
					InsertTime:   elastic.DateTime(time.Now()),
					Active:       active,
				}

				if _, err = elastic.InsertDocument(tenantID, elastic.JIRA_ISSUES_INDEX, jiraIssueDoc, issueDocID); err != nil {
					continue
				}

				for _, comment := range issue.Fields.Comment.Comment {
					commentBody := ""
					for _, content := range comment.Body.Content {
						if content.Type == "text" {
							commentBody += content.Text
						} else if content.Type == "paragraph" {
							for _, contentBlock := range content.Content {
								if contentBlock.Type == "text" {
									commentBody += contentBlock.Text
								} else if contentBlock.Type == "mention" {
									commentBody += strings.Trim(contentBlock.Attrs.Text, "@")
								}
							}
						}
					}

					comments = append(comments, common.Comment{
						Author: comment.Author.DisplayName,
						Body:   commentBody,
					})
				}

				// Jira resources will have details of the ticket.
				jiraIssueDoc.Comments = comments

				jiraTicketContext, err := analyzeTicketDescription(jiraIssueDoc, tenantID)
				if err != nil {
					continue
				}

				if jiraTicketContext == nil || len(jiraTicketContext.Resources) == 0 {
					continue
				}

				err = getTicketContextFromField(tenantID, jiraIssueDoc.ProjectID, issue.Fields.IssueType.ID, jiraFields.Issues[i].Fields, jiraTicketContext, fieldIdToName)
				if err != nil {
					continue
				}

				err = getCloudTicketChangeLog(tenantID, jiraEnv, jiraIssueDoc, jiraTicketContext)
				if err != nil {
					continue
				}

				for _, resourceDoc := range jiraTicketContext.Resources {

					if len(resourceDoc.EntityID) == 0 {
						continue
					}

					docID := common.GenerateCombinedHashID(issue.ID, resourceDoc.EntityID, resourceDoc.EntityType, tenantID)
					resourceDoc.ID = docID
					resourceDoc.TenantID = tenantID
					resourceDoc.IssueDocID = issueDocID
					resourceDoc.InsertTime = elastic.DateTime(time.Now())
					resourceDoc.Deleted = !active
					resourceDoc.Assignee = assignee

					if _, err = elastic.InsertDocument(tenantID, elastic.JIRA_RESOURCES_INDEX, resourceDoc, docID); err != nil {
						continue
					}
				}
			}

			if len(nextPageToken) <= 0 {
				break
			}
		}
	}

	return nil
}

func processJiraCloudData(tenantID string, jiraEnv tenant.JiraEnvironment, jiraEndTime time.Time) (err error) {

	if err = processJiraCloudProjects(tenantID, jiraEnv, jiraEndTime); err != nil {
		return
	}

	if err = processJiraCloudUsers(tenantID, jiraEnv, jiraEndTime); err != nil {
		return
	}

	return
}

func processJiraCloudProjects(tenantID string, jiraEnv tenant.JiraEnvironment, jiraEndTime time.Time) error {

	logger.Print(logger.INFO, "Fetching selected Jira Projects", []string{tenantID})

	var (
		recordsCount        int
		bulkJiraDataRequest string
		parentID            = tenantID
		accountID           = jiraEnv.URL
	)

	projects, err := getSelectedJiraCloudProjects(tenantID, jiraEnv)
	if err != nil {
		return err
	}

	deleteTrueQuery := `{"query":{"bool":{"must":[{"term":{"tenantId.keyword":"` + tenantID + `"}},{"term":{"type.keyword":"` + common.PROJECT_JIRADATA_TYPE + `"}},{"term":{"parentId.keyword":"` + parentID + `"}},{"term":{"accountId.keyword":"` + accountID + `"}}]}},"script":"ctx._source.deleted = true;"}`

	// Every iteration, we set deleted as true so that when all are collected, we get to know which actually got deleted
	logger.Print(logger.INFO, "Identifying deleted jira projects", []string{tenantID})
	elastic.UpdateByQuery(elastic.JIRA_DATA_INDEX, deleteTrueQuery)

	for _, project := range projects {

		docID := common.GenerateCombinedHashID(project.ID, common.PROJECT_JIRADATA_TYPE, parentID, tenantID, accountID)

		jiraDataDoc := common.JiraDataDoc{
			ID:         docID,
			JiraID:     project.ID,
			Name:       project.Name,
			Type:       common.PROJECT_JIRADATA_TYPE,
			ParentID:   parentID,
			AccountID:  accountID,
			TenantID:   tenantID,
			InsertTime: elastic.DateTime(jiraEndTime),
			Deleted:    false,
		}

		jiraDataInsertMetadata := `{"index": {"_id": "` + docID + `"}}`
		jiraDataInsertDoc, err := json.Marshal(jiraDataDoc)
		if err != nil {
			logger.Print(logger.ERROR, "Failed to marshal document", err)
			continue
		}

		recordsCount++
		bulkJiraDataRequest = bulkJiraDataRequest + jiraDataInsertMetadata + "\n" + string(jiraDataInsertDoc) + "\n"

		if err = processJiraCloudIssueTypesOfProject(project, tenantID, jiraEnv, jiraEndTime); err != nil {
			continue
		}

		if err = processJiraCloudUsersOfProject(project, tenantID, jiraEnv, jiraEndTime); err != nil {
			continue
		}
	}

	if recordsCount > 0 {
		if err := elastic.BulkDocumentsAPI(tenantID, elastic.JIRA_DATA_INDEX, bulkJiraDataRequest); err != nil {
			return err
		}

		logger.Print(logger.INFO, "Jira data bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID})
	}

	return nil
}

type jiraCloudListProjectsResponse struct {
	Values []common.JiraProject `json:"values"`
}

func getSelectedJiraCloudProjects(tenantID string, jiraEnv tenant.JiraEnvironment) (selectedProjects []common.JiraProject, err error) {

	var (
		projectMap       = make(map[string]struct{})
		projectSearchUrl = jiraEnv.URL + "/rest/api/3/project/search"
		basicAuth        = strings.TrimSpace(jiraEnv.Username) + ":" + strings.TrimSpace(jiraEnv.Token)
		startAt          = 0
		maxResults       = 50
	)

	for _, project := range jiraEnv.Projects {
		projectMap[project] = struct{}{}
	}

	for {

		projectsResp, err := transport.SendRequest(
			"GET",
			projectSearchUrl,
			map[string]string{"startAt": strconv.Itoa(startAt), "maxResults": strconv.Itoa(maxResults), "expand": "issueTypes"},
			map[string]string{"Authorization": "Basic " + common.EncodeBase64(basicAuth)},
			nil,
		)
		if err != nil {
			break
		}

		var jiraListProjectsResp jiraCloudListProjectsResponse

		if err = json.Unmarshal(projectsResp, &jiraListProjectsResp); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", []string{tenantID}, err)
			break
		}

		if len(jiraListProjectsResp.Values) <= 0 {
			break
		}

		startAt = startAt + maxResults

		for _, project := range jiraListProjectsResp.Values {

			selected := true

			if _, ok := projectMap[project.Name]; !ok {
				if _, ok := projectMap[project.ID]; !ok {
					if _, ok := projectMap[project.Key]; !ok {
						selected = false
					}
				}
			}

			if selected {
				selectedProjects = append(selectedProjects, project)
			}
		}
	}

	return
}

func processJiraCloudIssueTypesOfProject(project common.JiraProject, tenantID string, jiraEnv tenant.JiraEnvironment, jiraEndTime time.Time) error {

	var (
		recordsCount        int
		bulkJiraDataRequest string
		parentID            = project.ID
		accountID           = project.ID
	)

	logger.Print(logger.INFO, "Fetching Jira Issuetypes of project "+parentID, []string{tenantID})

	deleteTrueQuery := `{"query":{"bool":{"must":[{"term":{"tenantId.keyword":"` + tenantID + `"}},{"term":{"type.keyword":"` + common.ISSUETYPE_JIRADATA_TYPE + `"}},{"term":{"parentId.keyword":"` + parentID + `"}},{"term":{"accountId.keyword":"` + accountID + `"}}]}},"script":"ctx._source.deleted = true;"}`

	// Every iteration, we set deleted as true so that when all are collected, we get to know which actually got deleted
	logger.Print(logger.INFO, "Identifying deleted jira issuetypes", []string{tenantID})
	elastic.UpdateByQuery(elastic.JIRA_DATA_INDEX, deleteTrueQuery)

	for _, issueType := range project.IssueTypes {

		docID := common.GenerateCombinedHashID(issueType.ID, common.ISSUETYPE_JIRADATA_TYPE, parentID, tenantID, accountID)

		jiraDataDoc := common.JiraDataDoc{
			ID:         docID,
			JiraID:     issueType.ID,
			Name:       issueType.Name,
			Type:       common.ISSUETYPE_JIRADATA_TYPE,
			ParentID:   parentID,
			AccountID:  accountID,
			TenantID:   tenantID,
			InsertTime: elastic.DateTime(jiraEndTime),
			Deleted:    false,
		}

		jiraDataInsertMetadata := `{"index": {"_id": "` + docID + `"}}`
		jiraDataInsertDoc, err := json.Marshal(jiraDataDoc)
		if err != nil {
			logger.Print(logger.ERROR, "Failed to marshal document", err)
			continue
		}

		recordsCount++
		bulkJiraDataRequest = bulkJiraDataRequest + jiraDataInsertMetadata + "\n" + string(jiraDataInsertDoc) + "\n"

		if err = processJiraCloudFieldsOfIssueType(issueType, project, tenantID, jiraEnv, jiraEndTime); err != nil {
			continue
		}

	}

	if recordsCount > 0 {
		if err := elastic.BulkDocumentsAPI(tenantID, elastic.JIRA_DATA_INDEX, bulkJiraDataRequest); err != nil {
			return err
		}

		logger.Print(logger.INFO, "Jira data bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID})
	}

	return nil
}

type jiraCloudCreateMetaResponse struct {
	Fields []common.JiraIssueTypeField `json:"fields"`
}

func processJiraCloudFieldsOfIssueType(issueType common.JiraIssueType, project common.JiraProject, tenantID string,
	jiraEnv tenant.JiraEnvironment, jiraEndTime time.Time) error {

	var (
		createMetaUrl = jiraEnv.URL + "/rest/api/3/issue/createmeta/" + project.ID + "/issuetypes/" + issueType.ID
		basicAuth     = strings.TrimSpace(jiraEnv.Username) + ":" + strings.TrimSpace(jiraEnv.Token)
		maxResults    = 50
		startAt       = 0
		parentID      = issueType.ID
		accountID     = project.ID + "/" + issueType.ID
	)

	logger.Print(logger.INFO, "Fetching Jira Issuetypefields of issuetype "+parentID, []string{tenantID})

	for {

		fieldsResp, err := transport.SendRequest(
			"GET",
			createMetaUrl,
			map[string]string{"startAt": strconv.Itoa(startAt), "maxResults": strconv.Itoa(maxResults)},
			map[string]string{"Authorization": "Basic " + common.EncodeBase64(basicAuth)},
			nil,
		)
		if err != nil {
			break
		}

		if startAt == 0 {

			deleteTrueQuery := `{"query":{"bool":{"must":[{"term":{"tenantId.keyword":"` + tenantID + `"}},{"term":{"type.keyword":"` + common.ISSUEFIELD_JIRADATA_TYPE + `"}},{"term":{"parentId.keyword":"` + parentID + `"}},{"term":{"accountId.keyword":"` + accountID + `"}}]}},"script":"ctx._source.deleted = true;"}`

			// Every iteration, we set deleted as true so that when all are collected, we get to know which actually got deleted
			logger.Print(logger.INFO, "Identifying deleted jira issuetype fields", []string{tenantID})
			elastic.UpdateByQuery(elastic.JIRA_DATA_INDEX, deleteTrueQuery)
		}

		var jiraCreateMetaResp jiraCloudCreateMetaResponse

		if err = json.Unmarshal(fieldsResp, &jiraCreateMetaResp); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", []string{tenantID}, err)
			break
		}

		if len(jiraCreateMetaResp.Fields) <= 0 {
			break
		}

		startAt = startAt + maxResults

		var (
			recordsCount        int
			bulkJiraDataRequest string
		)

		for _, issueTypeField := range jiraCreateMetaResp.Fields {

			docID := common.GenerateCombinedHashID(issueTypeField.FieldID, common.ISSUEFIELD_JIRADATA_TYPE, parentID, tenantID, accountID)

			additionalDetails := make(map[string]any)

			additionalDetails["required"] = issueTypeField.Required

			if issueTypeField.HasDefaultValue {
				additionalDetails["defaultValue"] = issueTypeField.DefaultValue
			}

			if len(issueTypeField.Schema.Type) > 0 {
				additionalDetails["schemaType"] = issueTypeField.Schema.Type
			}

			if len(issueTypeField.Schema.Items) > 0 {
				additionalDetails["schemaItems"] = issueTypeField.Schema.Items
			}

			if len(issueTypeField.Schema.Custom) > 0 {
				additionalDetails["schemaCustom"] = issueTypeField.Schema.Custom
			}

			if len(issueTypeField.Schema.System) > 0 {
				additionalDetails["schemaSystem"] = issueTypeField.Schema.System
			}

			additionalDetailsString, err := json.Marshal(additionalDetails)
			if err != nil {
				continue
			}

			jiraDataDoc := common.JiraDataDoc{
				ID:                docID,
				JiraID:            issueTypeField.FieldID,
				Name:              issueTypeField.Name,
				Type:              common.ISSUEFIELD_JIRADATA_TYPE,
				ParentID:          parentID,
				AccountID:         accountID,
				TenantID:          tenantID,
				AdditionalDetails: string(additionalDetailsString),
				InsertTime:        elastic.DateTime(jiraEndTime),
				Deleted:           false,
			}

			jiraDataInsertMetadata := `{"index": {"_id": "` + docID + `"}}`
			jiraDataInsertDoc, err := json.Marshal(jiraDataDoc)
			if err != nil {
				logger.Print(logger.ERROR, "Failed to marshal document", err)
				continue
			}

			recordsCount++
			bulkJiraDataRequest = bulkJiraDataRequest + jiraDataInsertMetadata + "\n" + string(jiraDataInsertDoc) + "\n"

			if err = processJiraCloudFieldValues(issueType, issueTypeField, project, tenantID, jiraEnv, jiraEndTime); err != nil {
				continue
			}

		}

		if recordsCount > 0 {
			if err := elastic.BulkDocumentsAPI(tenantID, elastic.JIRA_DATA_INDEX, bulkJiraDataRequest); err != nil {
				return err
			}

			logger.Print(logger.INFO, "Jira data bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID})
		}
	}

	return nil
}

func processJiraCloudFieldValues(issueType common.JiraIssueType, issueTypeField common.JiraIssueTypeField, project common.JiraProject, tenantID string,
	jiraEnv tenant.JiraEnvironment, jiraEndTime time.Time) error {

	var (
		recordsCount        int
		bulkJiraDataRequest string
		maxRecords          = 1000
		parentID            = issueTypeField.FieldID
		accountID           = project.ID + "/" + issueType.ID + "/" + issueTypeField.FieldID
	)

	switch issueTypeField.Schema.Type {
	case "issuetype", "project", "user":
		return nil
	}

	switch issueTypeField.Schema.Items {
	case "user", "issuelinks":
		return nil
	}

	switch issueTypeField.Schema.Custom {
	case "com.pyxis.greenhopper.jira:gh-sprint":
		logger.Print(logger.INFO, "Fetching Jira values of sprint field "+parentID, []string{tenantID})
		if err := processJiraSprintValues(issueTypeField, project, tenantID, jiraEnv, jiraEndTime); err != nil {
			return err
		}

		return nil
	}

	if len(issueTypeField.AllowedValues) > 0 {

		deleteTrueQuery := `{"query":{"bool":{"must":[{"term":{"tenantId.keyword":"` + tenantID + `"}},{"term":{"type.keyword":"` + common.FIELDVALUE_JIRADATA_TYPE + `"}},{"term":{"parentId.keyword":"` + parentID + `"}},{"term":{"accountId.keyword":"` + accountID + `"}}]}},"script":"ctx._source.deleted = true;"}`

		// Every iteration, we set deleted as true so that when all are collected, we get to know which actually got deleted
		logger.Print(logger.INFO, "Identifying deleted jira issuetype field values", []string{tenantID})
		elastic.UpdateByQuery(elastic.JIRA_DATA_INDEX, deleteTrueQuery)

		logger.Print(logger.INFO, "Fetching Jira allowed values of field "+parentID, []string{tenantID})

		for _, allowedValue := range issueTypeField.AllowedValues {

			valueID, _ := allowedValue["id"].(string)
			valueName, _ := allowedValue["name"].(string)

			if len(valueID) > 0 && len(valueName) > 0 {

				docID := common.GenerateCombinedHashID(valueID, common.FIELDVALUE_JIRADATA_TYPE, parentID, tenantID, accountID)

				jiraDataDoc := common.JiraDataDoc{
					ID:         docID,
					JiraID:     valueID,
					Name:       valueName,
					Type:       common.FIELDVALUE_JIRADATA_TYPE,
					ParentID:   parentID,
					AccountID:  accountID,
					TenantID:   tenantID,
					InsertTime: elastic.DateTime(jiraEndTime),
					Deleted:    false,
				}

				jiraDataInsertMetadata := `{"index": {"_id": "` + docID + `"}}`
				jiraDataInsertDoc, err := json.Marshal(jiraDataDoc)
				if err != nil {
					logger.Print(logger.ERROR, "Failed to marshal document", err)
					continue
				}

				recordsCount++
				bulkJiraDataRequest = bulkJiraDataRequest + jiraDataInsertMetadata + "\n" + string(jiraDataInsertDoc) + "\n"

				if recordsCount > maxRecords {
					if err := elastic.BulkDocumentsAPI(tenantID, elastic.JIRA_DATA_INDEX, bulkJiraDataRequest); err != nil {
						return err
					}

					logger.Print(logger.INFO, "Jira data bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID})

					recordsCount = 0
					bulkJiraDataRequest = ""
				}
			}
		}
	} else if len(issueTypeField.AutoCompleteUrl) > 0 {

		// logger.Print(logger.INFO, "Fetching Jira values of field from url for "+parentID, []string{tenantID})

		// var (
		// 	basicAuth = strings.TrimSpace(jiraEnv.Username) + ":" + strings.TrimSpace(jiraEnv.Token)
		// 	values    = make(map[string]struct{})
		// )

		// autoCompleteResp, err := transport.SendRequest(
		// 	"GET",
		// 	issueTypeField.AutoCompleteUrl,
		// 	map[string]string{},
		// 	map[string]string{"Authorization": "Basic " + common.EncodeBase64(basicAuth)},
		// 	nil,
		// )
		// if err != nil {
		// 	return err
		// }

		// deleteTrueQuery := `{"query":{"bool":{"must":[{"term":{"tenantId.keyword":"` + tenantID + `"}},{"term":{"type.keyword":"` + common.FIELDVALUE_JIRADATA_TYPE + `"}},{"term":{"parentId.keyword":"` + parentID + `"}},{"term":{"accountId.keyword":"` + accountID + `"}}]}},"script":"ctx._source.deleted = true;"}`

		// // Every iteration, we set deleted as true so that when all are collected, we get to know which actually got deleted
		// logger.Print(logger.INFO, "Identifying deleted jira issuetype field values", []string{tenantID})
		// elastic.UpdateByQuery(elastic.JIRA_DATA_INDEX, deleteTrueQuery)

		// switch parentID {
		// case "labels":

		// 	var labelsResp struct {
		// 		Suggestions []struct {
		// 			Label string `json:"label"`
		// 		} `json:"suggestions"`
		// 	}

		// 	if err = json.Unmarshal(autoCompleteResp, &labelsResp); err != nil {
		// 		logger.Print(logger.ERROR, "Failed to unmarshal", []string{tenantID}, err)
		// 		return err
		// 	}

		// 	for _, suggestion := range labelsResp.Suggestions {
		// 		values[suggestion.Label] = struct{}{}
		// 	}
		// }

		// for value:= range values {

		// 	docID := common.GenerateCombinedHashID(value, common.FIELDVALUE_JIRADATA_TYPE, parentID, tenantID, accountID)

		// 	jiraDataDoc := common.JiraDataDoc{
		// 		ID:         docID,
		// 		JiraID:     value,
		// 		Name:       value,
		// 		Type:       common.FIELDVALUE_JIRADATA_TYPE,
		// 		ParentID:   parentID,
		//		AccountID:  accountID,
		// 		TenantID:   tenantID,
		// 		InsertTime: elastic.DateTime(jiraEndTime),
		// 		Deleted:    false,
		// 	}

		// 	jiraDataInsertMetadata := `{"index": {"_id": "` + docID + `"}}`
		// 	jiraDataInsertDoc, err := json.Marshal(jiraDataDoc)
		// 	if err != nil {
		// 		logger.Print(logger.ERROR, "Failed to marshal document", err)
		// 		continue
		// 	}

		// 	recordsCount++
		// 	bulkJiraDataRequest = bulkJiraDataRequest + jiraDataInsertMetadata + "\n" + string(jiraDataInsertDoc) + "\n"

		// 	if recordsCount > maxRecords {
		// 		if err := elastic.BulkDocumentsAPI(tenantID, elastic.JIRA_DATA_INDEX, bulkJiraDataRequest); err != nil {
		// 			return err
		// 		}

		// 		logger.Print(logger.INFO, "Jira data bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID})

		// 		recordsCount = 0
		// 		bulkJiraDataRequest = ""
		// 	}
		// }
	}

	if recordsCount > 0 {
		if err := elastic.BulkDocumentsAPI(tenantID, elastic.JIRA_DATA_INDEX, bulkJiraDataRequest); err != nil {
			return err
		}

		logger.Print(logger.INFO, "Jira data bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID})
	}

	return nil
}

func processJiraCloudUsersOfProject(project common.JiraProject, tenantID string, jiraEnv tenant.JiraEnvironment, jiraEndTime time.Time) error {

	var (
		usersUrl   = jiraEnv.URL + "/rest/api/3/user/assignable/multiProjectSearch"
		basicAuth  = strings.TrimSpace(jiraEnv.Username) + ":" + strings.TrimSpace(jiraEnv.Token)
		startAt    = 0
		maxResults = 50
		parentID   = project.ID
		accountID  = project.ID
	)

	logger.Print(logger.INFO, "Fetching Jira Users of project "+parentID, []string{tenantID})

	for {

		usersResp, err := transport.SendRequest(
			"GET",
			usersUrl,
			map[string]string{"startAt": strconv.Itoa(startAt), "maxResults": strconv.Itoa(maxResults), "projectKeys": project.Key},
			map[string]string{"Authorization": "Basic " + common.EncodeBase64(basicAuth)},
			nil,
		)
		if err != nil {
			break
		}

		if startAt == 0 {
			deleteTrueQuery := `{"query":{"bool":{"must":[{"term":{"tenantId.keyword":"` + tenantID + `"}},{"term":{"type.keyword":"` + common.USER_JIRADATA_TYPE + `"}},{"term":{"parentId.keyword":"` + parentID + `"}},{"term":{"accountId.keyword":"` + accountID + `"}}]}},"script":"ctx._source.deleted = true;"}`

			// Every iteration, we set deleted as true so that when all are collected, we get to know which actually got deleted
			logger.Print(logger.INFO, "Identifying deleted jira users of project", []string{tenantID})
			elastic.UpdateByQuery(elastic.JIRA_DATA_INDEX, deleteTrueQuery)
		}

		var jiraUsers []common.JiraUser

		if err = json.Unmarshal(usersResp, &jiraUsers); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", []string{tenantID}, err)
			break
		}

		if len(jiraUsers) <= 0 {
			break
		}

		startAt = startAt + maxResults

		var (
			recordsCount        int
			bulkJiraDataRequest string
		)

		for _, jiraUser := range jiraUsers {

			if jiraUser.AccountType != "app" {

				docID := common.GenerateCombinedHashID(jiraUser.AccountID, common.USER_JIRADATA_TYPE, parentID, tenantID, accountID)

				additionalDetails := make(map[string]any)

				if len(jiraUser.EmailAddress) > 0 {
					additionalDetails["email"] = jiraUser.EmailAddress
				}

				additionalDetailsString, err := json.Marshal(additionalDetails)
				if err != nil {
					continue
				}

				jiraDataDoc := common.JiraDataDoc{
					ID:                docID,
					JiraID:            jiraUser.AccountID,
					Name:              jiraUser.DisplayName,
					Type:              common.USER_JIRADATA_TYPE,
					ParentID:          parentID,
					AccountID:         accountID,
					TenantID:          tenantID,
					AdditionalDetails: string(additionalDetailsString),
					InsertTime:        elastic.DateTime(jiraEndTime),
					Deleted:           !jiraUser.Active,
				}

				jiraDataInsertMetadata := `{"index": {"_id": "` + docID + `"}}`
				jiraDataInsertDoc, err := json.Marshal(jiraDataDoc)
				if err != nil {
					logger.Print(logger.ERROR, "Failed to marshal document", err)
					continue
				}

				recordsCount++
				bulkJiraDataRequest = bulkJiraDataRequest + jiraDataInsertMetadata + "\n" + string(jiraDataInsertDoc) + "\n"
			}
		}

		if recordsCount > 0 {
			if err := elastic.BulkDocumentsAPI(tenantID, elastic.JIRA_DATA_INDEX, bulkJiraDataRequest); err != nil {
				return err
			}

			logger.Print(logger.INFO, "Jira data bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID})
		}
	}

	return nil
}

func processJiraCloudUsers(tenantID string, jiraEnv tenant.JiraEnvironment, jiraEndTime time.Time) error {

	logger.Print(logger.INFO, "Fetching all Jira Users", []string{tenantID})

	var (
		usersUrl   = jiraEnv.URL + "/rest/api/3/users/search"
		basicAuth  = strings.TrimSpace(jiraEnv.Username) + ":" + strings.TrimSpace(jiraEnv.Token)
		maxResults = 50
		startAt    = 0
		parentID   = tenantID
		accountID  = jiraEnv.URL
	)

	for {

		usersResp, err := transport.SendRequest(
			"GET",
			usersUrl,
			map[string]string{"startAt": strconv.Itoa(startAt), "maxResults": strconv.Itoa(maxResults)},
			map[string]string{"Authorization": "Basic " + common.EncodeBase64(basicAuth)},
			nil,
		)
		if err != nil {
			break
		}

		if startAt == 0 {
			deleteTrueQuery := `{"query":{"bool":{"must":[{"term":{"tenantId.keyword":"` + tenantID + `"}},{"term":{"type.keyword":"` + common.USER_JIRADATA_TYPE + `"}},{"term":{"parentId.keyword":"` + parentID + `"}},{"term":{"accountId.keyword":"` + accountID + `"}},{"match":{"deleted":"false"}}]}},"script":"ctx._source.deleted = true;"}`

			// Every iteration, we set deleted as true so that when all are collected, we get to know which actually got deleted
			logger.Print(logger.INFO, "Identifying deleted jira users", []string{tenantID})
			elastic.UpdateByQuery(elastic.JIRA_DATA_INDEX, deleteTrueQuery)
		}

		logger.Print(logger.INFO, "Fetching Deleted Jira users", []string{tenantID})

		var (
			searchAfter           any
			jiraUsersQuery        = `{"_source":["id"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"type.keyword":"user"}},{"match":{"deleted":"true"}}]}}}`
			deletedJiraUsersDocId = make(map[string]struct{})
		)

		for {
			cloudUserIdentitiesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.JIRA_DATA_INDEX}, jiraUsersQuery, searchAfter)
			if err != nil {
				break
			}

			if len(cloudUserIdentitiesDocs) > 0 {
				searchAfter = sortResponse
			} else {
				break
			}

			for identityDocID := range cloudUserIdentitiesDocs {
				deletedJiraUsersDocId[identityDocID] = struct{}{}
			}
		}

		var jiraUsers []common.JiraUser
		if err = json.Unmarshal(usersResp, &jiraUsers); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", []string{tenantID}, err)
			break
		}

		if len(jiraUsers) <= 0 {
			break
		}

		startAt = startAt + maxResults

		var (
			recordsCount        int
			bulkJiraDataRequest string
		)

		for _, jiraUser := range jiraUsers {

			if jiraUser.AccountType != "app" {

				docID := common.GenerateCombinedHashID(jiraUser.AccountID, common.USER_JIRADATA_TYPE, parentID, tenantID, accountID)

				if _, ok := deletedJiraUsersDocId[docID]; ok {
					continue
				}

				additionalDetails := make(map[string]any)

				if len(jiraUser.EmailAddress) > 0 {
					additionalDetails["email"] = jiraUser.EmailAddress
				}

				additionalDetailsString, err := json.Marshal(additionalDetails)
				if err != nil {
					continue
				}

				jiraDataDoc := common.JiraDataDoc{
					ID:                docID,
					JiraID:            jiraUser.AccountID,
					Name:              jiraUser.DisplayName,
					Type:              common.USER_JIRADATA_TYPE,
					ParentID:          parentID,
					AccountID:         accountID,
					TenantID:          tenantID,
					AdditionalDetails: string(additionalDetailsString),
					InsertTime:        elastic.DateTime(jiraEndTime),
					Deleted:           !jiraUser.Active,
				}

				jiraDataInsertMetadata := `{"index": {"_id": "` + docID + `"}}`
				jiraDataInsertDoc, err := json.Marshal(jiraDataDoc)
				if err != nil {
					logger.Print(logger.ERROR, "Failed to marshal document", err)
					continue
				}

				recordsCount++
				bulkJiraDataRequest = bulkJiraDataRequest + jiraDataInsertMetadata + "\n" + string(jiraDataInsertDoc) + "\n"
			}
		}

		if recordsCount > 0 {
			if err := elastic.BulkDocumentsAPI(tenantID, elastic.JIRA_DATA_INDEX, bulkJiraDataRequest); err != nil {
				return err
			}

			logger.Print(logger.INFO, "Jira data bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID})
		}
	}

	return nil
}

func getCloudTicketChangeLog(tenantID string, jiraEnv tenant.JiraEnvironment, jiraIssueDoc common.JiraIssuesDoc, jiraTicketContext *jirautils.JiraTicketContext) (err error) {
	changelog, err := fetchJiraChangelog(tenantID, jiraEnv, jiraIssueDoc.IssueID)
	if err != nil {
		return err
	}

	return processChangelogAndUpdateTicket(tenantID, changelog, jiraTicketContext)
}

func fetchJiraChangelog(tenantID string, jiraEnv tenant.JiraEnvironment, issueID string) ([]common.ChangeLogValue, error) {
	var (
		basicAuth      = strings.TrimSpace(jiraEnv.Username) + ":" + strings.TrimSpace(jiraEnv.Token)
		issueSearchUrl = jiraEnv.URL + "/rest/api/3/issue/" + issueID + "/changelog"
		maxResults     = 50
		startAt        = 0
		allChangelogs  []common.ChangeLogValue
	)

	for {
		changeLogResp, err := transport.SendRequest(
			"GET",
			issueSearchUrl,
			map[string]string{
				"startAt":    strconv.Itoa(startAt),
				"maxResults": strconv.Itoa(maxResults),
				"fields":     "*all",
			},
			map[string]string{
				"Authorization": "Basic " + common.EncodeBase64(basicAuth),
			},
			nil,
		)
		if err != nil {
			return nil, err
		}

		var jiraChngLogResp common.JiraCloudChangelog
		if err = json.Unmarshal(changeLogResp, &jiraChngLogResp); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", []string{tenantID}, err)
			return nil, err
		}

		allChangelogs = append(allChangelogs, jiraChngLogResp.Values...)

		if jiraChngLogResp.IsLast {
			break
		}
		startAt = startAt + maxResults
	}

	return allChangelogs, nil
}

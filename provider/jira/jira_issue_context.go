package jira

import (
	"encoding/json"
	"regexp"
	"strings"

	"github.com/precize/common"
	"github.com/precize/common/context"
	"github.com/precize/common/jira"
	"github.com/precize/enhancer/context"
	"github.com/precize/enhancer/source/attribute"
	"github.com/precize/logger"
)

var serviceTypeMap = map[string]map[string]string{
	common.AWS_SERVICE_ID:   common.AWSKeywordToResourceTypeMap,
	common.AZURE_SERVICE_ID: common.AzureKeywordToResourceTypeMap,
	common.GCP_SERVICE_ID:   common.GCPKeywordToResourceTypeMap,
}

func analyzeTicketDescription(jiraIssueDoc common.JiraIssuesDoc, tenantID string) (*jirautils.JiraTicketContext, error) {

	description := jiraIssueDoc.Description
	if len(jiraIssueDoc.Summary) > 0 {
		description += "\n" + "The title of the ticket is " + jiraIssueDoc.Summary
	}

	if len(jiraIssueDoc.Comments) > 0 {
		description += "\n" + "The comments in the ticket are as follows: "
		for _, comment := range jiraIssueDoc.Comments {
			// avoid issue link related comments
			if !strings.Contains(strings.ToLower(comment.Body), "mentioned this issue in a commit") {
				description += "\n" + comment.Body
			}
		}
	}

	req := []common.OpenAIMessage{
		{
			Role:    "system",
			Content: "You are a Jira ticket analyzer.",
		},
		{
			Role: "user",
			Content: `Extract the following information for each resource mentioned:
				1. Entity type
				2. Entity ID/name
				3. Account ID/name (account, project, folder under which entity is present)
				4. Service Id should be string not integer ("1000" - aws, "2000" - azure, "3000" - gcp)
				5. Environment (Sandbox, Development, QA/Staging)
				6. Deployments/ User Agents (AWS CloudFormation, Kubernetes, Terraform, Helm)
				7. Region (if present)
				8. OS type (if present)
				9. Data sensitivities (PII, PCI, PHI, Confidential, Restricted)
				10. Software components running inside the resource ("MongoDB", "ElasticSearch", "PostgreSQL", "SQL", "Oracle", "Redis", "Cassandra", "Neo4j", "VPN", "NAT", "NGINX", "NodeJs")

				Give response in the following format strictly: 
				{
					"Resources": [
						{
							"entityType": "",
							"entityId": "",
							"serviceId": "",
							"accountId": "",
							"environment": "",
							"deployment": [""],
							"region": "",
							"osType": "",
							"dataSensitivity": [""],
							"softwareComponents": [""]
						}
					]
				}

				Important:
				- User agents are ONLY tools used to create/modify resources
				- Software components are ONLY software running on or related to resources
				- These categories are mutually exclusive
				- Cloud providers should not be part of deployments
				- EntityId will always be a single word

				If any field is not found, use an empty string. Ensure the response is in valid format.
				Return empty Resources if it is not a cloud related ticket or no proper cloud resource is extracted
				Text to analyze: ` + description,
		},
	}

	jiraRespString := common.DeriveJiraContextFromText(description, tenantID, req)

	if jiraRespString == "" {
		logger.Print(logger.INFO, "Empty openAI response", []string{tenantID}, jiraIssueDoc.ID)
		return nil, nil
	}

	var ticketContext jirautils.JiraTicketContext
	if err := json.Unmarshal([]byte(jiraRespString), &ticketContext); err != nil {

		// open return ```json in response sometimes which fails the parsing
		if strings.Contains(jiraRespString, "```json") {
			jiraRespString = strings.ReplaceAll(jiraRespString, "```json", "")
			jiraRespString = strings.ReplaceAll(jiraRespString, "```", "")
			jiraRespString = strings.TrimSpace(jiraRespString)

			if err := json.Unmarshal([]byte(jiraRespString), &ticketContext); err != nil {
				logger.Print(logger.ERROR, "Failed to unmarshall openAI response", []string{tenantID}, jiraRespString, err)
				return nil, err
			}
		} else {
			logger.Print(logger.ERROR, "Failed to unmarshall openAI response", []string{tenantID}, jiraRespString, err)
			return nil, err
		}
	}

	// Convert entityId to lowercase for all resources
	for i := range ticketContext.Resources {
		ticketContext.Resources[i].EntityID = strings.ToLower(ticketContext.Resources[i].EntityID)
	}

	updateResourceType(&ticketContext)
	updateAITicketContext(&ticketContext)

	return &ticketContext, nil
}

func getTicketContextFromField(tenantID, projectID, issueTypeID string, issuesFields map[string]any, jiraTicketContext *jirautils.JiraTicketContext, fieldIdToName map[string]string) (err error) {

	var (
		uniqueDeployments   = make(map[string]struct{})
		uniqueSoftware      = make(map[string]struct{})
		uniqueSensitivities = make(map[string]struct{})
		uniqueEnvironments  = make(map[string]struct{})
		uniqueApps          = make(map[string]struct{})
	)

	for fieldKey, v := range issuesFields {

		if v == nil {
			continue
		}

		var fieldValue string
		switch val := v.(type) {
		case string:
			if len(val) > 0 {
				fieldValue = val
			}
		case map[string]any:
			vBytes, err := json.Marshal(val)
			if err != nil {
				logger.Print(logger.ERROR, "Failed to marshal field value to JSON", []string{tenantID}, err)
				continue
			}

			var cFieldVal common.JiraCustomField
			if err = json.Unmarshal(vBytes, &cFieldVal); err != nil {
				logger.Print(logger.ERROR, "Failed to unmarshal field value", []string{tenantID}, err)
				continue
			}

			if cFieldVal.Value != "" {
				fieldValue = cFieldVal.Value
			}
		case []any:
			if fieldKey == "labels" {
				for _, label := range val {

					if label, ok := label.(string); ok {
						if deploymentNames := context.GetDeploymentNamesFromValue(label); len(deploymentNames) > 0 {

							for _, deploymentName := range deploymentNames {
								if _, ok := uniqueDeployments[deploymentName]; !ok {
									uniqueDeployments[deploymentName] = struct{}{}
								}
							}
						}

						if sensitivityNames := context.GetSensitivityNameListFromValue(label); len(sensitivityNames) > 0 {

							for _, sensitivityName := range sensitivityNames {
								if _, ok := uniqueSensitivities[sensitivityName]; !ok {
									uniqueSensitivities[sensitivityName] = struct{}{}
								}
							}
						}

						if envName := context.GetEnvironmentNameFromValue(label); len(envName) > 0 {

							if _, ok := uniqueEnvironments[envName]; !ok {
								uniqueEnvironments[envName] = struct{}{}
							}
						}

						if softwareName := context.GetSoftwareNameFromValue(label); len(softwareName) > 0 {
							if _, ok := uniqueSoftware[softwareName]; !ok {
								uniqueSoftware[softwareName] = struct{}{}
							}
						}
					}
				}
			}
		}

		if fieldKey, ok := fieldIdToName[projectID+"/"+issueTypeID+"/"+fieldKey]; ok {

			if attribute.IsDescriptionKey(fieldKey) {

				if softwareNames := context.GetSoftwareNameListFromValue(fieldValue); len(softwareNames) > 0 {
					for _, softwareName := range softwareNames {
						if _, ok := uniqueSoftware[softwareName]; ok {
							continue
						}
						uniqueSoftware[softwareName] = struct{}{}
					}
				}

				if deploymentNames := context.GetDeploymentNamesFromValue(fieldValue); len(deploymentNames) > 0 {
					for _, deploymentName := range deploymentNames {
						if _, ok := uniqueDeployments[deploymentName]; ok {
							continue
						}
						uniqueDeployments[deploymentName] = struct{}{}
					}
				}

				if sensitivityNames := context.GetSensitivityNameListFromValue(fieldValue); len(sensitivityNames) > 0 {
					for _, sensitivityName := range sensitivityNames {
						if _, ok := uniqueSensitivities[sensitivityName]; ok {
							continue
						}
						uniqueSensitivities[sensitivityName] = struct{}{}
					}
				}
			}

			for envTagKey := range contextutils.EnvTagKeys {
				if strings.Contains(strings.ToLower(fieldKey), envTagKey) {
					if envName := context.GetEnvironmentNameFromValue(fieldValue); len(envName) > 0 {
						if _, ok := uniqueEnvironments[envName]; !ok {
							uniqueEnvironments[envName] = struct{}{}
						}
					}
				}
			}

			if _, ok := contextutils.SoftwareTagKeys[strings.ToLower(fieldKey)]; ok {

				var softwareNameFieldValue = fieldValue

				if softwareName := context.GetSoftwareNameFromValue(fieldValue); len(softwareName) > 0 {
					softwareNameFieldValue = softwareName

					if _, ok := uniqueSoftware[softwareNameFieldValue]; !ok {
						uniqueSoftware[softwareNameFieldValue] = struct{}{}
					}
				}
			}

			if deploymentNames := context.GetDeploymentNamesFromValue(fieldKey); len(deploymentNames) > 0 {
				for _, deploymentName := range deploymentNames {
					if !strings.Contains(fieldValue, "false") {
						if _, ok := uniqueDeployments[deploymentName]; !ok {
							uniqueDeployments[deploymentName] = struct{}{}
						}
					}
				}
			}

			if deploymentNames := context.GetDeploymentNamesFromValue(fieldValue); len(deploymentNames) > 0 {
				for _, deploymentName := range deploymentNames {
					if _, ok := uniqueDeployments[deploymentName]; !ok {
						uniqueDeployments[deploymentName] = struct{}{}
					}
				}
			}

			if sensitivityName := context.GetSensitivityNameFromValue(fieldKey); len(sensitivityName) > 0 {
				if !strings.Contains(fieldValue, "false") {
					if _, ok := uniqueSensitivities[sensitivityName]; !ok {
						uniqueSensitivities[sensitivityName] = struct{}{}
					}
				}
			}

			if sensitivityName := context.GetSensitivityNameFromValue(fieldValue); len(sensitivityName) > 0 {
				if _, ok := uniqueSensitivities[sensitivityName]; !ok {
					uniqueSensitivities[sensitivityName] = struct{}{}
				}
			}

		}
	}

	for i, ticket := range jiraTicketContext.Resources {
		for env := range uniqueEnvironments {
			ticket.Environment = env
		}
		for app := range uniqueApps {
			ticket.Applications = append(ticket.Applications, app)
		}
		for sensitivity := range uniqueSensitivities {
			ticket.DataSensitivity = append(ticket.DataSensitivity, sensitivity)
		}
		for deployment := range uniqueDeployments {
			ticket.Deployment = append(ticket.Deployment, deployment)
		}

		jiraTicketContext.Resources[i] = ticket
	}

	return
}

// Not being used today
// func fetchTicketResourceAccountId(ticketContext *jirautils.JiraTicketContext, jiraIssueDoc common.JiraIssuesDoc) (err error) {

// 	var (
// 		searchAfter     any
// 		parentResources = make(map[string]map[string]any)
// 	)

// 	parentResourcesQuery := `{"_source":["entityId","resourceName","entityType","serviceId"],"query":{"bool":{"must":[{"term":{"tenantId.keyword":"` + jiraIssueDoc.TenantID + `"}},{"terms":{"entityType.keyword":["` + common.AZURE_SUBSCRIPTION_RESOURCE_TYPE + `","` + common.AWS_ACCOUNT_RESOURCE_TYPE + `","` + common.GCP_PROJECT_RESOURCE_TYPE + `"]}}]}}}`

// 	for {
// 		parentResourcesDoc, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCE_STORE_INDEX}, parentResourcesQuery, searchAfter)
// 		if err != nil {
// 			return err
// 		}

// 		if len(parentResourcesDoc) > 0 {
// 			searchAfter = sortResponse
// 		} else {
// 			break
// 		}

// 		for k, v := range parentResourcesDoc {
// 			parentResources[k] = v
// 		}
// 	}

// 	var (
// 		resourceTypes = make([]string, 0)
// 		parentIds     = make([]string, 0)
// 		ok            bool
// 	)
// 	for _, parentResource := range parentResources {
// 		identifiers := make([]string, 0, 2)
// 		var parentID string

// 		if parentID, ok = parentResource["entityId"].(string); ok {
// 			identifiers = append(identifiers, parentID)
// 		}

// 		if resourceName, ok := parentResource["resourceName"].(string); ok {
// 			resourceName = strings.Replace(strings.ToLower(resourceName), " ", `[-_\s]*`, -1)
// 			identifiers = append(identifiers, resourceName)
// 		}

// 		for _, identifier := range identifiers {
// 			regex := regexp.MustCompile(identifier)
// 			if !regex.MatchString(strings.ToLower(jiraIssueDoc.Summary)) &&
// 				!regex.MatchString(strings.ToLower(jiraIssueDoc.Description)) {
// 				continue
// 			}

// 			serviceIDs, ok := parentResource["serviceId"].([]any)
// 			if !ok {
// 				continue
// 			}

// 			for _, serviceID := range serviceIDs {
// 				serviceIDFloat, ok := serviceID.(float64)
// 				if !ok {
// 					continue
// 				}

// 				serviceIDInt := int(serviceIDFloat)
// 				serviceIDStr := fmt.Sprintf("%v", serviceIDInt)
// 				keywordMap, exists := serviceTypeMap[serviceIDStr]
// 				if !exists {
// 					continue
// 				}

// 				ticketText := strings.ToLower(jiraIssueDoc.Summary + " " + jiraIssueDoc.Description)

// 				for keyword, resourceType := range keywordMap {
// 					regex := regexp.MustCompile(keyword)
// 					if regex.MatchString(ticketText) {
// 						if !slices.Contains(resourceTypes, resourceType) {
// 							resourceTypes = append(resourceTypes, resourceType)
// 						}
// 						if !slices.Contains(parentIds, parentID) {
// 							parentIds = append(parentIds, parentID)
// 						}
// 					}
// 				}
// 			}
// 		}
// 	}

// 	if len(resourceTypes) > 0 {

// 		// propagate the resource type and account id to the ticket context
// 		if len(resourceTypes) == 1 && len(ticketContext.Resources) == 1 {
// 			ticketContext.Resources[0].EntityType = resourceTypes[0]
// 			ticketContext.Resources[0].AccountID = parentIds[0]
// 		} else if len(resourceTypes) == len(ticketContext.Resources) {
// 			for i := range resourceTypes {
// 				ticketContext.Resources[i].EntityType = resourceTypes[i]
// 				ticketContext.Resources[i].AccountID = parentIds[i]
// 			}
// 		} else {
// 			//TODO: if more than one resources and count of resourceType and parentIds does not match (based on customer data)
// 		}
// 	}

// 	return
// }

func processChangelogAndUpdateTicket(tenantID string, changelogs []common.ChangeLogValue, jiraTicketContext *jirautils.JiraTicketContext) error {
	ownerChangeLogMap := make([]common.AssigneeChangeLog, 0)

	for _, value := range changelogs {
		for _, item := range value.Items {
			if item.Field == "assignee" {
				ownerChangeLogMap = append(ownerChangeLogMap, common.AssigneeChangeLog{
					FromAssignee: item.FromString,
					ToAssignee:   item.ToString,
					CreatedTime:  value.Created,
					Author:       value.Author.DisplayName,
				})
			}
		}
	}

	for i, ticket := range jiraTicketContext.Resources {
		additionalDetails := make(map[string]any)

		ownerChangeLogByte, err := json.Marshal(ownerChangeLogMap)
		if err != nil {
			logger.Print(logger.ERROR, "Failed to marshal owner change log", []string{tenantID}, err)
			return err
		}

		additionalDetails["ownerChangeLog"] = string(ownerChangeLogByte)

		additionalDetailsByte, err := json.Marshal(additionalDetails)
		if err != nil {
			logger.Print(logger.ERROR, "Failed to marshal owner change log", []string{tenantID}, err)
			return err
		}

		ticket.AdditionalDetails = string(additionalDetailsByte)
		jiraTicketContext.Resources[i] = ticket
	}

	return nil
}

func updateResourceType(ticketContext *jirautils.JiraTicketContext) {

	for i, resource := range ticketContext.Resources {
		keywordMap, exists := serviceTypeMap[resource.ServiceID]
		if !exists {
			continue
		}

		for keyword, resourceType := range keywordMap {
			regex := regexp.MustCompile(keyword)
			if regex.MatchString(resource.EntityType) {
				resource.EntityType = resourceType
				break
			}
		}

		ticketContext.Resources[i] = resource
	}
}

func updateAITicketContext(ticketContext *jirautils.JiraTicketContext) {
	var (
		uniqueDeployments   = make(map[string]struct{})
		uniqueApps          = make(map[string]struct{})
		uniqueSoftware      = make(map[string]struct{})
		uniqueSensitivities = make(map[string]struct{})
	)

	for i, ticketContextResource := range ticketContext.Resources {
		for _, deployment := range ticketContextResource.Deployment {
			if deploymentNames := context.GetDeploymentNamesFromValue(deployment); len(deploymentNames) > 0 {

				for _, deploymentName := range deploymentNames {
					if _, ok := uniqueDeployments[deploymentName]; !ok {
						uniqueDeployments[deploymentName] = struct{}{}
					}
				}
			}
		}

		for dep := range uniqueDeployments {
			ticketContextResource.Deployment = append(ticketContextResource.Deployment, dep)
		}

		for _, sensitity := range ticketContextResource.DataSensitivity {
			if sensitivityNames := context.GetSensitivityNameListFromValue(sensitity); len(sensitivityNames) > 0 {
				for _, sensitivityName := range sensitivityNames {
					if _, ok := uniqueSensitivities[sensitivityName]; !ok {
						uniqueSensitivities[sensitivityName] = struct{}{}
					}
				}
			}
		}

		for sensi := range uniqueSensitivities {
			ticketContextResource.DataSensitivity = append(ticketContextResource.DataSensitivity, sensi)
		}

		if envName := context.GetEnvironmentNameFromValue(ticketContextResource.Environment); len(envName) > 0 {
			ticketContextResource.Environment = envName
		}

		for _, software := range ticketContextResource.Software {
			if softwareName := context.GetSoftwareNameFromValue(software); len(softwareName) > 0 {
				if _, ok := uniqueSoftware[softwareName]; !ok {
					uniqueSoftware[softwareName] = struct{}{}
				}
			}
		}

		for software := range uniqueSoftware {
			ticketContextResource.Software = append(ticketContextResource.Software, software)
		}

		for _, application := range ticketContextResource.Applications {
			if appName := context.GetAppNameFromValue(application); len(appName) > 0 {
				if _, ok := uniqueApps[appName]; !ok {
					uniqueApps[appName] = struct{}{}
				}
			}
		}

		for app := range uniqueApps {
			ticketContextResource.Applications = append(ticketContextResource.Applications, app)
		}

		ticketContext.Resources[i] = ticketContextResource
	}
}

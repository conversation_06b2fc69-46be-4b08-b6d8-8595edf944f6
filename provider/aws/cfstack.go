package aws

import (
	"context"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/credentials/stscreds"
	"github.com/aws/aws-sdk-go-v2/service/cloudformation"
	"github.com/aws/aws-sdk-go-v2/service/sts"

	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
)

const DEFAULT_REGION = "us-east-1"

func ProcessStackTemplates(tenantID, externalID, accessKey, secretToken string, awsAccount tenant.AWSAccountDetails, eventStartTime, eventEndTime, tenantStartTime time.Time) {

	defaultTime := tenant.FetchAttributeDefaultTime(tenant.STACK_TEMPLATE_EVENT, tenantStartTime)

	if (eventEndTime.Sub(eventStartTime)) > eventEndTime.Sub(defaultTime) {
		eventStartTime = defaultTime
	}

	lastEventTime := eventStartTime

	basicCreds := aws.NewCredentialsCache(
		credentials.NewStaticCredentialsProvider(
			accessKey,
			secretToken,
			"",
		),
	)

	cfg, err := config.LoadDefaultConfig(
		context.TODO(),
		config.WithRegion(DEFAULT_REGION),
		config.WithCredentialsProvider(basicCreds),
	)
	if err != nil {
		logger.Print(logger.ERROR, "Got error loading assumer config", []string{tenantID}, err)
		return
	}

	if len(awsAccount.AssumedRole) > 0 {

		// assume role using the assumer config above
		stsclient := sts.NewFromConfig(cfg)
		assumedCreds := stscreds.NewAssumeRoleProvider(
			stsclient,
			awsAccount.AssumedRole,
			func(o *stscreds.AssumeRoleOptions) {
				o.ExternalID = aws.String(externalID)
			},
		)

		cfg.Credentials = aws.NewCredentialsCache(assumedCreds)
	}

	accountID := awsAccount.AccountID
	cloudFormationClient := cloudformation.NewFromConfig(cfg)

	startTime := elastic.DateTime(eventStartTime)
	endTime := elastic.DateTime(eventEndTime)

	logger.Print(logger.INFO, "Fetching stack events from "+startTime+" to "+endTime, []string{tenantID, accountID})

	searchQuery := `{"query":{"bool":{"filter":[{"terms":{"eventName.keyword":["CreateStack", "UpdateStack", "ExecuteChangeSet"]}},{"range":{"eventTime":{"gt":"` + startTime + `","lte":"` + endTime + `"}}},{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"accountId.keyword":"` + accountID + `"}},{"term":{"readOnly":"false"}}]}}}`

	stackTemplateEventDocs, err := elastic.ExecuteSearchQuery([]string{elastic.CLOUD_ACTIVITY_INDEX}, searchQuery)
	if err != nil {
		return
	}

	var stackTemplateEvents []StackTemplateEvent

	for _, stackTemplateEventDoc := range stackTemplateEventDocs {

		var stackTemplateEvent = StackTemplateEvent{
			Account: accountID,
		}

		if resourceInterfaces, ok := stackTemplateEventDoc["resources"].([]any); ok {

			for _, resourceInterface := range resourceInterfaces {

				if resourceMap, ok := resourceInterface.(map[string]any); ok {

					if resourceType, ok := resourceMap["resourceType"].(string); ok {

						if resourceType == "AWS::CloudFormation::Stack" {

							if resourceName, ok := resourceMap["resourceName"].(string); ok {

								stackTemplateEvent.StackID = resourceName

								if region, ok := stackTemplateEventDoc["region"].(string); ok {
									stackTemplateEvent.Region = region
								}

								if eventTimeString, ok := stackTemplateEventDoc["eventTime"].(string); ok {

									stackTemplateEvent.EventTime = eventTimeString

									eventTime, err := time.Parse(elastic.DATE_FORMAT, eventTimeString)
									if err != nil {
										logger.Print(logger.ERROR, "Got error parsing event time", []string{tenantID, accountID}, err)
										// return
									}

									if eventTime.After(lastEventTime) {
										lastEventTime = eventTime
									}
								}

								if tenantID, ok := stackTemplateEventDoc["tenantId"].(string); ok {
									stackTemplateEvent.TenantID = tenantID
								}

								stackTemplateEvents = append(stackTemplateEvents, stackTemplateEvent)

							}
						}
					}
				}
			}
		}
	}

	for _, templateEvent := range stackTemplateEvents {

		err = processStackTemplate(templateEvent, cloudFormationClient)
		if err != nil {
			continue
		}
	}

	tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.STACK_TEMPLATE_EVENT, lastEventTime)
}

// Not being used today
// func getAWSAccount(cfg aws.Config, tenantID string) (accountID string, err error) {

// 	stsclient := sts.NewFromConfig(cfg)
// 	r, err := stsclient.GetCallerIdentity(context.TODO(), &sts.GetCallerIdentityInput{}, func(o *sts.Options) {
// 		o.Region = DEFAULT_REGION
// 	})
// 	if err != nil {
// 		logger.Print(logger.ERROR, "Got error getting account id", []string{tenantID}, err)
// 		return
// 	}

// 	if r.Account != nil {
// 		accountID = *r.Account
// 	}

// 	return
// }

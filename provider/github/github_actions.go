package github

import (
	"archive/zip"
	"bytes"
	"context"
	"fmt"
	"io"
	"strconv"
	"strings"
	"time"

	"github.com/google/go-github/v48/github"
	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
	"github.com/precize/transport"
)

const (
	bufferTime = 12 * time.Hour
)

func ProcessGithubWorkflows(tenantID string, githubEnv tenant.GithubEnvironment, workflowStartTime, workflowEndTime, tenantStartTime time.Time) {
	if workflowEndTime.Sub(workflowStartTime) < bufferTime {
		return
	}

	defaultTime := tenant.FetchAttributeDefaultTime(tenant.GITHUB_WORKFLOW, tenantStartTime)

	if (workflowEndTime.Sub(workflowStartTime)) > workflowEndTime.Sub(defaultTime) {
		workflowStartTime = defaultTime
	}

	accessToken, err := getGithubAccessToken(tenantID, githubEnv.Token)
	if err != nil {
		return
	}

	githubCtx := context.Background()
	githubClient := CreateGithubClient(githubCtx, accessToken)

	var (
		crsQuery            = `{"_source":["resourceName", "entityId"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"terms":{"entityType.keyword":["` + common.GCP_PROJECT_RESOURCE_TYPE + `", "` + common.GCP_FOLDER_RESOURCE_TYPE + `", "` + common.GCP_ORG_RESOURCE_TYPE + `"]}},{"term":{"serviceId":` + common.GCP_SERVICE_ID + `}}]}}}`
		searchAfter         any
		parentRscNameToID   = make(map[string]string)
		missingNormalizeRsc = make(map[string]string)
	)

	for {
		crsDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCE_STORE_INDEX}, crsQuery, searchAfter)
		if err != nil {
			return
		}

		if len(crsDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		for _, crsDoc := range crsDocs {
			if resourceName, ok := crsDoc["resourceName"].(string); ok {
				if entityID, ok := crsDoc["entityId"].(string); ok {
					if len(resourceName) > 0 && len(entityID) > 0 {
						parentRscNameToID[resourceName] = entityID
					}
				}
			}
		}
	}

	repoMap := make(map[string]struct{})
	for _, r := range githubEnv.Repositories {
		repoMap[r] = struct{}{}
	}

	globalErr := processGithubRepositories(githubCtx, githubClient, tenantID, repoMap, workflowStartTime, workflowEndTime, parentRscNameToID, missingNormalizeRsc)

	if len(missingNormalizeRsc) > 0 {
		var missingResources []string
		for resourceName, entityID := range missingNormalizeRsc {
			missingResources = append(missingResources, resourceName+" ("+entityID+")")
		}

		common.SendEmailForResourceNormalizationFromDeploymentLogs(missingResources, tenantID, "github")
	}

	if globalErr == nil {
		tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.GITHUB_WORKFLOW, workflowEndTime)
	}
}

func processGithubRepositories(ctx context.Context, client *github.Client, tenantID string, repoMap map[string]struct{},
	startTime, endTime time.Time, parentRscNameToID map[string]string, missingNormalizeRsc map[string]string) error {

	var globalErr error
	repoPage := 1

	for {
		repos, repoResp, err := client.Repositories.List(ctx, "", &github.RepositoryListOptions{
			ListOptions: github.ListOptions{
				Page:    repoPage,
				PerPage: 90,
			},
		})
		if err != nil {
			logger.Print(logger.ERROR, "Got error listing repos", []string{tenantID}, err)
			return err
		}

		for _, repo := range repos {
			repoName := *repo.Name
			repoOwner := *repo.Owner.Login

			if _, ok := repoMap[repoName]; !ok {
				continue
			}

			logger.Print(logger.INFO, "Processing repository "+repoName, []string{tenantID})

			if err := processGithubWorkflows(ctx, client, tenantID, repoOwner, repoName, startTime, endTime, &globalErr, parentRscNameToID, missingNormalizeRsc); err != nil {
				continue
			}
		}

		if repoResp == nil || repoResp.NextPage == 0 {
			break
		}

		repoPage = repoResp.NextPage
		time.Sleep(1 * time.Second)
	}

	return globalErr
}

func processGithubWorkflows(ctx context.Context, client *github.Client, tenantID, repoOwner, repoName string,
	startTime, endTime time.Time, globalError *error,
	parentRscNameToID map[string]string, missingNormalizeRsc map[string]string) error {

	workflowPage := 1
	var successfulRuns []*github.WorkflowRun
	createdRange := fmt.Sprintf("%s..%s", startTime.Format(time.RFC3339), endTime.Format(time.RFC3339))

	for {
		runs, resp, err := client.Actions.ListRepositoryWorkflowRuns(ctx, repoOwner, repoName, &github.ListWorkflowRunsOptions{
			Status:  "success",
			Created: *github.String(createdRange),
			ListOptions: github.ListOptions{
				Page:    workflowPage,
				PerPage: 30,
			},
		})

		if err != nil {
			*globalError = err
			logger.Print(logger.ERROR, "Error listing workflow runs", []string{tenantID, repoOwner, repoName}, err)
			return err
		}

		for _, run := range runs.WorkflowRuns {
			if run.CreatedAt != nil {
				if run.CreatedAt.Time.Before(startTime) {
					resp.NextPage = 0
					break
				}

				if run.CreatedAt.Time.After(endTime) {
					continue
				}

				successfulRuns = append(successfulRuns, run)
			}
		}

		if resp.NextPage == 0 {
			break
		}

		workflowPage = resp.NextPage
		time.Sleep(1 * time.Second)
	}

	for _, run := range successfulRuns {
		err := processGithubWorkflowRun(ctx, client, tenantID, repoOwner, repoName, run, parentRscNameToID, missingNormalizeRsc)
		if err != nil {
			continue
		}
	}

	return nil
}

func processGithubWorkflowRun(ctx context.Context, client *github.Client, tenantID, repoOwner, repoName string,
	run *github.WorkflowRun, parentRscNameToID map[string]string, missingNormalizeRsc map[string]string) error {

	logURL, _, err := client.Actions.GetWorkflowRunLogs(ctx, repoOwner, repoName, *run.ID, true)
	if err != nil && !strings.Contains(err.Error(), "410 Gone") {
		logger.Print(logger.ERROR, "Error getting workflow run logs", []string{tenantID, repoOwner, repoName, strconv.FormatInt(*run.ID, 10)}, err)
		return err
	}

	bytesData, err := transport.SendRequest("GET", logURL.String(), nil, nil, nil)
	if err != nil {
		logger.Print(logger.ERROR, "Error fetching workflow run logs", []string{tenantID, repoOwner, repoName, strconv.FormatInt(*run.ID, 10)}, err)
		return err
	}

	zipReader, err := zip.NewReader(bytes.NewReader(bytesData), int64(len(bytesData)))
	if err != nil {
		logger.Print(logger.ERROR, "Error creating zip reader", []string{tenantID, repoOwner, repoName, strconv.FormatInt(*run.ID, 10)}, err)
		return err
	}

	for _, file := range zipReader.File {
		if !strings.HasSuffix(file.Name, ".txt") {
			continue
		}

		logger.Print(logger.INFO, "Reading file", []string{file.Name})

		rc, err := file.Open()
		if err != nil {
			logger.Print(logger.ERROR, "Error opening file", []string{file.Name}, err)
			continue
		}

		content, err := io.ReadAll(rc)
		if err != nil {
			logger.Print(logger.ERROR, "Error reading file", []string{file.Name}, err)
			rc.Close()
			continue
		}
		rc.Close()

		fileContent := string(content)

		if strings.Contains(fileContent, "Terraform has been successfully initialized!") {
			var name, email string

			if run.Actor != nil {
				name = run.Actor.GetLogin()

				user, _, err := client.Users.Get(ctx, name)
				if err == nil && user != nil {
					if user.GetName() != "" {
						name = user.GetName()
					}
					email = user.GetEmail()
				}
			}

			jobInfo := common.DeplopymentInfo{
				Name:        name,
				Email:       email,
				StartTime:   elastic.DateTime(run.CreatedAt.Time),
				EndTime:     elastic.DateTime(run.UpdatedAt.Time),
				Duration:    float64(run.UpdatedAt.Time.Sub(run.CreatedAt.Time).Seconds()),
				GitClient:   "github",
				JobID:       strconv.FormatInt(*run.ID, 10),
				ProjectID:   strconv.FormatInt(*run.Repository.ID, 10),
				ProjectName: repoName,
			}

			common.ProcessTraceFile(fileContent, tenantID, jobInfo, parentRscNameToID, missingNormalizeRsc)
		}
	}

	return nil
}

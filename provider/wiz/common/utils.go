package wiz

import (
	"errors"
	"strings"

	"github.com/precize/common"
	provCommon "github.com/precize/provider/common"
)

var serviceToPropertyMap = map[string]any{
	"KUBERNETES_CLUSTER": []map[string]any{
		{
			"key":  "additionalDetails",
			"path": "-all-",
		},
	},
	"BUCKET": []map[string]any{
		{
			"key":  "additionalDetails",
			"path": "-all-",
		},
	},
}

// ExtractGCPResourcePath extracts the GCP resource path starting from a marker, eg: 'projects/'
// in the given providerUniqueID. If "projects/" is not present, it returns an error.
//
// Example:
// Input:  "https://container.googleapis.com/v1beta1/projects/regional-research/zones/us-central1-c/clusters/gke-regional-research"
// Output: "projects/regional-research/zones/us-central1-c/clusters/gke-regional-research"
func ExtractGCPResourceFromURL(providerUniqueID, marker string) (string, error) {
	parts := strings.SplitN(providerUniqueID, marker, 2)
	if len(parts) != 2 {
		return "", errors.New(`invalid providerUniqueID: "projects/" not found`)
	}
	return marker + parts[1], nil
}

func FetchResourceIDAndType(resourceProperties map[string]any, accountID, region, rscID, rscType, resourceName, wizType string, unsupportedData *provCommon.UnsupportedData) (resourceID, resourceType string, skipResource bool) {

	resourceID = rscID
	resourceType = rscType

	nativeType := ""

	if natType, ok := resourceProperties["nativeType"].(string); ok {
		nativeType = natType
	}

	switch wizType {
	case ACCESSROLE_WIZ_TYPE:

		switch nativeType {
		case CLUSTERROLE_WIZ_NATIVE_TYPE:
			if kubernetesFlavour, ok := resourceProperties["kubernetes_kubernetesFlavor"].(string); ok {
				switch kubernetesFlavour {
				case "EKS":
					resourceType = common.AWS_EKSCLUSTERROLE_RESOURCE_TYPE
					resourceID = resourceName
				case "AKS":
					// TODO
				case "GKE":
					if clusterName, ok := resourceProperties["kubernetes_clusterName"].(string); ok {
						resourceType = common.GCP_GKECLUSTERROLE_RESOURCE_TYPE
						resourceID = "projects/" + accountID + "/locations/" + region + "/clusters/" + clusterName + "k8s/rbac.authorization.k8s.io/clusterroles/" + resourceName
					}
				}
			}

		case ROLE_WIZ_NATIVE_TYPE:
			if kubernetesFlavour, ok := resourceProperties["kubernetes_kubernetesFlavor"].(string); ok {
				switch kubernetesFlavour {
				case "EKS":
					resourceType = common.AWS_EKSNAMESPACEROLE_RESOURCE_TYPE
					resourceID = resourceName
				case "AKS":
					// TODO
				case "GKE":
					if clusterName, ok := resourceProperties["kubernetes_clusterName"].(string); ok {
						if namespace, ok := resourceProperties["namespace"].(string); ok {
							resourceType = common.GCP_GKENAMESPACEROLE_RESOURCE_TYPE
							resourceID = "projects/" + accountID + "/locations/" + region + "/clusters/" + clusterName + "namespaces/" + namespace + "rbac.authorization.k8s.io/roles/" + resourceName

						}
					}
				}
			}
		default:
			skipResource = true
		}
	case KUBERNETES_WIZ_TYPE:
		if providerUniqueId, ok := resourceProperties["providerUniqueId"].(string); ok {
			resourceType = common.GCP_GKECLUSTER_RESOURCE_TYPE
			if rscID, _ = ExtractGCPResourceFromURL(providerUniqueId, "projects/"); ok {
				resourceID = rscID
			}
		} else if providerUniqueId, ok := resourceProperties["providerId"].(string); ok {
			resourceType = common.GCP_GKECLUSTER_RESOURCE_TYPE
			if rscID, _ = ExtractGCPResourceFromURL(providerUniqueId, "projects/"); ok {
				resourceID = rscID
			}
		}
	case BUCKET_WIZ_TYPE:
		if cloudPlatform, ok := resourceProperties["cloudPlatform"].(string); ok {
			switch cloudPlatform {
			case "AWS":
				if bucketID, ok := resourceProperties["externalId"].(string); ok {
					resourceType = common.AWS_S3_RESOURCE_TYPE
					resourceID = bucketID
				}
			case "GCP":
			case "AZURE":
			}
		}
		resourceType = common.GCP_CLOUDSTORAGE_RESOURCE_TYPE
		resourceID = resourceName

	default:
		if unsupportedData != nil {
			unsupportedData.AssetTypes[wizType] = struct{}{}
		}
		skipResource = true
	}

	resourceID = strings.ToLower(resourceID)

	return
}

func ExtractRgFromEntityID(entityID string) (rg string) {
	rg = entityID
	entIDSplit := strings.Split(entityID, "/providers/")
	if len(entIDSplit) > 1 {
		rg = entIDSplit[0]
	}
	return
}

func MapWizSeverity(wizSeverity string) (severity string) {
	switch wizSeverity {
	case "CRITICAL":
		severity = common.CRITICAL_RISK
	case "HIGH":
		severity = common.HIGH_RISK
	case "MEDIUM":
		severity = common.MEDIUM_RISK
	case "LOW":
		severity = common.LOW_RISK
	case "INFORMATIONAL":
		severity = common.INFORMATIONAL_RISK
	default:
		severity = common.NOTEVALUATED_RISK
	}

	return
}

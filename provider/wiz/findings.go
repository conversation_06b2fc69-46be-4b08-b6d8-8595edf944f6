package wiz

import (
	"bytes"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
	wizCommon "github.com/precize/provider/wiz/common"
	"github.com/precize/provider/wiz/queries"
	"github.com/precize/provider/wiz/types"
	"github.com/precize/transport"
)

const (
	RESOURCE_CONFIG_TYPE     = "rscconfig"
	RESOURCE_CONFIG_CATEGORY = "Resource Configuration"
)

func CollectRscConfigFindings(tenantID, token string, wizEnv tenant.WizEnvironment, startTime, endTime, tenantStartTime time.Time) {

	if endTime.Sub(startTime) < bufferTime {
		return
	}

	defaultTime := tenant.FetchAttributeDefaultTime(tenant.WIZ_RESOURCE, tenantStartTime)

	if (endTime.Sub(startTime)) > endTime.Sub(defaultTime) {
		startTime = defaultTime
	}

	var (
		crsQuery          = `{"_source":["resourceName","entityId"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"terms":{"entityType.keyword":["` + common.GCP_PROJECT_RESOURCE_TYPE + `","` + common.GCP_FOLDER_RESOURCE_TYPE + `","` + common.GCP_ORG_RESOURCE_TYPE + `","` + common.AZURE_SUBSCRIPTION_RESOURCE_TYPE + `","` + common.AWS_ACCOUNT_RESOURCE_TYPE + `","` + common.AWS_ORG_RESOURCE_TYPE + `","` + common.AZURE_RG_RESOURCE_TYPE + `","` + common.AZURE_TENANT_RESOURCE_TYPE + `","` + common.AZURE_MGMTGRP_RESOURCE_TYPE + `","` + common.AWS_ORGUNIT_RESOURCE_TYPE + `"]}}]}}}`
		searchAfter       any
		parentRscNameToID = make(map[string]string)
	)

	logger.Print(logger.INFO, "Starting Wiz cloud configuration findings collection", []string{tenantID}, nil)

	for {
		crsDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCE_STORE_INDEX}, crsQuery, searchAfter)
		if err != nil {
			return
		}

		if len(crsDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		for _, crsDoc := range crsDocs {
			if resourceName, ok := crsDoc["resourceName"].(string); ok {
				if entityID, ok := crsDoc["entityId"].(string); ok {
					if len(resourceName) > 0 && len(entityID) > 0 {
						parentRscNameToID[resourceName] = entityID
					}
				}
			}
		}
	}

	var (
		hasNextPage     bool = true
		cursor          string
		batchSize       int = 1000
		bulkInsertQuery string
		currentCount    int
		maxRecords      = 1000
	)

	headers := map[string]string{
		"accept":        "application/json",
		"authorization": "Bearer " + token,
		"content-Type":  "application/json",
	}

	for hasNextPage {
		variables := map[string]any{
			"first": batchSize,
			"filterBy": map[string]any{
				"updatedAt": map[string]string{
					"before": elastic.DateTime(endTime),
					"after":  elastic.DateTime(startTime),
				},
			},
		}

		if cursor != "" {
			variables["after"] = cursor
		}

		graphqlQuery := types.GraphQLQuery{
			Query:     queries.ConfigurationFindingsQuery,
			Variables: variables,
		}

		payload, err := json.Marshal(graphqlQuery)
		if err != nil {
			logger.Print(logger.ERROR, "Error marshaling GraphQL query", []string{tenantID}, err)
			return
		}

		resp, err := transport.SendRequest("POST", wizEnv.URL, nil, headers, bytes.NewReader(payload))
		if err != nil {
			logger.Print(logger.ERROR, "Error querying Wiz API", []string{tenantID}, err)
			return
		}

		var result types.WizConfigFindingsResponse
		if err = json.Unmarshal(resp, &result); err != nil {
			logger.Print(logger.ERROR, "Error unmarshaling Wiz API response", []string{tenantID}, err)
			return
		}

		processWizConfigFindings(tenantID, result.Data.ConfigurationFindings.Nodes, &bulkInsertQuery, &currentCount, parentRscNameToID)

		hasNextPage = result.Data.ConfigurationFindings.PageInfo.HasNextPage
		cursor = result.Data.ConfigurationFindings.PageInfo.EndCursor

		if currentCount > maxRecords {
			if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_INCIDENTS_INDEX, bulkInsertQuery); err != nil {
				logger.Print(logger.ERROR, "Error in bulk insertion of cloud configurations", []string{tenantID}, err)
				break
			}

			logger.Print(logger.INFO, "Cloud Configuration bulk API Successful for "+strconv.Itoa(currentCount)+" records", []string{tenantID})
			currentCount = 0
			bulkInsertQuery = ""
		}

		time.Sleep(500 * time.Millisecond)
	}

	if currentCount > 0 {
		if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_INCIDENTS_INDEX, bulkInsertQuery); err != nil {
			logger.Print(logger.ERROR, "Error in bulk insertion of cloud configurations", []string{tenantID}, err)
			return
		}

		logger.Print(logger.INFO, "Cloud Configuration bulk API Successful for "+strconv.Itoa(currentCount)+" records", []string{tenantID})
	}

	logger.Print(logger.INFO, "Completed Wiz cloud configuration findings collection", []string{tenantID}, nil)

	return
}

func processWizConfigFindings(tenantID string, findings []types.ConfigurationFinding, bulkInsertQuery *string, currentCount *int, parentRscNameToID map[string]string) error {
	for _, finding := range findings {
		if err := processAndStoreWizConfigFinding(tenantID, finding, bulkInsertQuery, currentCount, parentRscNameToID); err != nil {
			continue
		}
	}
	return nil
}

func processAndStoreWizConfigFinding(tenantID string, finding types.ConfigurationFinding, bulkInsertQuery *string, currentCount *int, parentRscNameToID map[string]string) error {

	serviceID, ok := common.CspStrToIdIntMap[strings.ToLower(finding.Resource.Subscription.CloudProvider)]
	if !ok {
		logger.Print(logger.ERROR, "Unsupported cloud provider", []string{tenantID}, nil)
		return fmt.Errorf("unsupported cloud provider: %s", finding.Resource.Subscription.CloudProvider)
	}

	jsonData, err := json.Marshal(finding)
	if err != nil {
		logger.Print(logger.ERROR, "Error marshalling configuration finding json", []string{tenantID}, err)
		return err
	}

	severity := wizCommon.MapWizSeverity(finding.Severity)

	status := common.INCIDENT_STATUS_OPEN
	if finding.Status == "RESOLVED" {
		status = common.INCIDENT_STATUS_RESOLVED
	}

	resourceID := finding.Resource.ProviderID
	if len(resourceID) <= 0 {
		resourceID = finding.TargetObjectProviderUniqueID
	}
	if len(resourceID) <= 0 {
		resourceID = finding.TargetExternalID
	}

	resourceType := finding.Resource.Type
	if len(resourceType) <= 0 {
		resourceType = finding.Resource.NativeType
	}

	accountID := finding.Resource.Subscription.ExternalID
	if len(accountID) <= 0 {
		accountID = finding.Resource.Subscription.Name
	}

	if accID, ok := parentRscNameToID[accountID]; ok {
		accountID = accID
	}

	issueTitle := finding.Rule.Name
	if len(issueTitle) <= 0 {
		issueTitle = "Configuration Finding"
	}

	var resourceProperty map[string]any

	entitySnapshotBytes, err := json.Marshal(finding.Resource)
	if err != nil {
		logger.Print(logger.ERROR, "Error marshalling entity snapshot", []string{tenantID}, err)
		return err
	}

	if err = json.Unmarshal(entitySnapshotBytes, &resourceProperty); err != nil {
		logger.Print(logger.ERROR, "Error unmarshalling resource property", []string{tenantID}, err)
		return err
	}

	resourceID, resourceType, skipResource := wizCommon.FetchResourceIDAndType(resourceProperty, accountID, finding.Resource.Region, resourceID, resourceType, finding.Resource.Name, finding.Resource.Type, nil)
	if skipResource {
		return nil
	}

	incidentDocID := common.GenerateCombinedHashID(tenantID, finding.ID)

	configDoc := common.Incident{
		ID:            incidentDocID,
		AlertID:       finding.ID,
		Issue:         issueTitle,
		AccountID:     accountID,
		AccountName:   finding.Resource.Subscription.Name,
		EntityID:      resourceID,
		EntityType:    resourceType,
		ResourceName:  finding.Resource.Name,
		Source:        common.WIZ_SOURCE,
		IssueSeverity: severity,
		SourceRisk:    severity,
		CreatedAt:     elastic.DateTime(finding.FirstSeenAt),
		UpdatedAt:     elastic.DateTime(finding.FirstSeenAt),
		ServiceID:     serviceID,
		Category:      RESOURCE_CONFIG_CATEGORY,
		Description:   finding.Rule.Description,
		Status:        status,
		Stage:         "dc",
		TenantID:      tenantID,
		SourceJson:    string(jsonData),
		InsertTime:    elastic.DateTime(time.Now()),
		IsIncident:    false,
	}

	if len(finding.Rule.RemediationInstructions) > 0 {
		additionalData := map[string]interface{}{
			"remediation": finding.Rule.RemediationInstructions,
			"result":      finding.Result,
			"ruleId":      finding.Rule.ID,
			"Type":        RESOURCE_CONFIG_TYPE,
		}

		additionalDataBytes, _ := json.Marshal(additionalData)
		configDoc.AdditionalData = string(additionalDataBytes)
	}

	if doc, _ := elastic.GetDocument(elastic.CLOUD_INCIDENTS_INDEX, incidentDocID); len(doc) > 0 {

		docBytes, err := json.Marshal(doc)
		if err != nil {
			logger.Print(logger.ERROR, "Error marshalling", []string{tenantID}, err)
			return err
		}

		var existingIncident common.Incident
		if err = json.Unmarshal(docBytes, &existingIncident); err != nil {
			logger.Print(logger.ERROR, "Error unmarshalling", []string{tenantID}, err)
			return err
		}

		configDoc.EntityID = existingIncident.EntityID
		configDoc.AccountID = existingIncident.AccountID
		configDoc.CrsID = existingIncident.CrsID
	} else {

		configDoc.CrsID = common.GenerateCombinedHashIDCaseSensitive(tenantID, strconv.Itoa(serviceID), configDoc.AccountID, configDoc.EntityID, configDoc.EntityType)

		crsDoc, err := elastic.GetDocument(elastic.CLOUD_RESOURCE_STORE_INDEX, configDoc.CrsID)
		if err != nil {
			return err
		}

		if len(crsDoc) > 0 {

			b, err := json.Marshal(crsDoc)
			if err != nil {
				logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
				return err
			}

			var crs common.CloudResourceStoreDoc

			if err = json.Unmarshal(b, &crs); err != nil {
				logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err)
				return err
			}

			if len(crs.Environment) > 0 {
				configDoc.Environment = crs.Environment
			}
			if len(crs.Owner) > 0 {
				configDoc.Owner = crs.Owner
			}
			if len(crs.ResourceName) > 0 {
				configDoc.ResourceName = crs.ResourceName
			}
			if len(crs.AccountName) > 0 {
				configDoc.AccountName = crs.AccountName
			}
		}
	}

	docID := tenantID + ":" + finding.ID
	configBytes, _ := json.Marshal(configDoc)
	*bulkInsertQuery = *bulkInsertQuery + `{"index": {"_id": "` + docID + `"}}` + "\n"
	*bulkInsertQuery += string(configBytes) + "\n"

	*currentCount++

	return nil
}

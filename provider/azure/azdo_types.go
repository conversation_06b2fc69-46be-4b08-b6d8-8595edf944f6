package azure

import (
	"github.com/precize/transport"
)

type AzureDevOpsOrganizationsResponse struct {
	transport.ServerResponseInfo
	Data []AzureDevOpsOrganization `json:"data"`
}

type AzureDevOpsOrganization struct {
	AccountID   *string `json:"accountId"`
	AccountName string  `json:"accountName"`
}

type AzureDevOpsProjectsResponse struct {
	transport.ServerResponseInfo
	Data []AzureDevOpsProject `json:"data"`
}

type AzureDevOpsProject struct {
	ID          string  `json:"id"`
	Name        string  `json:"name"`
	Description *string `json:"description"`
	URL         string  `json:"url"`
	State       string  `json:"state"`
}

type AzureDevOpsBuildsResponse struct {
	transport.ServerResponseInfo
	Data []AzureDevOpsBuild `json:"data"`
}

type AzureDevOpsBuild struct {
	ID          int    `json:"id"`
	BuildNumber string `json:"buildNumber"`
	Status      string `json:"status"`
	Result      string `json:"result"`
	QueueTime   string `json:"queueTime"`
	StartTime   string `json:"startTime"`
	FinishTime  string `json:"finishTime"`
}

type AzureDevOpsBuildDetailsResponse struct {
	transport.ServerResponseInfo
	Data AzureDevOpsBuildInfo `json:"data"`
}

type AzureDevOpsBuildInfo struct {
	RequestedFor AzureDevOpsUser `json:"requestedFor"`
	RequestedBy  AzureDevOpsUser `json:"requestedBy"`
}

type AzureDevOpsUser struct {
	DisplayName string `json:"displayName"`
	ID          string `json:"id"`
	UniqueName  string `json:"uniqueName"`
	ImageURL    string `json:"imageUrl,omitempty"`
}

type AzureDevOpsProjectDetails struct {
	ID         string `json:"id"`
	Name       string `json:"name"`
	URL        string `json:"url,omitempty"`
	State      string `json:"state,omitempty"`
	Visibility string `json:"visibility,omitempty"`
}

type AzureDevOpsRepository struct {
	ID   string `json:"id"`
	Type string `json:"type"`
	Name string `json:"name,omitempty"`
}

type AzureDevOpsBuildLogsResponse struct {
	transport.ServerResponseInfo
	Data []AzureDevOpsBuildLog `json:"data"`
}

type AzureDevOpsBuildLog struct {
	ID        int    `json:"id"`
	Type      string `json:"type"`
	LineCount int    `json:"lineCount"`
	URL       string `json:"url"`
}

type BuildProcessorOptions struct {
	ParentRscNameToID   map[string]string
	MissingNormalizeRsc map[string]string
}

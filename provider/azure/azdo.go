package azure

import (
	"strconv"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
)

const (
	azdoBufferTime = 12 * time.Hour
)

func ProcessAZDOData(tenantID, envID string, startTime, endTime, tenantStartTime time.Time) {
	if endTime.Sub(startTime) < azdoBufferTime {
		return
	}

	defaultTime := tenant.FetchAttributeDefaultTime(tenant.AZDO_PIPELINE, tenantStartTime)

	if (endTime.Sub(startTime)) > endTime.Sub(defaultTime) {
		startTime = defaultTime
	}

	logger.Print(logger.INFO, "Fetching AZDO Pipelines from "+common.DateTime(startTime)+" to "+common.DateTime(endTime), []string{tenantID})

	options := &BuildProcessorOptions{
		ParentRscNameToID:   make(map[string]string),
		MissingNormalizeRsc: make(map[string]string),
	}

	if err := fetchResourceMappings(tenantID, options); err != nil {
		logger.Print(logger.ERROR, "Error fetching resource mappings", []string{tenantID}, err)
		return
	}

	processor := NewAzureDevOpsProcessor(tenantID, envID, options)
	globalErr := processor.ProcessPipelineData(startTime, endTime)

	if len(options.MissingNormalizeRsc) > 0 {
		// Convert map to slice for email body
		var missingResources []string
		for resourceName, entityID := range options.MissingNormalizeRsc {
			missingResources = append(missingResources, resourceName+" ("+entityID+")")
		}

		common.SendEmailForResourceNormalizationFromDeploymentLogs(missingResources, tenantID, "azdo")
	}

	if globalErr == nil {
		tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.AZDO_PIPELINE, endTime)
	}
}

func fetchResourceMappings(tenantID string, options *BuildProcessorOptions) error {
	crsQuery := `{"_source":["resourceName", "entityId"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"terms":{"entityType.keyword":["` + common.GCP_PROJECT_RESOURCE_TYPE + `", "` + common.GCP_FOLDER_RESOURCE_TYPE + `", "` + common.GCP_ORG_RESOURCE_TYPE + `"]}},{"term":{"serviceId":` + common.GCP_SERVICE_ID + `}}]}}}`
	var searchAfter any

	for {
		crsDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCE_STORE_INDEX}, crsQuery, searchAfter)
		if err != nil {
			return err
		}

		if len(crsDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		for _, crsDoc := range crsDocs {
			if resourceName, ok := crsDoc["resourceName"].(string); ok {
				if entityID, ok := crsDoc["entityId"].(string); ok {
					if len(resourceName) > 0 && len(entityID) > 0 {
						options.ParentRscNameToID[resourceName] = entityID
					}
				}
			}
		}
	}

	return nil
}

type AzureDevOpsProcessor struct {
	client   *AzureDevOpsClient
	tenantID string
	options  *BuildProcessorOptions
}

func NewAzureDevOpsProcessor(tenantID, envID string, options *BuildProcessorOptions) *AzureDevOpsProcessor {
	return &AzureDevOpsProcessor{
		client:   NewAzureDevOpsClient(tenantID, envID),
		tenantID: tenantID,
		options:  options,
	}
}

func (p *AzureDevOpsProcessor) ProcessPipelineData(startTime, endTime time.Time) error {

	organizations, err := p.client.FetchOrganizations()
	if err != nil {
		logger.Print(logger.ERROR, "Error fetching Azure DevOps organizations", []string{p.tenantID}, err)
		return err
	}

	logger.Print(logger.INFO, "Found "+strconv.Itoa(len(organizations))+" Azure DevOps organizations", []string{p.tenantID})

	for _, org := range organizations {
		if err := p.processOrganization(org, startTime, endTime); err != nil {
			logger.Print(logger.ERROR, "Error processing organization "+org.AccountName, []string{p.tenantID}, err)
			continue
		}
	}

	return nil
}

func (p *AzureDevOpsProcessor) processOrganization(org AzureDevOpsOrganization, startTime, endTime time.Time) error {
	logger.Print(logger.INFO, "Processing organization "+org.AccountName, []string{p.tenantID})

	projects, err := p.client.FetchProjects(org.AccountName)
	if err != nil {
		return err
	}

	logger.Print(logger.INFO, "Found "+strconv.Itoa(len(projects))+" projects in organization "+org.AccountName, []string{p.tenantID})

	for _, project := range projects {
		if err := p.processProject(org.AccountName, project, startTime, endTime); err != nil {
			logger.Print(logger.ERROR, "Error processing project "+project.Name, []string{p.tenantID}, err)
			continue
		}
	}

	return nil
}

func (p *AzureDevOpsProcessor) processProject(orgName string, project AzureDevOpsProject, startTime, endTime time.Time) error {
	logger.Print(logger.INFO, "Processing project "+project.Name, []string{p.tenantID})

	builds, err := p.client.FetchBuilds(orgName, project.Name, startTime, endTime)
	if err != nil {
		return err
	}

	logger.Print(logger.INFO, "Found "+strconv.Itoa(len(builds))+" builds in project "+project.Name, []string{p.tenantID})

	for _, build := range builds {
		if build.Status == "completed" && build.Result == "succeeded" {
			if err := p.processBuild(orgName, project.Name, project.ID, build); err != nil {
				logger.Print(logger.ERROR, "Error processing build "+strconv.Itoa(build.ID), []string{p.tenantID}, err)
				continue
			}
		}
	}

	return nil
}

func (p *AzureDevOpsProcessor) processBuild(orgName, projectName, projectID string, build AzureDevOpsBuild) error {

	buildInfo, err := p.client.FetchBuildDetails(orgName, projectName, build.ID)
	if err != nil {
		return err
	}

	if buildInfo == nil {
		return nil
	}

	buildLogs, err := p.client.FetchBuildLogs(orgName, projectName, build.ID)
	if err != nil {
		return err
	}

	for _, log := range buildLogs {
		if err := p.processLogFile(orgName, projectName, projectID, build.ID, log.ID, build, buildInfo); err != nil {
			logger.Print(logger.ERROR, "Error processing log "+strconv.Itoa(log.ID), []string{p.tenantID}, err)
			continue
		}
	}

	return nil
}

func (p *AzureDevOpsProcessor) processLogFile(orgName, projectName, projectID string, buildID, logID int, buildDetails AzureDevOpsBuild, buildInfo *AzureDevOpsBuildInfo) error {
	logContent, err := p.client.FetchBuildLogContent(orgName, projectName, buildID, logID)
	if err != nil {
		return err
	}

	lowerContent := strings.ToLower(logContent)
	if !strings.Contains(lowerContent, "terraform") && !strings.Contains(lowerContent, "terragrunt") {
		return nil
	}

	if !strings.Contains(lowerContent, "terraform has been successfully initialized!") {
		return nil
	}

	var startTime, endTime time.Time
	if buildDetails.StartTime != "" {
		startTime, _ = time.Parse(time.RFC3339, buildDetails.StartTime)
	}
	if buildDetails.FinishTime != "" {
		endTime, _ = time.Parse(time.RFC3339, buildDetails.FinishTime)
	}

	jobInfo := common.DeplopymentInfo{
		Name:        buildInfo.RequestedFor.DisplayName,
		Email:       buildInfo.RequestedFor.UniqueName,
		StartTime:   elastic.DateTime(startTime),
		EndTime:     elastic.DateTime(endTime),
		Duration:    float64(endTime.Sub(startTime).Seconds()),
		GitClient:   "azdo",
		JobID:       strconv.Itoa(buildDetails.ID),
		ProjectID:   projectID,
		ProjectName: projectName,
	}

	common.ProcessTraceFile(logContent, p.tenantID, jobInfo, p.options.ParentRscNameToID, p.options.MissingNormalizeRsc)

	return nil
}

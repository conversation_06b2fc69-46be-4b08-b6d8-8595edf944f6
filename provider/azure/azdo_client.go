package azure

import (
	"encoding/json"
	"net/url"
	"strconv"
	"time"

	"github.com/precize/transport"
)

type AzureDevOpsClient struct {
	tenantID string
	envID    string
}

func NewAzureDevOpsClient(tenantID, envID string) *AzureDevOpsClient {
	return &AzureDevOpsClient{
		tenantID: tenantID,
		envID:    envID,
	}
}

func (c *AzureDevOpsClient) FetchOrganizations() ([]AzureDevOpsOrganization, error) {
	url := "/precize/private/azure/environments/" + c.envID + "/devops/organizations"

	respBytes, err := transport.SendRequestToServer("GET", url, nil, nil)
	if err != nil {
		return nil, err
	}

	var response AzureDevOpsOrganizationsResponse
	if err := json.Unmarshal(respBytes, &response); err != nil {
		return nil, err
	}

	if response.Status != 200 {
		return nil, err
	}

	return response.Data, nil
}

func (c *AzureDevOpsClient) FetchProjects(orgName string) ([]AzureDevOpsProject, error) {
	url := "/precize/private/azure/environments/" + c.envID + "/devops/organizations/" + url.PathEscape(orgName) + "/projects"

	respBytes, err := transport.SendRequestToServer("GET", url, nil, nil)
	if err != nil {
		return nil, err
	}

	var response AzureDevOpsProjectsResponse
	if err := json.Unmarshal(respBytes, &response); err != nil {
		return nil, err
	}

	if response.Status != 200 {
		return nil, err
	}

	return response.Data, nil
}

func (c *AzureDevOpsClient) FetchBuilds(orgName, projectName string, startTime, endTime time.Time) ([]AzureDevOpsBuild, error) {
	url := "/precize/private/azure/environments/" + c.envID + "/devops/organizations/" + url.PathEscape(orgName) + "/projects/" + url.PathEscape(projectName) + "/builds"

	urlParams := map[string]string{
		"minTime":      startTime.Format(time.RFC3339),
		"maxTime":      endTime.Format(time.RFC3339),
		"resultFilter": "succeeded",
		"statusFilter": "completed",
	}

	respBytes, err := transport.SendRequestToServer("GET", url, urlParams, nil)
	if err != nil {
		return nil, err
	}

	var response AzureDevOpsBuildsResponse
	if err := json.Unmarshal(respBytes, &response); err != nil {
		return nil, err
	}

	if response.Status != 200 {
		return nil, err
	}

	var filteredBuilds []AzureDevOpsBuild
	for _, build := range response.Data {
		filteredBuilds = append(filteredBuilds, build)
	}

	return filteredBuilds, nil
}

func (c *AzureDevOpsClient) FetchBuildDetails(orgName, projectName string, buildID int) (*AzureDevOpsBuildInfo, error) {
	url := "/precize/private/azure/environments/" + c.envID + "/devops/organizations/" + url.PathEscape(orgName) + "/projects/" + url.PathEscape(projectName) + "/builds/" + strconv.Itoa(buildID) + "/info"

	respBytes, err := transport.SendRequestToServer("GET", url, nil, nil)
	if err != nil {
		return nil, err
	}

	var response AzureDevOpsBuildDetailsResponse
	if err := json.Unmarshal(respBytes, &response); err != nil {
		return nil, err
	}

	if response.Status != 200 {
		return nil, err
	}

	return &response.Data, nil
}

func (c *AzureDevOpsClient) FetchBuildLogs(orgName, projectName string, buildID int) ([]AzureDevOpsBuildLog, error) {
	url := "/precize/private/azure/environments/" + c.envID + "/devops/organizations/" + url.PathEscape(orgName) + "/projects/" + url.PathEscape(projectName) + "/builds/" + strconv.Itoa(buildID) + "/logs"

	respBytes, err := transport.SendRequestToServer("GET", url, nil, nil)
	if err != nil {
		return nil, err
	}

	var response AzureDevOpsBuildLogsResponse
	if err := json.Unmarshal(respBytes, &response); err != nil {
		return nil, err
	}

	if response.Status != 200 {
		return nil, err
	}

	return response.Data, nil
}

func (c *AzureDevOpsClient) FetchBuildLogContent(orgName, projectName string, buildID, logID int) (string, error) {
	url := "/precize/private/azure/environments/" + c.envID + "/devops/organizations/" + url.PathEscape(orgName) + "/projects/" + url.PathEscape(projectName) + "/builds/" + strconv.Itoa(buildID) + "/logs/" + strconv.Itoa(logID) + "/content"

	respBytes, err := transport.SendRequestToServer("GET", url, nil, nil)
	if err != nil {
		return "", err
	}

	return string(respBytes), nil
}

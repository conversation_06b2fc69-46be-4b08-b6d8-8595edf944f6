package gitlab

import (
	"strconv"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
	"github.com/xanzy/go-gitlab"
)

const (
	bufferTime = 12 * time.Hour
)

func ProcessGitlabPipelines(tenantID string, gitlabEnv tenant.GitlabEnvironment, pipelineStartTime, pipelineEndTime, tenantStartTime time.Time) {

	if pipelineEndTime.Sub(pipelineStartTime) < bufferTime {
		return
	}

	defaultTime := tenant.FetchAttributeDefaultTime(tenant.GITLAB_PIPELINE, tenantStartTime)

	if (pipelineEndTime.Sub(pipelineStartTime)) > pipelineEndTime.Sub(defaultTime) {
		pipelineStartTime = defaultTime
	}

	accessToken, err := getGitlabAccessToken(tenantID, gitlabEnv.Token)
	if err != nil {
		return
	}

	gitlabClient, err := gitlab.NewOAuthClient(accessToken)
	if err != nil {
		logger.Print(logger.ERROR, "Got error creating gitlab client", []string{tenantID}, err)
		return
	}

	projectsPage := 1
	var globalErr error

	var (
		crsQuery            = `{"_source":["resourceName", "entityId"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"terms":{"entityType.keyword":["` + common.GCP_PROJECT_RESOURCE_TYPE + `", "` + common.GCP_FOLDER_RESOURCE_TYPE + `", "` + common.GCP_ORG_RESOURCE_TYPE + `"]}},{"term":{"serviceId":` + common.GCP_SERVICE_ID + `}}]}}}`
		searchAfter         any
		parentRscNameToID   = make(map[string]string)
		missingNormalizeRsc = make(map[string]string)
	)

	for {
		crsDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCE_STORE_INDEX}, crsQuery, searchAfter)
		if err != nil {
			return
		}

		if len(crsDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		for _, crsDoc := range crsDocs {
			if resourceName, ok := crsDoc["resourceName"].(string); ok {
				if entityID, ok := crsDoc["entityId"].(string); ok {
					if len(resourceName) > 0 && len(entityID) > 0 {
						parentRscNameToID[resourceName] = entityID
					}
				}
			}
		}
	}

	for {

		projectMap := make(map[string]struct{})

		for _, r := range gitlabEnv.Projects {
			projectMap[r] = struct{}{}
		}

		projects, projectsResp, err := gitlabClient.Projects.ListProjects(&gitlab.ListProjectsOptions{
			Membership: gitlab.Bool(true),
			ListOptions: gitlab.ListOptions{
				Page:    projectsPage,
				PerPage: 90,
			},
		})
		if err != nil {
			if strings.Contains(err.Error(), "401") {
				gitlabClient = refreshGitlabToken(tenantID, &accessToken, gitlabClient)
				projects, projectsResp, err = gitlabClient.Projects.ListProjects(&gitlab.ListProjectsOptions{
					Membership: gitlab.Bool(true),
					ListOptions: gitlab.ListOptions{
						Page:    projectsPage,
						PerPage: 90,
					},
				})
				if err != nil {
					globalErr = err
					logger.Print(logger.ERROR, "Got error getting projects", []string{tenantID}, err)
					break
				}
			} else {
				logger.Print(logger.ERROR, "Got error getting projects", []string{tenantID}, err)
				break
			}
		}

		for _, project := range projects {

			if _, ok := projectMap[project.Name]; !ok {
				if _, ok := projectMap[project.PathWithNamespace]; !ok {
					continue
				}
			}

			logger.Print(logger.INFO, "Processing project "+project.Name, []string{tenantID})

			jobsPage := 1

			for {

				jobs, jobsResp, err := gitlabClient.Jobs.ListProjectJobs(project.ID, &gitlab.ListJobsOptions{
					ListOptions: gitlab.ListOptions{
						Page:    jobsPage,
						PerPage: 90,
					},
					Scope: &[]gitlab.BuildStateValue{"success"},
				})

				if err != nil {
					if strings.Contains(err.Error(), "401") {
						gitlabClient = refreshGitlabToken(tenantID, &accessToken, gitlabClient)
						jobs, jobsResp, err = gitlabClient.Jobs.ListProjectJobs(project.ID, &gitlab.ListJobsOptions{
							ListOptions: gitlab.ListOptions{
								Page:    jobsPage,
								PerPage: 90,
							},
							Scope: &[]gitlab.BuildStateValue{"success"},
						})
						if err != nil {
							globalErr = err
							logger.Print(logger.ERROR, "Got error listing jobs", []string{tenantID, strconv.Itoa(project.ID)}, err)
							break
						}
					}
				}

				for _, job := range jobs {
					if job.CreatedAt.Before(pipelineStartTime) {
						jobsResp.NextPage = 0
						break
					} else if job.CreatedAt.After(pipelineEndTime) {
						continue
					}

					processGitlabJob(gitlabClient, job, project, tenantID, accessToken, &globalErr, parentRscNameToID, missingNormalizeRsc)
				}

				if jobsResp == nil || jobsResp.NextPage == 0 {
					break
				}

				jobsPage = jobsResp.NextPage

				// sleeping to avoid rate limits
				time.Sleep(1 * time.Second)
			}
		}

		if projectsResp == nil || projectsResp.NextPage == 0 {
			break
		}

		projectsPage = projectsResp.NextPage
	}

	if len(missingNormalizeRsc) > 0 {
		// Convert map to slice for email body
		var missingResources []string
		for resourceName, entityID := range missingNormalizeRsc {
			missingResources = append(missingResources, resourceName+" ("+entityID+")")
		}

		common.SendEmailForResourceNormalizationFromDeploymentLogs(missingResources, tenantID, "gitlab")
	}

	if globalErr == nil {
		tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.GITLAB_PIPELINE, pipelineEndTime)
	}

}

package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/config"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/transport"
)

const (
	serverQAToken   = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIlQzMlOTElQzIlODglMkYlQzMlOTElQzIlOUQlMkIlQzIlOENrJUMzJTk4JTIxJTE0JUMyJUI3WCVDMyU4MyVDMiVCQSVDMiVCRiVDMyU5QSVDMyVCMiVDMiVBNSU1QyVDMiVBMiU0MCUyQyUyM3glQzMlOEMlMEElMjklQzMlOTglQzMlOEYlQzIlOEElQzIlOEIlQzMlOUYlQzMlQUElQzIlOTQlQzIlOUUlQzIlODQlQzIlQjYlMTgiLCJhdXRoIjoiU3lzdGVtIEFkbWluLFJPTEVfQU5PTllNT1VTIiwiZXhwIjoxNzc2NzYzNDQyfQ.YS0YJHA3FY11DFxUTckxvYFrIJmycf_fCjFMM12bSy5OOI1lgi3Tvbp7vgVbSCRhXqpvuxyua_c9d1pBHVRuTQ"
	serverProdToken = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIlQzMlOTElQzIlODglMkYlQzMlOTElQzIlOUQlMkIlQzIlOENrJUMzJTk4JTIxJTE0JUMyJUI3WCVDMyU4MyVDMiVCQSVDMiVCRiVDMyU5QSVDMyVCMiVDMiVBNSU1QyVDMiVBMiU0MCUyQyUyM3glQzMlOEMlMEElMjklQzMlOTglQzMlOEYlQzIlOEElQzIlOEIlQzMlOUYlQzMlQUElQzIlOTQlQzIlOUUlQzIlODQlQzIlQjYlMTgiLCJhdXRoIjoiU3lzdGVtIEFkbWluLFJPTEVfQU5PTllNT1VTIiwiZXhwIjoxNzc2NzYzNzM3fQ.uJWvZS9_hm4Ds8GsGJ4jiNdB_3XvU65JjHaF4YZzr5kZrLzHW476WYik3fTBwZEhARH5Fbse1iKKHQhVs7sKkg"
)

var baseURL = "https://qa.precize.ai"

var serviceIDToName = map[int]string{
	1000: "aws",
	2000: "azure",
	3000: "gcp",
}

type AggregationResult struct {
	ByTenant struct {
		Buckets []struct {
			Key       string `json:"key"` // tenantId
			ByService struct {
				Buckets []struct {
					Key               int `json:"key"` // serviceId
					LatestCollectedAt struct {
						Hits struct {
							Hits []struct {
								Source struct {
									CollectedAt int64 `json:"collectedAt"`
								} `json:"_source"`
							} `json:"hits"`
						} `json:"hits"`
					} `json:"latest_collectedAt"`
				} `json:"buckets"`
			} `json:"by_service"`
		} `json:"buckets"`
	} `json:"by_tenant"`
}

// Date - 02/07/2025
// Target - SCANS
// This migration is to trigger post scan APIs for all tenants and services

func main() {

	var (
		appConfigPath   = flag.String("config", "application.yml", "Path to application.yml")
		specTenantID    = flag.String("tenant", "", "TenantId(s) to run migration for (comma-separated for multiple tenants)")
		specServiceName = flag.String("category", "", "Specific serviceID to run migration for") // for eg: aws, azure, gcp
		env             = flag.String("env", "qa", "Environment to run migration for")           // for eg: qa, preprod, prod
	)

	flag.Parse()

	logger.InitializeLogs("migration", false)

	defaultConf, err := config.InitializeApplicationConfig(*appConfigPath)
	if err != nil {
		return
	} else if defaultConf {
		logger.Print(logger.INFO, "Application config could not be read. Starting with defaults", "application.yml")
	}

	if err := elastic.ConnectToElasticSearch(); err != nil {
		return
	}

	transport.SetHttpClient()
	common.InitializeOpenAI()

	query :=
		`
	{
		"size": 0,
		"query": {
			"bool": {
			"must": [
				{
					"match": {
						"status": "2"
					}
				},
				{
					"match": {
						"scanType": "0"
					}
				}
		  	]
			}
		},
		"aggs": {
			"by_tenant": {
			"terms": {
				"field": "tenantId.keyword",
				"size": 1000
			},
			"aggs": {
				"by_service": {
				"terms": {
					"field": "serviceId",
					"size": 1000
				},
				"aggs": {
					"latest_collectedAt": {
					"top_hits": {
						"sort": [
						{
							"collectedAt": {
							"order": "desc"
							}
						}
						],
						"size": 1,
						"_source": {
						"includes": [
							"collectedAt"
						]
						}
					}
					}
				}
				}
			}
			}
		}
	}
	`
	// Call your custom wrapper to fetch aggregation response
	eventsAggregation, err := elastic.ExecuteSearchForAggregation([]string{"scans"}, query)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to execute search:", err)
		return
	}

	eventsAggBytes, err := json.Marshal(eventsAggregation)
	if err != nil {
		logger.Print(logger.ERROR, "Got error marshalling", err)
		return
	}

	var result AggregationResult
	if err := json.Unmarshal(eventsAggBytes, &result); err != nil {
		logger.Print(logger.ERROR, "Failed to parse aggregation response:", err)
		return
	}

	for _, tenantBucket := range result.ByTenant.Buckets {
		tenantId := tenantBucket.Key

		if len(*specTenantID) > 0 {

			tenantIDs := strings.Split(*specTenantID, ",")
			found := false
			for _, t := range tenantIDs {
				if tenantId == strings.TrimSpace(t) {
					found = true
					break
				}
			}
			if !found {
				continue
			}
		}

		for _, serviceBucket := range tenantBucket.ByService.Buckets {
			serviceId := serviceBucket.Key
			serviceName, ok := serviceIDToName[serviceId]
			if !ok {
				logger.Print(logger.INFO, "Unknown service ID:", serviceId)
				continue
			}

			if len(*specServiceName) > 0 {
				if serviceName != *specServiceName {
					continue
				}
			}

			if len(serviceBucket.LatestCollectedAt.Hits.Hits) == 0 {
				logger.Print(logger.INFO, "No collectedAt for tenant", tenantId, "service", serviceId)
				continue
			}

			collectedAt := serviceBucket.LatestCollectedAt.Hits.Hits[0].Source.CollectedAt

			err := callPostScanAPI(serviceName, tenantId, collectedAt, *env)
			if err != nil {
				logger.Print(logger.ERROR, "Error calling API for tenant", tenantId, "service", serviceId, err)
			}
		}
	}

	os.Exit(0)
}

func callPostScanAPI(serviceId string, tenantId string, collectedAt int64, env string) error {

	authToken := serverQAToken
	if env == "prod" {
		baseURL = "https://web.precize.ai"
		authToken = serverProdToken
	} else if env == "preprod" {
		baseURL = "https://preprod.precize.ai"
	}

	apiURL := baseURL + "/precize/resources/run/evaluatePostScanHeroStats"
	url := fmt.Sprintf(
		"%s/%s/%s/%d?keywordSearch=true&campaigns=true&markDeleted=true&markAi=true&enhancer=true&incidentWorkflow=true&saveStatsStore=true&evaluatePostScanHeroStats=true&identityExtraction=true&extractAIResources=true&evaluateVirtualResources=true&prioritiser=true&enrichCloudResources=true&identityHeroStats=true",
		apiURL, serviceId, tenantId, collectedAt,
	)

	req, err := http.NewRequest("POST", url, nil)
	if err != nil {
		return err
	}

	req.Header.Set("Authorization", "Bearer "+authToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	io.Copy(io.Discard, resp.Body)

	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusAccepted {
		logger.Print(logger.ERROR, "API call failed with status", resp.Status)
		return fmt.Errorf("API call failed with status: %s", resp.Status)
	}

	logger.Print(logger.INFO, "API call succeeded", []string{tenantId}, serviceId, collectedAt)
	return nil
}

package main

import (
	"flag"
	"os"
	"os/signal"
	"syscall"

	"github.com/precize/common"
	"github.com/precize/config"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/transport"
)

func main() {

	var (
		appConfigPath = flag.String("config", "application.yml", "Path to application.yml")
		tenantID      = flag.String("tenant", "", "TenantId(s) to run migration for (comma-separated for multiple tenants)")
	)

	flag.Parse()

	logger.InitializeLogs("migration", false)

	if len(*tenantID) <= 0 {
		logger.Print(logger.ERROR, "TenantId not specified")
		os.Exit(1)
	}

	defaultConf, err := config.InitializeApplicationConfig(*appConfigPath)
	if err != nil {
		return
	} else if defaultConf {
		logger.Print(logger.INFO, "Application config could not be read. Starting with defaults", "application.yml")
	}

	if err := elastic.ConnectToElasticSearch(); err != nil {
		return
	}

	transport.SetHttpClient()
	common.InitializeOpenAI()
	gracefullyShutDown()

	GetInactiveOktaLastRead(*tenantID)

	os.Exit(0)

}

func gracefullyShutDown() {

	sigs := make(chan os.Signal, 1)

	signal.Notify(sigs, syscall.SIGINT, syscall.SIGTERM)

	go func() {

		sig := <-sigs
		logger.Print(logger.INFO, "Signal received", sig)
		os.Exit(1)
	}()
}

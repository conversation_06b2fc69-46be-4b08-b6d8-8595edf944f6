package main

import (
	"fmt"
	"strings"

	"github.com/precize/elastic"
)

func GetInactiveOktaLastRead(tenantID string) {

	ciQuery := `{"query":{"bool":{"must":[{"wildcard":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"hsDbKeys.keyword":"inactiveOktaUser"}}]}}}`
	ciDocs, err := elastic.ExecuteSearchQuery([]string{elastic.CLOUD_IDENTITY_INDEX}, ciQuery)
	if err != nil {
		return
	}

	identityIDs := []string{}

	for _, ciDoc := range ciDocs {
		if identityID, ok := ciDoc["entityId"].(string); ok {
			identityIDs = append(identityIDs, identityID)
		}
	}

	identityQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"type.keyword":"OKTA_USER"}},{"terms":{"identityId.keyword":["` + strings.Join(identityIDs, `","`) + `"]}}]}},"sort":[{"identityId.keyword":{"order":"asc"}}]}`
	identityDocs, err := elastic.ExecuteSearchQuery([]string{elastic.IDENTITIES_INDEX}, identityQuery)
	if err != nil {
		return
	}

	type IdentityData struct {
		Email        string
		LastReadTime string
	}

	defaultDateTime := "Never"

	for _, identityDoc := range identityDocs {
		email, _ := identityDoc["identityId"].(string)

		if email == "" {
			if emailInterface, ok := identityDoc["email"]; ok {
				email, _ = emailInterface.(string)
			}
		}

		if email == "" {
			continue
		}

		lastReadTimeStr := defaultDateTime
		if lastReadTime, ok := identityDoc["lastReadTime"].(float64); ok {
			if lastReadTime > 0 {
				lastReadTimeStr = elastic.ConvertEpochToDateTime(int64(lastReadTime))
			}
		}

		fmt.Printf("%s : %s\n", email, lastReadTimeStr)
	}
}

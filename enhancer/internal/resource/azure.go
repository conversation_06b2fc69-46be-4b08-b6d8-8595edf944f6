package resourceutils

import (
	"github.com/precize/common"
	"github.com/precize/enhancer/rcontext"
)

func GetAzureAppName(resourceContext *rcontext.ResourceContext, applicationID, azureTenantID string) (applicationName string) {

	applicationName = applicationID

	if len(azureTenantID) > 0 {
		applicationContextID := common.GenerateCombinedHashID(applicationID, common.AZURE_GRAPHAPP_RESOURCE_TYPE, azureTenantID, resourceContext.LastCollectedAt, resourceContext.TenantID)

		if applicationDoc, ok := resourceContext.GetResourceContextInsertDoc(applicationContextID); ok {
			if len(applicationDoc.ResourceName) > 0 {
				applicationName = applicationDoc.ResourceName
				return
			}
		}
	} else {
		for _, azTenant := range resourceContext.AzureTenantIDs {
			applicationContextID := common.GenerateCombinedHashID(applicationID, common.AZURE_GRAPHAPP_RESOURCE_TYPE, azTenant, resourceContext.LastCollectedAt, resourceContext.TenantID)

			if applicationDoc, ok := resourceContext.GetResourceContextInsertDoc(applicationContextID); ok {
				if len(applicationDoc.ResourceName) > 0 {
					applicationName = applicationDoc.ResourceName
					return
				}
			}
		}
	}

	if commonApp, ok := common.COMMON_AZURE_APPS[applicationID]; ok {
		applicationName = commonApp
		return
	}

	if appName, ok := resourceContext.GetAzureManagedRegisteredApps(applicationID); ok {
		// Some ownerships are giving registered app id and not enterprise app id of azure managed apps
		applicationName = appName
	}

	return
}

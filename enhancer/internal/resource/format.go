package resourceutils

import (
	"strings"
	"unicode"

	"github.com/precize/common"
)

// Slice of ResourceContextItem is updated in place to remove duplicates for resource_context index
// Optionally, uniqueContext map contains the unique values required for cloud_resources index
func GetUniqueContext(contexts *[]common.ResourceContextItem, uniqueContext map[string]struct{}) {

	uniqueNameAndDesc := make(map[string]struct{})

	i := 0
	for _, context := range *contexts {
		if len(context.Name) > 0 {
			if _, ok := uniqueNameAndDesc[context.Name+context.Desc]; !ok {
				uniqueNameAndDesc[context.Name+context.Desc] = struct{}{}
				(*contexts)[i] = context
				i++
			}

			if uniqueContext != nil {
				if _, ok := uniqueContext[context.Name]; !ok {
					uniqueContext[context.Name] = struct{}{}
				}
			}
		}
	}

	*contexts = (*contexts)[:i]
}

func FormatContextValue(str string) (formattedStr string) {

	formattedStr = str

	if _, err := common.ParseAddress(str); err == nil {
		return
	}

	if len(str) == 2 {
		formattedStr = strings.ToUpper(str)
		return
	}

	if len(str) > 0 && unicode.IsLower([]rune(str)[0]) {
		formattedStr = common.ConvertToTitleCase(str)
		return
	}

	return
}

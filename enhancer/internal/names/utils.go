package nameutils

import (
	"strings"
	"unicode"
)

var Prefixes = []string{
	"dr", "mr", "mrs", "ms", "miss", "prof",
	"rev", "fr", "sr",
	"capt", "major", "col", "colonel", "general",
	"lt", "sgt",
}

var Suffixes = []string{
	"jr", "sr", "ii", "iii", "iv", "v", "vi", "vii", "viii", "ix", "x",
	"phd", "md",
}

func cleanNamePart(s string) string {
	var result strings.Builder
	for _, r := range s {
		if unicode.IsLetter(r) {
			result.WriteRune(unicode.ToLower(r))
		}
	}

	s = strings.TrimSpace(result.String())

	for _, prefix := range Prefixes {
		if strings.HasPrefix(s, prefix+" ") {
			s = strings.TrimSpace(s[len(prefix):])
			break
		}
		if s == prefix {
			return ""
		}
	}

	for _, suffix := range Suffixes {
		if strings.HasSuffix(s, " "+suffix) {
			s = strings.TrimSpace(s[:len(s)-len(suffix)])
			break
		}
		if s == suffix {
			return ""
		}
	}

	return s
}

func hasNameParts(name1, name2 string) bool {
	if strings.HasPrefix(name1, name2) || strings.HasPrefix(name2, name1) {
		return true
	}

	return false
}

func AreNamesComparable(name1, name2 string) bool {
	parts1 := strings.Fields(name1)
	parts2 := strings.Fields(name2)

	var validParts1, validParts2 []string
	for _, part := range parts1 {
		if cleanNamePart(part) != "" {
			validParts1 = append(validParts1, part)
		}
	}
	for _, part := range parts2 {
		if cleanNamePart(part) != "" {
			validParts2 = append(validParts2, part)
		}
	}

	if len(validParts1) == 0 || len(validParts2) == 0 {
		return false
	}

	var shorterParts, longerParts []string
	if len(validParts1) <= len(validParts2) {
		shorterParts = validParts1
		longerParts = validParts2
	} else {
		shorterParts = validParts2
		longerParts = validParts1
	}

	for _, shortPart := range shorterParts {
		found := false
		for _, longPart := range longerParts {
			if hasNameParts(shortPart, longPart) {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	return true
}

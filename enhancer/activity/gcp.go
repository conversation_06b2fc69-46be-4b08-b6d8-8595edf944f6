package activity

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/precize/common"
	"github.com/precize/common/context"
	"github.com/precize/enhancer/context"
	"github.com/precize/enhancer/internal/email"
	"github.com/precize/enhancer/internal/resource"
	"github.com/precize/enhancer/rcontext"
	"github.com/precize/logger"
)

type GCPLog struct {
	ProtoPayload ProtoPayload `json:"protoPayload"`
}

type ProtoPayload struct {
	Type               string             `json:"@type"`
	AuthenticationInfo AuthenticationInfo `json:"authenticationInfo"`
}

type AuthenticationInfo struct {
	PrincipalEmail               string                     `json:"principalEmail"`
	ServiceAccountKeyName        string                     `json:"serviceAccountKeyName"`
	ServiceAccountDelegationInfo []ServiceAccountDelegation `json:"serviceAccountDelegationInfo"`
	ServiceDelegationHistory     ServiceDelegationHistory   `json:"serviceDelegationHistory"`
}

type ServiceAccountDelegation struct {
	FirstPartyPrincipal FirstPartyPrincipal `json:"firstPartyPrincipal"`
}

type FirstPartyPrincipal struct {
	PrincipalEmail string `json:"principalEmail"`
}

type ServiceDelegationHistory struct {
	OriginalPrincipal string `json:"originalPrincipal"`
}

func ParseGCPActivity(resourceContext *rcontext.ResourceContext, eventInfo EventInfo,
	activityUsers map[string]common.ResourceContextItem, resourceContextInsertDoc common.ResourceContextInsertDoc) {

	var gcpLog GCPLog

	if err := json.Unmarshal([]byte(eventInfo.EventJSONString), &gcpLog); err != nil {
		logger.Print(logger.ERROR, "Got error unmarshalling", []string{resourceContext.TenantID}, err)
		return
	}

	var serviceAccount = eventInfo.Username

	_, serviceAccountDesc := resourceutils.ParseServiceAccountData(serviceAccount, eventInfo.Account, resourceContext)

	uniqueIdentities := make(map[string]struct{})

	// Using serviceAccountKey
	if serviceAccountKey := gcpLog.ProtoPayload.AuthenticationInfo.ServiceAccountKeyName; len(serviceAccountKey) > 0 {
		serviceAccountKey = strings.TrimPrefix(strings.ToLower(serviceAccountKey), "//iam.googleapis.com/")
		getServiceAccountKeyOwners(resourceContext, serviceAccountKey, eventInfo, activityUsers, resourceContextInsertDoc)
	}

	// Impersonating the service account (an identity with serviceAccountTokenCreator permission)
	serviceAccountDelegationInfo := gcpLog.ProtoPayload.AuthenticationInfo.ServiceAccountDelegationInfo
	for i, delegation := range serviceAccountDelegationInfo {
		if principalEmail := delegation.FirstPartyPrincipal.PrincipalEmail; len(principalEmail) > 0 {
			if common.IsServiceAccountEmail(principalEmail) || strings.Contains(principalEmail, "serviceAccount:") {
				emailSplit := strings.Split(principalEmail, "serviceAccount:")
				if len(emailSplit) > 1 {
					principalEmail = emailSplit[1]
					if !common.IsServiceAccountEmail(principalEmail) {
						continue
					}
				}

				GetServiceAccountOwnersForActivity(resourceContext, principalEmail, eventInfo, activityUsers, uniqueIdentities, "User owned service account "+principalEmail+" has performed activities on the resource", "b", resourceContextInsertDoc)

				serviceAccountName := resourceutils.GetGCPServiceAccountName(resourceContext, principalEmail, eventInfo.Account)
				activityUsers[eventInfo.EventTime+"b"+fmt.Sprintf("%03d", len(serviceAccountDelegationInfo)-i)] = context.GetUserContextItem(resourceContext, serviceAccountName+contextutils.SERVICEACCOUNT_USER_SUFFIX, common.ACTIVITY_USER_TYPE, "Service account has performed activities on the resource by impersonating another Service Account "+serviceAccount, principalEmail, resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
					Name:   eventInfo.Event,
					Region: eventInfo.Region,
					Time:   eventInfo.EventTime,
				})
			} else {
				emailSplit := strings.Split(principalEmail, "user:")
				if len(emailSplit) > 1 {
					emailSplit = strings.Split(emailSplit[1], "?uid")
					principalEmail = emailSplit[0]
				}

				activityUsers[eventInfo.EventTime+"b"+fmt.Sprintf("%03d", len(serviceAccountDelegationInfo)-i)] = context.GetUserContextItem(resourceContext, principalEmail, common.ACTIVITY_USER_TYPE, "User has performed activities on the resource by impersonating the Service Account "+serviceAccount, "", resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
					Name:   eventInfo.Event,
					Region: eventInfo.Region,
					Time:   eventInfo.EventTime,
				})
			}
		}
	}

	// Service agent acting on behalf of another identity
	if originalPrincipal := gcpLog.ProtoPayload.AuthenticationInfo.ServiceDelegationHistory.OriginalPrincipal; len(originalPrincipal) > 0 {
		if strings.Contains(originalPrincipal, "serviceAccount:") {
			emailSplit := strings.Split(originalPrincipal, "serviceAccount:")
			if len(emailSplit) > 1 {
				email := emailSplit[1]
				if common.IsServiceAccountEmail(email) {
					GetServiceAccountOwnersForActivity(resourceContext, email, eventInfo, activityUsers, uniqueIdentities, "User owned service account "+email+" has performed activities on the resource", "b", resourceContextInsertDoc)

					serviceAccountName := resourceutils.GetGCPServiceAccountName(resourceContext, email, eventInfo.Account)
					activityUsers[eventInfo.EventTime+"b"] = context.GetUserContextItem(resourceContext, serviceAccountName+contextutils.SERVICEACCOUNT_USER_SUFFIX, common.ACTIVITY_USER_TYPE, "Service account has performed activities on the resource by delegating tasks to another Service Account "+serviceAccount, email, resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
						Name:   eventInfo.Event,
						Region: eventInfo.Region,
						Time:   eventInfo.EventTime,
					})
				}
			}
		} else if strings.Contains(originalPrincipal, "user:") {
			emailSplit := strings.Split(originalPrincipal, "user:")
			if len(emailSplit) > 1 {
				emailSplit = strings.Split(emailSplit[1], "?uid")
				email := emailSplit[0]
				activityUsers[eventInfo.EventTime+"b"] = context.GetUserContextItem(resourceContext, email, common.ACTIVITY_USER_TYPE,
					"User has performed activities on the resource by delegating tasks to the Service Account "+serviceAccount, "", resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
						Name:   eventInfo.Event,
						Region: eventInfo.Region,
						Time:   eventInfo.EventTime,
					})
			}
		}
	}

	// Add Service Account owner as an owner of this resource via Activity
	GetServiceAccountOwnersForActivity(resourceContext, serviceAccount, eventInfo, activityUsers, uniqueIdentities,
		"User owned service account "+serviceAccount+" has performed activities on the resource", "a", resourceContextInsertDoc)

	serviceAccountName := resourceutils.GetGCPServiceAccountName(resourceContext, serviceAccount, eventInfo.Account)

	// Add Service Account as an owner of this resource via Activity
	activityUsers[eventInfo.EventTime+"a"] = context.GetUserContextItem(resourceContext, serviceAccountName+contextutils.SERVICEACCOUNT_USER_SUFFIX, common.ACTIVITY_USER_TYPE,
		serviceAccountDesc, serviceAccount, resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
			Name:   eventInfo.Event,
			Region: eventInfo.Region,
			Time:   eventInfo.EventTime,
		})
}

func GetServiceAccountOwnersForActivity(resourceContext *rcontext.ResourceContext, serviceAccount string, eventInfo EventInfo,
	activityUsers map[string]common.ResourceContextItem, uniqueIdentities map[string]struct{}, desc, sortAlphabet string, resourceContextInsertDoc common.ResourceContextInsertDoc) {

	if _, ok := uniqueIdentities[serviceAccount]; ok {
		return
	}

	uniqueIdentities[serviceAccount] = struct{}{}
	depth := len(sortAlphabet)

	saDoc := resourceutils.GetServiceAccountDoc(resourceContext, serviceAccount, eventInfo.Account)

	// Resourcename user will not be derived for the service identity at this time. Additional entry for it if exists
	serviceAccountName := serviceAccount
	if len(saDoc.ResourceName) > 0 {
		serviceAccountName = saDoc.ResourceName
	}

	activityUsers[eventInfo.EventTime+sortAlphabet+"c"] = context.GetUserContextItem(resourceContext, serviceAccountName+contextutils.PLACEHOLDER_USER_SUFFIX, common.ACTIVITY_USER_TYPE,
		desc, "", resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
			Name:          eventInfo.Event,
			Region:        eventInfo.Region,
			Time:          eventInfo.EventTime,
			IndirectEvent: true,
		})

	for i, defined := range saDoc.DefinedOwners {
		if strings.HasSuffix(defined.Name, contextutils.SERVICEACCOUNT_USER_SUFFIX) {
			if len(defined.IdentityId) > 0 && depth <= 5 {
				// Recursive call
				GetServiceAccountOwnersForActivity(resourceContext, defined.IdentityId, eventInfo, activityUsers, uniqueIdentities, getIdentityChainDescription(desc, defined.IdentityId, depth), sortAlphabet+sortAlphabet, resourceContextInsertDoc)
			}
		} else if emailutils.IsNonHumanEmail(defined.Name, resourceContext) {
			uniqueGroupEmailIdentities := make(map[string]struct{})
			GetNonHumanEmailOwnersForActivity(resourceContext, defined.Name, eventInfo, activityUsers, uniqueGroupEmailIdentities, "User owned group email "+defined.Name+" has performed activities on the resource", sortAlphabet+sortAlphabet)
		} else {
			if _, ok := uniqueIdentities[defined.Name]; !ok || depth == 1 {
				uniqueIdentities[defined.Name] = struct{}{}
				activityUsers[eventInfo.EventTime+sortAlphabet+"b"+fmt.Sprintf("%03d", len(saDoc.DefinedOwners)-i)] = context.GetUserContextItem(resourceContext, defined.Name, common.ACTIVITY_USER_TYPE,
					desc, "", resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
						Name:          eventInfo.Event,
						Region:        eventInfo.Region,
						Time:          eventInfo.EventTime,
						IndirectEvent: true,
					})
			}
		}
	}

	for i, derived := range saDoc.DerivedOwners {
		if strings.HasSuffix(derived.Name, contextutils.SERVICEACCOUNT_USER_SUFFIX) {
			if len(derived.IdentityId) > 0 && depth <= 5 {
				// Recursive call
				GetServiceAccountOwnersForActivity(resourceContext, derived.IdentityId, eventInfo, activityUsers, uniqueIdentities, getIdentityChainDescription(desc, derived.IdentityId, depth), sortAlphabet+sortAlphabet, resourceContextInsertDoc)
			}
		} else if emailutils.IsNonHumanEmail(derived.Name, resourceContext) {
			uniqueGroupEmailIdentities := make(map[string]struct{})
			GetNonHumanEmailOwnersForActivity(resourceContext, derived.Name, eventInfo, activityUsers, uniqueGroupEmailIdentities, "User owned group email "+derived.Name+" has performed activities on the resource", sortAlphabet+sortAlphabet)
		} else {
			if _, ok := uniqueIdentities[derived.Name]; !ok || depth == 1 {
				uniqueIdentities[derived.Name] = struct{}{}
				activityUsers[eventInfo.EventTime+sortAlphabet+"a"+fmt.Sprintf("%03d", len(saDoc.DerivedOwners)-i)] = context.GetUserContextItem(resourceContext, derived.Name, common.ACTIVITY_USER_TYPE,
					desc, "", resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
						Name:          eventInfo.Event,
						Region:        eventInfo.Region,
						Time:          eventInfo.EventTime,
						IndirectEvent: true,
					})
			}
		}
	}
}

func getServiceAccountKeyOwners(resourceContext *rcontext.ResourceContext, serviceAccountKey string, eventInfo EventInfo,
	activityUsers map[string]common.ResourceContextItem, resourceContextInsertDoc common.ResourceContextInsertDoc) {

	serviceAccountKeyContextID := common.GenerateCombinedHashID(serviceAccountKey, common.GCP_SERVICEACCOUNTKEY_RESOURCE_TYPE, eventInfo.Account, resourceContext.LastCollectedAt, resourceContext.TenantID)

	if servAccRscInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(serviceAccountKeyContextID); ok {
		for i, defined := range servAccRscInsertDoc.DefinedOwners {
			if strings.HasSuffix(defined.Name, contextutils.SERVICEACCOUNT_USER_SUFFIX) {
				uniqueIdentities := make(map[string]struct{})
				if len(defined.IdentityId) > 0 {
					GetServiceAccountOwnersForActivity(resourceContext, defined.IdentityId, eventInfo, activityUsers, uniqueIdentities, "User owned service account key "+serviceAccountKey+" has performed activities on the resource", "c", resourceContextInsertDoc)
				}
			} else {
				activityUsers[eventInfo.EventTime+"c"+fmt.Sprintf("%03d", len(servAccRscInsertDoc.DefinedOwners)-i)] = context.GetUserContextItem(resourceContext, defined.Name, common.ACTIVITY_USER_TYPE,
					"User owned Service Account Key "+serviceAccountKey+" has been used to do activities on the resource", "", resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
						Name:          eventInfo.Event,
						Region:        eventInfo.Region,
						Time:          eventInfo.EventTime,
						IndirectEvent: true,
					})
			}
		}

		for i, derived := range servAccRscInsertDoc.DerivedOwners {
			if strings.HasSuffix(derived.Name, contextutils.SERVICEACCOUNT_USER_SUFFIX) {
				uniqueIdentities := make(map[string]struct{})
				if len(derived.IdentityId) > 0 {
					GetServiceAccountOwnersForActivity(resourceContext, derived.IdentityId, eventInfo, activityUsers, uniqueIdentities, "User owned service account key "+serviceAccountKey+" has performed activities on the resource", "b", resourceContextInsertDoc)
				}
			} else {
				activityUsers[eventInfo.EventTime+"b"+fmt.Sprintf("%03d", len(servAccRscInsertDoc.DerivedOwners)-i)] = context.GetUserContextItem(resourceContext, derived.Name, common.ACTIVITY_USER_TYPE,
					"User owned Service Account Key "+serviceAccountKey+" has been used to do activities on the resource", "", resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
						Name:          eventInfo.Event,
						Region:        eventInfo.Region,
						Time:          eventInfo.EventTime,
						IndirectEvent: true,
					})
			}
		}
	}
}

func GetServiceAccountOwnersForPolicy(resourceContext *rcontext.ResourceContext, serviceAccount, defaultProject string,
	resourceContextInsertDoc *common.ResourceContextInsertDoc, uniqueIdentities map[string]struct{}, desc string, depth int) {

	if _, ok := uniqueIdentities[serviceAccount]; ok {
		return
	}

	uniqueIdentities[serviceAccount] = struct{}{}

	saDoc := resourceutils.GetServiceAccountDoc(resourceContext, serviceAccount, defaultProject)

	// Resourcename user will not be derived for the service identity at this time. Additional entry for it if exists
	serviceAccountName := serviceAccount
	if len(saDoc.ResourceName) > 0 {
		serviceAccountName = saDoc.ResourceName
	}

	resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
		resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners, context.GetUserContextItem(resourceContext, serviceAccountName+contextutils.PLACEHOLDER_USER_SUFFIX, common.POLICYBINDING_USER_TYPE,
			desc, "", resourceContextInsertDoc.Account, nil))

	for _, defined := range saDoc.DefinedOwners {
		if strings.HasSuffix(defined.Name, contextutils.SERVICEACCOUNT_USER_SUFFIX) {
			if len(defined.IdentityId) > 0 && depth <= 5 {
				// Recursive call
				GetServiceAccountOwnersForPolicy(resourceContext, defined.IdentityId, defaultProject, resourceContextInsertDoc, uniqueIdentities, getIdentityChainDescription(desc, defined.IdentityId, depth), depth+1)
			}
		} else if emailutils.IsNonHumanEmail(defined.Name, resourceContext) {
			uniqueGroupEmailIdentities := make(map[string]struct{})
			GetNonHumanEmailOwners(resourceContext, defined.Name, resourceContextInsertDoc, uniqueGroupEmailIdentities, "User owned group email "+defined.Name+" has been assigned policy binding for the resource", depth+1)
		} else {
			if _, ok := uniqueIdentities[defined.Name]; !ok || depth == 1 {
				uniqueIdentities[defined.Name] = struct{}{}
				resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
					resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners, context.GetUserContextItem(resourceContext, defined.Name, common.POLICYBINDING_USER_TYPE,
						desc, "", resourceContextInsertDoc.Account, nil))
			}
		}
	}

	for _, derived := range saDoc.DerivedOwners {
		if strings.HasSuffix(derived.Name, contextutils.SERVICEACCOUNT_USER_SUFFIX) {
			if len(derived.IdentityId) > 0 && depth <= 5 {
				// Recursive call
				GetServiceAccountOwnersForPolicy(resourceContext, derived.IdentityId, defaultProject, resourceContextInsertDoc, uniqueIdentities, getIdentityChainDescription(desc, derived.IdentityId, depth), depth+1)
			}
		} else if emailutils.IsNonHumanEmail(derived.Name, resourceContext) {
			uniqueGroupEmailIdentities := make(map[string]struct{})
			GetNonHumanEmailOwners(resourceContext, derived.Name, resourceContextInsertDoc, uniqueGroupEmailIdentities, "User owned group email "+derived.Name+" has been assigned policy binding for the resource", depth+1)
		} else {
			if _, ok := uniqueIdentities[derived.Name]; !ok || depth == 1 {
				uniqueIdentities[derived.Name] = struct{}{}
				resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
					resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners, context.GetUserContextItem(resourceContext, derived.Name, common.POLICYBINDING_USER_TYPE,
						desc, "", resourceContextInsertDoc.Account, nil))
			}
		}
	}
}

func getGCPEventActor(eventJSONString string, username *string) (actorType string) {

	var gcpLog GCPLog

	if err := json.Unmarshal([]byte(eventJSONString), &gcpLog); err != nil {
		return
	}

	authInfo := gcpLog.ProtoPayload.AuthenticationInfo

	if len(authInfo.ServiceAccountDelegationInfo) > 0 {
		for _, delegation := range authInfo.ServiceAccountDelegationInfo {
			if principalEmail := delegation.FirstPartyPrincipal.PrincipalEmail; len(principalEmail) > 0 {
				if common.IsServiceAccountEmail(principalEmail) {
					emailSplit := strings.Split(principalEmail, "serviceAccount:")
					if len(emailSplit) > 1 {
						principalEmail = emailSplit[1]
						if !common.IsServiceAccountEmail(principalEmail) {
							continue
						} else {
							*username = principalEmail
							*username += contextutils.SERVICEACCOUNT_USER_SUFFIX
							return "GCP Service Account (Impersonated) "
						}
					}
				} else {
					emailSplit := strings.Split(principalEmail, "user:")
					if len(emailSplit) > 1 {
						emailSplit = strings.Split(emailSplit[1], "?uid")
						principalEmail = emailSplit[0]
					}
					*username = principalEmail
					return "GCP User (Impersonated) "
				}
			}
		}
	}

	if originalPrincipal := authInfo.ServiceDelegationHistory.OriginalPrincipal; len(originalPrincipal) > 0 {
		if strings.Contains(originalPrincipal, "serviceAccount:") {
			emailSplit := strings.Split(originalPrincipal, "serviceAccount:")
			if len(emailSplit) > 1 {
				email := emailSplit[1]
				if common.IsServiceAccountEmail(email) {
					*username = email
					*username += contextutils.SERVICEACCOUNT_USER_SUFFIX
					return "GCP Service Account (Delegated) "
				}
			}
		}
		if strings.Contains(originalPrincipal, "user:") {
			emailSplit := strings.Split(originalPrincipal, "user:")
			if len(emailSplit) > 1 {
				emailSplit = strings.Split(emailSplit[1], "?uid")
				*username = emailSplit[0]
				return "GCP User (Delegated) "
			}
		}
	}

	if keyName := authInfo.ServiceAccountKeyName; len(keyName) > 0 {
		*username += contextutils.SERVICEACCOUNT_USER_SUFFIX
		return "GCP Service Account (Key Used) "
	}

	if common.IsServiceAccountEmail(*username) {
		*username += contextutils.SERVICEACCOUNT_USER_SUFFIX
		return "GCP Service Account "
	}

	return ""
}

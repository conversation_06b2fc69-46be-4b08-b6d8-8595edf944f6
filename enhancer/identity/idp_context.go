package identity

import (
	"encoding/json"
	"strings"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/enhancer/internal/resource"
	"github.com/precize/enhancer/rcontext"
	"github.com/precize/logger"
)

func getOktaUsers(resourceContext *rcontext.ResourceContext, userMaps *UserMaps) {

	var (
		searchAfter    any
		oktaUsersQuery = `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"idpType.keyword":"` + common.OKTA_IDP_TYPE + `"}}]}}}`
	)

	for {
		oktaUsersDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.IDP_USERS_INDEX}, oktaUsersQuery, searchAfter)
		if err != nil {
			return
		}

		if len(oktaUsersDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		for _, oktaUsersDoc := range oktaUsersDocs {

			oktaUser, err := json.Marshal(oktaUsersDoc)
			if err != nil {
				logger.Print(logger.ERROR, "Got error marshalling document", []string{resourceContext.TenantID}, err)
				continue
			}

			var oktaUserDoc common.IDPUsersDoc

			if err = json.Unmarshal(oktaUser, &oktaUserDoc); err != nil {
				logger.Print(logger.ERROR, "Got error unmarshalling document", []string{resourceContext.TenantID}, err)
				continue
			}

			if addr, err := common.ParseAddress(oktaUserDoc.Email); err == nil {

				// TODO: Check if this is a cloud user by verifying applications they have access to

				for _, app := range oktaUserDoc.Apps {
					resourceContext.SetIDPMappedUser(app.AppUsername, addr.Address)
				}

				var (
					userResource *rcontext.UserContext
					deleted      bool
					userID       = oktaUserDoc.UserID // default value email
				)

				if oktaUserDoc.Status == "DEPROVISIONED" || oktaUserDoc.Status == "DELETED" {
					deleted = true
				}

				if u, ok := resourceContext.GetUserResource(addr.Address); ok {
					if !u.Active && !deleted {
						// If an okta user existed before which was deleted and then got created again with same email
						// Consider the active one
						userResource = &rcontext.UserContext{
							Name:   common.ConvertToTitleCase(oktaUserDoc.Name),
							Email:  addr.Address,
							Active: !deleted,
							Type:   map[string]struct{}{common.OKTA_USER_IDENTITY_TYPE: {}},

							Manager:    map[string]string{},
							JobTitle:   map[string]struct{}{},
							Department: map[string]struct{}{},
							Team:       map[string]struct{}{},
							Groups:     map[string]struct{}{},
						}
					} else {
						userResource = u
					}
				} else {
					userResource = &rcontext.UserContext{
						Name:   common.ConvertToTitleCase(oktaUserDoc.Name),
						Email:  addr.Address,
						Active: !deleted,
						Type:   map[string]struct{}{common.OKTA_USER_IDENTITY_TYPE: {}},

						Manager:    map[string]string{},
						JobTitle:   map[string]struct{}{},
						Department: map[string]struct{}{},
						Team:       map[string]struct{}{},
						Groups:     map[string]struct{}{},
					}
				}

				userResource.Sources = append(userResource.Sources, rcontext.UserContextSource{
					ResourceID:   userID,
					AccountID:    oktaUserDoc.Domain,
					IdentityType: common.OKTA_USER_RESOURCE_TYPE,
				})

				if _, err := common.ParseAddress(userResource.Name); err == nil || len(userResource.Name) <= 0 {
					userResource.Name = common.GetFormattedNameFromEmail(addr.Address)
				}

				if userResource.Active {
					userMaps.emailNameMap[addr.Address] = userResource.Name
				} else {
					resourceContext.SetEmailStatus(addr.Address, false)
					resourceContext.SetUpdateUndeliverableValidEmail(addr.Address)
				}

				resourceContext.SetUserIDToEmail(userID, addr.Address)

				if _, err := common.ParseAddress(oktaUserDoc.ManagerID); err == nil {
					userResource.Manager[oktaUserDoc.ManagerID] = EMAIL_TYPE
				} else if _, err = common.ParseAddress(oktaUserDoc.ManagerName); err == nil {
					userResource.Manager[oktaUserDoc.ManagerName] = EMAIL_TYPE
				} else if len(oktaUserDoc.ManagerID) > 0 {
					userResource.Manager[oktaUserDoc.ManagerID] = ID_TYPE
				}

				if len(userResource.Groups) <= 0 {
					userResource.Groups = make(map[string]struct{})
				}

				for _, group := range oktaUserDoc.Groups {
					userResource.Groups[group] = struct{}{}
				}

				if len(oktaUserDoc.Department) > 0 {
					userResource.Team[resourceutils.FormatContextValue(oktaUserDoc.Department)] = struct{}{}
					userResource.Department[oktaUserDoc.Department] = struct{}{}
				}

				if len(oktaUserDoc.Title) > 0 {
					userResource.JobTitle[oktaUserDoc.Title] = struct{}{}

					for dept := range userResource.Department {
						for title := range userResource.JobTitle {
							orgMapKey := strings.ToLower(dept + title)
							userMaps.orgMap[orgMapKey] = append(userMaps.orgMap[orgMapKey], userResource.Email)
						}
					}
				}

				resourceContext.SetUserResource(addr.Address, userResource)
			}
		}
	}
}

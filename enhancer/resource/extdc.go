package resource

import (
	"encoding/json"
	"strings"

	"github.com/precize/common"
	"github.com/precize/common/context"
	"github.com/precize/enhancer/context"
	"github.com/precize/logger"
)

func GetExternalDCContext(resourcesDoc map[string]any, resourceContextInsertDoc *common.ResourceContextInsertDoc) {

	if extContextMap, ok := resourcesDoc["extContext"].(map[string]any); ok {
		if orcaDetailsStr, ok := extContextMap["orcaDetails"].(string); ok && len(orcaDetailsStr) > 0 {
			var orcaDetails map[string]any
			err := json.Unmarshal([]byte(orcaDetailsStr), &orcaDetails)
			if err == nil {
				extOrcaDetailsBytes, err := json.Marshal(orcaDetails)
				if err == nil {
					var orcaAssetsDetails common.OrcaAssetData
					err := json.Unmarshal(extOrcaDetailsBytes, &orcaAssetsDetails)
					if err == nil {
						softwareComponents := orcaAssetsDetails.Model.Data.Compute.Applications.Models
						if len(softwareComponents) > 0 {
							for _, softwareComponent := range softwareComponents {
								softwareComponentName := softwareComponent.ModelItem.Name

								if softwareNameValue := context.GetSoftwareNameFromValue(softwareComponentName); len(softwareNameValue) > 0 {
									softwareComponentName = softwareNameValue
								}

								resourceContextInsertDoc.ResourceSoftwareTypes.DerivedSoftware = append(resourceContextInsertDoc.ResourceSoftwareTypes.DerivedSoftware,
									common.ResourceContextItem{
										Name: softwareComponentName,
										Type: common.ORCACONTEXT_SOFTWARE_TYPE,
									},
								)
							}
						}

						if orcaAssetsDetails.Model.Data.Content.HasPii || orcaAssetsDetails.Model.Data.Content.HasSensitiveKeys {

							var sensitivityDetails string
							if len(orcaAssetsDetails.Model.Data.Content.PiiTypes) > 0 {
								sensitivityDetails = strings.Join(orcaAssetsDetails.Model.Data.Content.PiiTypes, ",")
							}
							resourceContextInsertDoc.ResourceSensitivityTypes.DerivedSensitivity = append(resourceContextInsertDoc.ResourceSensitivityTypes.DerivedSensitivity,
								common.ResourceContextItem{
									Name: contextutils.PII_SENSITIVITY,
									Type: common.ORCACONTEXT_SENSITIVITY_TYPE,
									Desc: sensitivityDetails,
								},
							)

							if orcaAssetsDetails.Model.Data.Content.HasSensitiveKeys {
								resourceContextInsertDoc.ResourceSensitivityTypes.DerivedSensitivity = append(
									resourceContextInsertDoc.ResourceSensitivityTypes.DerivedSensitivity,
									common.ResourceContextItem{
										Name: contextutils.CONFIDENTIAL_SENSITIVITY,
										Type: common.ORCACONTEXT_SENSITIVITY_TYPE,
									},
								)
							}
						}
					} else {
						logger.Print(logger.INFO, "failed to unmarshal", []string{resourceContextInsertDoc.TenantID}, string(extOrcaDetailsBytes))
					}
				} else {
					logger.Print(logger.INFO, "failed to marshal", []string{resourceContextInsertDoc.TenantID}, orcaDetails)
				}
			} else {
				logger.Print(logger.INFO, "failed to unmarshal", []string{resourceContextInsertDoc.TenantID}, orcaDetailsStr)
			}
		}

		if defenderDetailsStr, ok := extContextMap["defenderDetails"].(string); ok && len(defenderDetailsStr) > 0 {
			var defenderDetails []string
			err := json.Unmarshal([]byte(defenderDetailsStr), &defenderDetails)
			if err == nil {
				for _, softwareName := range defenderDetails {
					if softwareNameVal := context.GetSoftwareNameFromValue(softwareName); len(softwareNameVal) > 0 {
						softwareName = softwareNameVal
					}

					resourceContextInsertDoc.ResourceSoftwareTypes.DerivedSoftware = append(resourceContextInsertDoc.ResourceSoftwareTypes.DerivedSoftware,
						common.ResourceContextItem{
							Name: softwareName,
							Type: common.DEFENDERCONTEXT_SOFTWARE_TYPE,
						},
					)
				}
			} else {
				logger.Print(logger.INFO, "failed to unmarshal", []string{resourceContextInsertDoc.TenantID}, defenderDetailsStr)
			}
		}
	}
}

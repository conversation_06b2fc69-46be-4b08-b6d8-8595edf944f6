package resource

import (
	"encoding/json"
	"sync"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/enhancer/activity"
	"github.com/precize/enhancer/context"
	"github.com/precize/enhancer/rcontext"
	"github.com/precize/enhancer/source/customer"
	"github.com/precize/enhancer/source/previous"
	"github.com/precize/logger"
)

const (
	MAX_IDENTITY_THREAD = 10
)

func GetOktaContext(resourceContext *rcontext.ResourceContext) {
	logger.Print(logger.INFO, "Processing started for okta user context", []string{resourceContext.TenantID})

	var (
		searchAfter            any
		oktaUserResourcesQuery = `{"_source":["entityId","accountId","tags","entityJson"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"entityType.keyword":"` + common.OKTA_USER_RESOURCE_TYPE + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.OKTA_SERVICE_ID + `}}]}}}`
		oktaUserResourcesChan  = make(chan map[string]map[string]any, MAX_IDENTITY_THREAD)
		semaphore              = make(chan struct{}, MAX_IDENTITY_THREAD)
		wg                     sync.WaitGroup
		batchWg                sync.WaitGroup
		collectedDocIDs        []string
		mutex                  sync.Mutex
	)

	go func() {
		for {
			oktaUserResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, oktaUserResourcesQuery, searchAfter)
			if err != nil {
				close(oktaUserResourcesChan)
				return
			}

			if len(oktaUserResourcesDocs) > 0 {
				searchAfter = sortResponse
				oktaUserResourcesChan <- oktaUserResourcesDocs
			} else {
				close(oktaUserResourcesChan)
				return
			}
		}
	}()

	for oktaUserResourcesDocs := range oktaUserResourcesChan {
		for oktaUserDocID, oktaUserResourcesDoc := range oktaUserResourcesDocs {
			semaphore <- struct{}{}

			wg.Add(1)
			go func(docID string, doc map[string]any) {
				defer wg.Done()
				defer func() { <-semaphore }()

				contextDocID := processOktaUser(resourceContext, doc, oktaUserDocID)

				if contextDocID != "" {
					mutex.Lock()
					collectedDocIDs = append(collectedDocIDs, contextDocID)

					if len(collectedDocIDs) > 999 {
						docIDsBatch := make([]string, len(collectedDocIDs))
						copy(docIDsBatch, collectedDocIDs)

						batchWg.Add(1)
						go func(batch []string) {
							defer batchWg.Done()
							activity.ProcessIDPActivityBatch(resourceContext, batch, common.OKTA_IDP_TYPE)
							previous.ProcessPreviousContext(resourceContext, batch)
						}(docIDsBatch)

						collectedDocIDs = []string{}
					}
					mutex.Unlock()
				}
			}(oktaUserDocID, oktaUserResourcesDoc)
		}
	}

	wg.Wait()

	logger.Print(logger.INFO, "Processing complete for okta user context", []string{resourceContext.TenantID})
}

func processOktaUser(resourceContext *rcontext.ResourceContext, oktaUserResourcesDoc map[string]any, oktaUserResourcesDocID string) (contextDocID string) {

	var (
		resourceContextInsertDoc common.ResourceContextInsertDoc
	)

	if oktaUserEmail, ok := oktaUserResourcesDoc["entityId"].(string); ok {

		if oktaDomain, ok := oktaUserResourcesDoc["accountId"].(string); ok {

			contextDocID = common.GenerateCombinedHashID(oktaUserEmail, common.OKTA_USER_RESOURCE_TYPE, oktaDomain, resourceContext.LastCollectedAt, resourceContext.TenantID)

			resourceContextInsertDoc = common.ResourceContextInsertDoc{
				ResourceID:         oktaUserEmail,
				ResourceType:       common.OKTA_USER_RESOURCE_TYPE,
				Account:            oktaDomain,
				TenantID:           resourceContext.TenantID,
				Region:             "Global",
				ServiceID:          common.OKTA_SERVICE_ID_INT,
				CloudResourceDocID: oktaUserResourcesDocID,
			}

			customer.SetCustomerEntityIncludeContextOfResource(resourceContext, &resourceContextInsertDoc)

			if entityJSON, ok := oktaUserResourcesDoc["entityJson"].(string); ok {

				entityJSONMap := make(map[string]any)

				if err := json.Unmarshal([]byte(entityJSON), &entityJSONMap); err != nil {
					logger.Print(logger.ERROR, "Failed to unmarshal", err)
					return
				}

				if managerEmail, ok := entityJSONMap["managerId"].(string); ok && len(managerEmail) > 0 {
					resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
						resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
						context.GetUserContextItem(resourceContext, managerEmail, common.MANAGER_OWNER_USER_TYPE, "", "", resourceContextInsertDoc.Account, nil),
					)
				}
			}

			resourceContext.SetResourceContextInsertDoc(contextDocID, resourceContextInsertDoc)
		}
	}

	return
}

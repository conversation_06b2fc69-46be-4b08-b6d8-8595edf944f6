package resource

import (
	"encoding/json"
	"strconv"
	"strings"
	"sync"

	"github.com/precize/common"
	contextutils "github.com/precize/common/context"
	"github.com/precize/elastic"
	"github.com/precize/enhancer/context"
	resourceutils "github.com/precize/enhancer/internal/resource"
	"github.com/precize/enhancer/rcontext"
	"github.com/precize/enhancer/related"
	"github.com/precize/enhancer/source/attribute"
	"github.com/precize/enhancer/source/customer"
	"github.com/precize/enhancer/source/previous"
	"github.com/precize/enhancer/source/similarity"
	"github.com/precize/logger"
)

const (
	MAX_RESOURCE_THREAD = 10
)

func GetResourceContext(resourceContext *rcontext.ResourceContext) {

	logger.Print(logger.INFO, "Processing started for resource context", []string{resourceContext.TenantID})

	resourcesAggQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + resourceContext.ServiceID + `}}],"must_not":[{"terms":{"entityType.keyword":[` + GetNonDataResourcesElasticQuery() + `]}}]}},"size":0,"aggs":{"account_aggregation":{"terms":{"field":"accountId.keyword","size":10000}}}}`
	aggResponse, err := elastic.ExecuteSearchForAggregation([]string{elastic.CLOUD_RESOURCES_INDEX}, resourcesAggQuery)
	if err != nil {
		logger.Print(logger.ERROR, "Error executing aggregation query", []string{err.Error()})
		return
	}

	accountIds := make([]string, 0)

	if accAggs, ok := aggResponse["account_aggregation"].(map[string]any); ok {
		if accBuckets, ok := accAggs["buckets"].([]any); ok {
			for _, accBucket := range accBuckets {
				if accBucketMap, ok := accBucket.(map[string]any); ok {
					if rg, ok := accBucketMap["key"].(string); ok {
						accountIds = append(accountIds, rg)
					}
				}
			}
		}
	}

	sem := make(chan struct{}, MAX_RESOURCE_THREAD)

	var wg sync.WaitGroup

	for _, accountID := range accountIds {

		sem <- struct{}{}

		wg.Add(1)

		go func(resourceContext *rcontext.ResourceContext, accID string) {
			defer func() {
				<-sem
				wg.Done()
			}()

			ProcessResourceContext(resourceContext, accID)
		}(resourceContext, accountID)
	}

	wg.Wait()
	close(sem)

	logger.Print(logger.INFO, "Processing complete for resource context", []string{resourceContext.TenantID})
}

func ProcessResourceContext(resourceContext *rcontext.ResourceContext, accountID string) {

	var (
		searchAfter          any
		similarResourceNames = make(map[string][]string)
		sameApps             = make(map[string][]string)
		sameTags             = make(map[string][]string)
	)

	resourcesQuery := `{"_source":["entityId","entityType","accountId","resourceGroup","tags","region","serviceId","entityJson","extContext", "osType"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + resourceContext.ServiceID + `}},{"term":{"accountId.keyword":"` + accountID + `"}}],"must_not":[{"terms":{"entityType.keyword":[` + GetNonDataResourcesElasticQuery() + `]}}]}}}`

	for {

		resourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, resourcesQuery, searchAfter)
		if err != nil {
			return
		}

		if len(resourcesDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		logger.Print(logger.INFO, "Resources fetched", len(resourcesDocs))

		var (
			countForCollectedActivityFetch int
			collectedDocIDs                []string
		)

		for resourcesDocID, resourcesDoc := range resourcesDocs {

			var (
				resourceContextInsertDoc        common.ResourceContextInsertDoc
				serviceID, resourceNameTagValue string
				tags                            []attribute.TagStruct
			)

			if resourceID, ok := resourcesDoc["entityId"].(string); ok {

				if resourceType, ok := resourcesDoc["entityType"].(string); ok {

					accountID, _ := resourcesDoc["accountId"].(string)
					region, _ := resourcesDoc["region"].(string)
					resourceGroup, _ := resourcesDoc["resourceGroup"].(string)
					entityJSONString, _ := resourcesDoc["entityJson"].(string)

					entityJSON := make(map[string]any)
					if err := json.Unmarshal([]byte(entityJSONString), &entityJSON); err != nil {
						continue
					}

					contextDocID := common.GenerateCombinedHashID(resourceID, resourceType, accountID, resourceContext.LastCollectedAt, resourceContext.TenantID)

					serviceIDFloat, ok := resourcesDoc["serviceId"].(float64)
					if ok {
						serviceID = strconv.FormatFloat(serviceIDFloat, 'f', 0, 64)
					}

					if resourceContextInsertDoc, ok = resourceContext.GetResourceContextInsertDoc(contextDocID); !ok {

						resourceContextInsertDoc = common.ResourceContextInsertDoc{
							ResourceID:         resourceID,
							ResourceType:       resourceType,
							Account:            accountID,
							ResourceGroup:      resourceGroup,
							TenantID:           resourceContext.TenantID,
							Region:             region,
							ServiceID:          int(serviceIDFloat),
							CloudResourceDocID: resourcesDocID,
						}
					}

					switch resourceType {
					case common.IPDETAILS_RESOURCE_TYPE:
						if onbaordedIP, ok := entityJSON["onboardedIP"].(bool); ok && !onbaordedIP {
							resourceContextInsertDoc.SkipContext = true
						} else if portInfo, ok := entityJSON["portInformation"].(map[string]any); ok {
							if onbaordedIP, ok := portInfo["onboardedIP"].(bool); ok && !onbaordedIP {
								resourceContextInsertDoc.SkipContext = true
							}
						}
					}

					if resourceContextInsertDoc.SkipContext {
						resourceContext.SetResourceContextInsertDoc(contextDocID, resourceContextInsertDoc)
						continue
					}

					customer.SetCustomerEntityIncludeContextOfResource(resourceContext, &resourceContextInsertDoc)
					attribute.GetResourceInbuiltProperty(resourceContext, entityJSON, &resourceContextInsertDoc)
					resourceNameTagValue, tags = attribute.GetTagContextOfResource(resourceContext, resourcesDoc, &resourceContextInsertDoc)
					similarity.GetSameTagsResourceList(sameTags, contextDocID, tags)
					context.GetSensitivityContext(entityJSON, &resourceContextInsertDoc, resourceContext)

					switch serviceID {
					case common.AWS_SERVICE_ID:
						resourceContextInsertDoc.ResourceName = GetNameForAWSResource(resourceID, resourceType, resourceNameTagValue, entityJSONString)
					case common.AZURE_SERVICE_ID:
						resourceContextInsertDoc.ResourceName = GetNameForAzureResource(resourceID, resourceType, entityJSONString)
					case common.GCP_SERVICE_ID:
						resourceContextInsertDoc.ResourceName = GetNameForGCPResource(resourceID, resourceType, entityJSONString)
					case common.OPENAI_SERVICE_ID:
						resourceContextInsertDoc.ResourceName = GetNameForOpenAIResource(resourceID, entityJSONString)
					default:
						if name := GetResourceNameFromEntityJSON(entityJSONString); len(name) > 0 {
							resourceContextInsertDoc.ResourceName = name
						}
					}

					context.GetTTLFromResourceName(resourceContext, &resourceContextInsertDoc, entityJSON)
					related.GetRelatedResourceList(resourceContext, resourceContextInsertDoc, contextDocID, entityJSONString)
					similarity.GetSimilarResourceNameList(similarResourceNames, contextDocID, resourceContextInsertDoc)

					if common.IsVirtualResource(resourceType) {
						// Most of them are excluded early in query, but some needs to reach here for related resources
						continue
					}

					defaultResource := IsDefaultResource(resourceContextInsertDoc.ResourceType, resourceID, entityJSON)
					resourceContextInsertDoc.IsDefaultResource = defaultResource

					if defaultResource {
						resourceContextInsertDoc.ResourceDeploymentTypes.DerivedDeployment = append(resourceContextInsertDoc.ResourceDeploymentTypes.DerivedDeployment,
							common.ResourceContextItem{
								Name: "Default Provisioned",
								Type: common.DEFAULT_RESOURCE_DEPLOYMENT_TYPE,
							},
						)
					}

					switch serviceID {

					case common.AWS_SERVICE_ID:

						accContextID := common.GenerateCombinedHashID(accountID, common.AWS_ACCOUNT_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContext.TenantID)

						if accRscInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(accContextID); ok {

							resourceContextInsertDoc.ResourceOwnerTypes.InheritedOwners = context.GetInheritedOwners(accRscInsertDoc, common.AWS_ACCOUNT_RESOURCE_TYPE)
							if defaultResource {
								resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners, context.GetInheritedOwnersForDefaultResources(accRscInsertDoc, common.AWS_ACCOUNT_RESOURCE_TYPE)...)
							}
							resourceContextInsertDoc.ResourceEnvTypes.InheritedEnv = context.GetInheritedEnv(accRscInsertDoc)
							resourceContextInsertDoc.ResourceCostCenterTypes.InheritedCostCenter = context.GetInheritedCostCenter(accRscInsertDoc)
							resourceContextInsertDoc.ResourceTeamTypes.InheritedTeam = context.GetInheritedTeam(accRscInsertDoc)

							resourceContextInsertDoc.ResourceOwnerTypes.CostOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.CostOwners, accRscInsertDoc.CostOwners...)
							resourceContextInsertDoc.ResourceOwnerTypes.SecurityOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.SecurityOwners, accRscInsertDoc.SecurityOwners...)
							resourceContextInsertDoc.ResourceOwnerTypes.OpsOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.OpsOwners, accRscInsertDoc.OpsOwners...)

						}

						if jiraOpsOwners, ok := resourceContext.GetJiraOpsOwners(accountID); ok {
							if jiraOpsOwners, ok := jiraOpsOwners[resourceType]; ok {
								for _, jiraOpsOwner := range jiraOpsOwners {
									resourceContextInsertDoc.ResourceOwnerTypes.OpsOwners = append(
										resourceContextInsertDoc.ResourceOwnerTypes.OpsOwners,
										context.GetUserContextItem(resourceContext, jiraOpsOwner, common.JIRA_USER_TYPE, "User has worked on JIRA issues in this account related to this resource type", "", resourceContextInsertDoc.Account, nil),
									)
								}
							}
						}

					case common.AZURE_SERVICE_ID:

						if len(resourceGroup) > 0 {

							rgContextID := common.GenerateCombinedHashID(strings.ToLower(resourceGroup), common.AZURE_RG_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContext.TenantID)

							if rgRscInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(rgContextID); ok {

								resourceContextInsertDoc.ResourceOwnerTypes.InheritedOwners = context.GetInheritedOwners(rgRscInsertDoc, common.AZURE_RG_RESOURCE_TYPE)
								if defaultResource {
									resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners, context.GetInheritedOwnersForDefaultResources(rgRscInsertDoc, common.AZURE_RG_RESOURCE_TYPE)...)
								}
								resourceContextInsertDoc.ResourceEnvTypes.InheritedEnv = context.GetInheritedEnv(rgRscInsertDoc)
								resourceContextInsertDoc.ResourceCostCenterTypes.InheritedCostCenter = context.GetInheritedCostCenter(rgRscInsertDoc)
								resourceContextInsertDoc.ResourceTeamTypes.InheritedTeam = context.GetInheritedTeam(rgRscInsertDoc)

								resourceContextInsertDoc.ResourceSensitivityTypes.InheritedSensitivity = context.GetInheritedSensitivity(rgRscInsertDoc)
								resourceContextInsertDoc.ResourceComplianceTypes.InheritedCompliance = context.GetInheritedCompliance(rgRscInsertDoc)

							}
						}

						subsContextID := common.GenerateCombinedHashID(accountID, common.AZURE_SUBSCRIPTION_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContext.TenantID)

						if subsRscCtxInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(subsContextID); ok {
							if len(resourceContextInsertDoc.ResourceOwnerTypes.InheritedOwners) <= 0 {
								resourceContextInsertDoc.ResourceOwnerTypes.InheritedOwners = context.GetInheritedOwners(subsRscCtxInsertDoc, common.AZURE_SUBSCRIPTION_RESOURCE_TYPE)
								if defaultResource {
									resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners, context.GetInheritedOwnersForDefaultResources(subsRscCtxInsertDoc, common.AZURE_SUBSCRIPTION_RESOURCE_TYPE)...)
								}
							}
							if len(resourceContextInsertDoc.ResourceEnvTypes.InheritedEnv) <= 0 {
								resourceContextInsertDoc.ResourceEnvTypes.InheritedEnv = context.GetInheritedEnv(subsRscCtxInsertDoc)
							}
							if len(resourceContextInsertDoc.ResourceCostCenterTypes.InheritedCostCenter) <= 0 {
								resourceContextInsertDoc.ResourceCostCenterTypes.InheritedCostCenter = context.GetInheritedCostCenter(subsRscCtxInsertDoc)
							}
							if len(resourceContextInsertDoc.ResourceTeamTypes.InheritedTeam) <= 0 {
								resourceContextInsertDoc.ResourceTeamTypes.InheritedTeam = context.GetInheritedTeam(subsRscCtxInsertDoc)
							}

							resourceContextInsertDoc.ResourceOwnerTypes.CostOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.CostOwners, subsRscCtxInsertDoc.CostOwners...)
							resourceContextInsertDoc.ResourceOwnerTypes.SecurityOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.SecurityOwners, subsRscCtxInsertDoc.SecurityOwners...)

						}

						if jiraOpsOwners, ok := resourceContext.GetJiraOpsOwners(accountID); ok {
							if jiraOpsOwners, ok := jiraOpsOwners[resourceType]; ok {
								for _, jiraOpsOwner := range jiraOpsOwners {
									resourceContextInsertDoc.ResourceOwnerTypes.OpsOwners = append(
										resourceContextInsertDoc.ResourceOwnerTypes.OpsOwners,
										context.GetUserContextItem(resourceContext, jiraOpsOwner, common.JIRA_USER_TYPE, "User has worked on JIRA issues in this subscription related to this resource type", "", resourceContextInsertDoc.Account, nil),
									)
								}
							}
						}

					case common.GCP_SERVICE_ID:

						projectContextID := common.GenerateCombinedHashID(accountID, common.GCP_PROJECT_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContext.TenantID)

						if prjRscInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(projectContextID); ok {

							resourceContextInsertDoc.ResourceOwnerTypes.InheritedOwners = context.GetInheritedOwners(prjRscInsertDoc, common.GCP_PROJECT_RESOURCE_TYPE)
							if defaultResource {
								resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners, context.GetInheritedOwnersForDefaultResources(prjRscInsertDoc, common.GCP_PROJECT_RESOURCE_TYPE)...)
							}
							resourceContextInsertDoc.ResourceEnvTypes.InheritedEnv = context.GetInheritedEnv(prjRscInsertDoc)
							resourceContextInsertDoc.ResourceCostCenterTypes.InheritedCostCenter = context.GetInheritedCostCenter(prjRscInsertDoc)
							resourceContextInsertDoc.ResourceTeamTypes.InheritedTeam = context.GetInheritedTeam(prjRscInsertDoc)

						}

						if jiraOpsOwners, ok := resourceContext.GetJiraOpsOwners(accountID); ok {
							if jiraOpsOwners, ok := jiraOpsOwners[resourceType]; ok {
								for _, jiraOpsOwner := range jiraOpsOwners {
									resourceContextInsertDoc.ResourceOwnerTypes.OpsOwners = append(
										resourceContextInsertDoc.ResourceOwnerTypes.OpsOwners,
										context.GetUserContextItem(resourceContext, jiraOpsOwner, common.JIRA_USER_TYPE, "User has worked on JIRA issues in this project related to this resource type", "", resourceContextInsertDoc.Account, nil),
									)
								}
							}
						}
					}

					if envName := context.GetEnvironmentNameFromValue(resourceContextInsertDoc.ResourceName); len(envName) > 0 {
						resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv = append(resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv,
							common.ResourceContextItem{
								Name: envName,
								Type: common.RESOURCE_NAME_ENV_TYPE,
							},
						)

						context.IncrementParentChildEnvCount(resourceContext, envName, accountID, resourceGroup)
					}

					if teamName := context.GetTeamNameFromValue(resourceContextInsertDoc.ResourceName); len(teamName) > 0 {

						resourceContextInsertDoc.ResourceTeamTypes.DerivedTeam = append(
							resourceContextInsertDoc.ResourceTeamTypes.DerivedTeam,
							common.ResourceContextItem{
								Name: teamName,
								Type: common.RESOURCE_NAME_TEAM_TYPE,
							},
						)
					}

					if softwareName := context.GetSoftwareNameFromValue(resourceContextInsertDoc.ResourceName); len(softwareName) > 0 {

						resourceContextInsertDoc.ResourceSoftwareTypes.DerivedSoftware = append(resourceContextInsertDoc.ResourceSoftwareTypes.DerivedSoftware,
							common.ResourceContextItem{
								Name: softwareName,
								Type: common.RESOURCE_NAME_SOFTWARE_TYPE,
							},
						)
					}

					if appName := context.GetAppNameFromValue(resourceContextInsertDoc.ResourceName, rcontext.WithResourceType(resourceContextInsertDoc.ResourceType)); len(appName) > 0 {

						resourceContextInsertDoc.ResourceAppTypes.DerivedApp = append(resourceContextInsertDoc.ResourceAppTypes.DerivedApp,
							common.ResourceContextItem{
								Name: appName,
								Type: common.RESOURCE_NAME_APP_TYPE,
							},
						)
					}

					// Deployment should not be derived from resource name

					if resourceutils.IsDataResourceType(resourceType) {

						if complianceName := context.GetComplianceNameFromValue(resourceContextInsertDoc.ResourceName); len(complianceName) > 0 {
							resourceContextInsertDoc.ResourceComplianceTypes.DerivedCompliance = append(resourceContextInsertDoc.ResourceComplianceTypes.DerivedCompliance,
								common.ResourceContextItem{
									Name: complianceName,
									Type: common.RESOURCE_NAME_COMPLIANCE_TYPE,
								},
							)
						}

						if sensitivityName := context.GetSensitivityNameFromValue(resourceContextInsertDoc.ResourceName); len(sensitivityName) > 0 {
							resourceContextInsertDoc.ResourceSensitivityTypes.DerivedSensitivity = append(resourceContextInsertDoc.ResourceSensitivityTypes.DerivedSensitivity,
								common.ResourceContextItem{
									Name: sensitivityName,
									Type: common.RESOURCE_NAME_SENSITIVITY_TYPE,
								},
							)
						}
					}

					GetExternalDCContext(resourcesDoc, &resourceContextInsertDoc)

					switch resourceType {
					case common.AWS_EC2_RESOURCE_TYPE:

						uniquePortSoftware := make(map[string]struct{})

						if securityGroups, ok := entityJSON["securityGroups"].([]any); ok {
							for _, securityGroup := range securityGroups {

								if sgID, ok := securityGroup.(string); ok {

									for port, software := range contextutils.PortToSoftware {

										if _, ok := uniquePortSoftware[software]; ok {
											continue
										}

										uniquePortSoftware[software] = struct{}{}

										if ports, ok := resourceContext.GetNetworkInboundPorts(sgID); ok {
											if _, ok := ports[port]; ok {
												resourceContextInsertDoc.ResourceSoftwareTypes.DerivedSoftware = append(resourceContextInsertDoc.ResourceSoftwareTypes.DerivedSoftware,
													common.ResourceContextItem{
														Name: software,
														Type: common.DEFAULT_PORT_SOFTWARE_TYPE,
													},
												)
											}
										}
									}
								}
							}
						}

					case common.AZURE_VM_RESOURCE_TYPE:

						uniquePortSoftware := make(map[string]struct{})

						if networkInterfaces, ok := entityJSON["azureNetworkInterfaces"].([]any); ok {
							for _, networkInterface := range networkInterfaces {

								if networkInterfaceJSON, ok := networkInterface.(map[string]any); ok {

									if networkProperties, ok := networkInterfaceJSON["properties"].(map[string]any); ok {

										if nsg, ok := networkProperties["networkSecurityGroup"].(map[string]any); ok {

											if nsgID, ok := nsg["id"].(string); ok {

												for port, software := range contextutils.PortToSoftware {

													if _, ok := uniquePortSoftware[software]; ok {
														continue
													}

													uniquePortSoftware[software] = struct{}{}

													if ports, ok := resourceContext.GetNetworkInboundPorts(strings.ToLower(nsgID)); ok {
														if _, ok := ports[port]; ok {
															resourceContextInsertDoc.ResourceSoftwareTypes.DerivedSoftware = append(resourceContextInsertDoc.ResourceSoftwareTypes.DerivedSoftware,
																common.ResourceContextItem{
																	Name: software,
																	Type: common.DEFAULT_PORT_SOFTWARE_TYPE,
																},
															)
														}
													}
												}
											}
										}
									}
								}
							}
						}

					case common.GCP_INSTANCE_RESOURCE_TYPE: // TODO

					case common.GCP_SERVICEACCOUNT_RESOURCE_TYPE, common.GCP_USER_RESOURCE_TYPE, common.AWS_SSOUSER_RESOURCE_TYPE, common.AWS_IAM_ROLE_RESOURCE_TYPE, common.AWS_IAM_USER_RESOURCE_TYPE, common.AZURE_ADUSER_RESOURCE_TYPE:

						switch resourceType {

						case common.GCP_SERVICEACCOUNT_RESOURCE_TYPE:
							getServiceAccountIAMPolicies(resourceContext, &resourceContextInsertDoc, entityJSONString)

						case common.AZURE_ADUSER_RESOURCE_TYPE:

							if manager, ok := entityJSON["manager"].(map[string]any); ok {
								if managerID, ok := manager["id"].(string); ok {
									if managerEmail, ok := resourceContext.GetUserIDToEmail(managerID); ok {
										resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
											resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
											context.GetUserContextItem(resourceContext, managerEmail, common.MANAGER_OWNER_USER_TYPE, "", "", resourceContextInsertDoc.Account, nil),
										)
									}
								}
							}
						}

						if apps, ok := resourceContext.GetIdentityApps(resourceID); ok {
							for _, app := range apps {
								resourceContextInsertDoc.ResourceAppTypes.DerivedApp = append(resourceContextInsertDoc.ResourceAppTypes.DerivedApp,
									common.ResourceContextItem{
										Name: app,
										Type: common.ACTIVITY_APP_TYPE,
									},
								)
							}
						}
					}

					similarity.GetSameApplicationResourceList(sameApps, contextDocID, resourceContextInsertDoc)

					resourceContext.SetResourceContextInsertDoc(contextDocID, resourceContextInsertDoc)

					countForCollectedActivityFetch++
					collectedDocIDs = append(collectedDocIDs, contextDocID)

					if countForCollectedActivityFetch > 999 {
						ProcessBatchContext(resourceContext, collectedDocIDs)
						previous.ProcessPreviousContext(resourceContext, collectedDocIDs)
						context.ProcessResourceNamesForApps(resourceContext, collectedDocIDs)

						countForCollectedActivityFetch = 0
						collectedDocIDs = []string{}
					}
				}
			}
		}

		if countForCollectedActivityFetch > 0 {
			ProcessBatchContext(resourceContext, collectedDocIDs)
			previous.ProcessPreviousContext(resourceContext, collectedDocIDs)
			context.ProcessResourceNamesForApps(resourceContext, collectedDocIDs)

			countForCollectedActivityFetch = 0
		}
	}

	for _, docIDs := range similarResourceNames {
		if len(docIDs) > 1 {

			similarResourceNameDocIDs := make([]string, 0, len(docIDs)-1)

			for i, docID := range docIDs {
				similarResourceNameDocIDs = similarResourceNameDocIDs[:0]

				similarResourceNameDocIDs = append(similarResourceNameDocIDs, docIDs[:i]...)
				similarResourceNameDocIDs = append(similarResourceNameDocIDs, docIDs[i+1:]...)

				resourceContext.SetSimilarResourceNameList(docID, similarResourceNameDocIDs)
			}
		}
	}

	for _, docIDs := range sameApps {
		if len(docIDs) > 1 {

			sameAppDocIDs := make([]string, 0, len(docIDs)-1)

			for i, docID := range docIDs {

				sameAppDocIDs = sameAppDocIDs[:0]

				sameAppDocIDs = append(sameAppDocIDs, docIDs[:i]...)
				sameAppDocIDs = append(sameAppDocIDs, docIDs[i+1:]...)

				resourceContext.SetSameAppResourceList(docID, sameAppDocIDs)
			}
		}
	}

	for _, docIDs := range sameTags {
		if len(docIDs) > 1 {

			sameTagDocIDs := make([]string, 0, len(docIDs)-1)

			for i, docID := range docIDs {
				sameTagDocIDs = sameTagDocIDs[:0]

				sameTagDocIDs = append(sameTagDocIDs, docIDs[:i]...)
				sameTagDocIDs = append(sameTagDocIDs, docIDs[i+1:]...)

				resourceContext.SetSameTagResourceList(docID, sameTagDocIDs)
			}
		}
	}
}

func FetchRctxDocsFromIDs(docIDs []string, resourceContext *rcontext.ResourceContext) (rctxDocs []common.ResourceContextInsertDoc, err error) {

	var (
		rctxQuery   = `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"terms":{"id.keyword":["` + strings.Join(docIDs, `","`) + `"]}}]}}}`
		searchAfter any
	)

	for {
		docs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.RESOURCE_CONTEXT_INDEX}, rctxQuery, searchAfter)
		if err != nil {
			return rctxDocs, err
		}

		if len(docs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		for _, rctxDoc := range docs {
			var rctx common.ResourceContextInsertDoc
			jsonBytes, er := json.Marshal(rctxDoc)
			if er != nil {
				return rctxDocs, er
			}
			err = json.Unmarshal(jsonBytes, &rctx)
			if err != nil {
				return rctxDocs, err
			}

			rctxDocs = append(rctxDocs, rctx)
		}
	}

	return
}

package postprocess

import (
	"path/filepath"
	"slices"
	"strings"
	"sync"

	"github.com/precize/common"
	"github.com/precize/enhancer/context"
	"github.com/precize/enhancer/rcontext"
)

func PostProcessRelatedResources(docID string, resourceContext *rcontext.ResourceContext, resourceContextDoc *common.ResourceContextInsertDoc, uniqueOwners, uniqueApp, uniqueTeam *sync.Map, enhancedUniqueOwners, enhancedUniqueApps, enhancedUniqueTeams *[]string) {

	var additionalRelations bool

	if relatedResources, ok := resourceContext.GetRelatedResourceList(docID); ok {

		var updatedRelatedResources = append([]rcontext.RelatedResource{}, relatedResources...)

		for _, relatedResource := range relatedResources {
			if len(relatedResource.TransitiveResourceType) > 0 {
				if level2RelatedResources, ok := resourceContext.GetRelatedResourceList(relatedResource.ResourceDocID); ok {
					for _, level2RelatedResource := range level2RelatedResources {
						if level2RelatedResource.ResourceType == relatedResource.TransitiveResourceType {
							updatedRelatedResources = append(updatedRelatedResources, level2RelatedResource)
							additionalRelations = true
						}
					}
				}
			}
		}

		for _, relatedResource := range updatedRelatedResources {
			if relatedResource.ContextualRelation {
				if ownersVal, ok := uniqueOwners.Load(relatedResource.ResourceDocID); ok {
					uniqueResourceOwners, validCast := ownersVal.([]string)
					if validCast {
						if relatedResource.Parent && len(uniqueResourceOwners) > 0 {
							// Parent relation only
							directParentOwner := uniqueResourceOwners[0]

							resourceContextDoc.ResourceOwnerTypes.DerivedOwners = append(resourceContextDoc.ResourceOwnerTypes.DerivedOwners,
								common.ResourceContextItem{
									Name: directParentOwner,
									Type: common.PARENT_RESOURCE_OWNER_USER_TYPE,
									Desc: "User owns parent " + relatedResource.ResourceType + " of this resource",
								},
							)

							if !slices.Contains(*enhancedUniqueOwners, directParentOwner) {
								*enhancedUniqueOwners = append(*enhancedUniqueOwners, directParentOwner)
							}
						}
					}
				}

				if appsVal, ok := uniqueApp.Load(relatedResource.ResourceDocID); ok {
					uniqueResourceApps, validCast := appsVal.([]string)
					if validCast {
						for _, uniqueResourceApp := range uniqueResourceApps {

							if !slices.Contains(*enhancedUniqueApps, uniqueResourceApp) {

								resourceContextDoc.ResourceAppTypes.DerivedApp = append(
									resourceContextDoc.ResourceAppTypes.DerivedApp,
									common.ResourceContextItem{
										Name: uniqueResourceApp,
										Type: common.RELATED_RESOURCE_APP_TYPE,
									},
								)

								*enhancedUniqueApps = append(*enhancedUniqueApps, uniqueResourceApp)
							}
						}
					}
				}

				if teamsVal, ok := uniqueTeam.Load(relatedResource.ResourceDocID); ok {
					uniqueResourceTeams, validCast := teamsVal.([]string)
					if validCast {
						for _, uniqueResourceTeam := range uniqueResourceTeams {

							if !slices.Contains(*enhancedUniqueTeams, uniqueResourceTeam) {

								resourceContextDoc.ResourceTeamTypes.DerivedTeam = append(
									resourceContextDoc.ResourceTeamTypes.DerivedTeam,
									common.ResourceContextItem{
										Name: uniqueResourceTeam,
										Type: common.RELATED_RESOURCE_TEAM_TYPE,
									},
								)

								*enhancedUniqueTeams = append(*enhancedUniqueTeams, uniqueResourceTeam)
							}
						}
					}
				}
			}
		}

		if additionalRelations {
			resourceContext.SetRelatedResourceList(docID, updatedRelatedResources)
		}
	}
}

func PostProcessMaxRelatedOwner(docID string, resourceContext *rcontext.ResourceContext, resourceContextDoc *common.ResourceContextInsertDoc, uniqueOwners *sync.Map, enhancedUniqueOwners *[]string) {

	if len(*enhancedUniqueOwners) <= 0 {

		var (
			maxCount        int
			maxOwner        string
			relatedOwners   = make(map[string]context.RelatedOwner)
			processedDocIDs = make(map[string]struct{})
		)

		context.GetMaxRelatedOwner(resourceContext, resourceContextDoc, docID, uniqueOwners, &maxCount, &maxOwner, "", relatedOwners, processedDocIDs, 1, false)

		if len(maxOwner) > 0 {

			var (
				desc         string
				relRes       []string
				iacRes       []string
				directString = "indirectly"
			)

			for resourceType, resources := range relatedOwners[maxOwner].Resources {
				if relatedOwners[maxOwner].IacRelation {
					for _, resource := range resources {
						if len(iacRes) < 2 {
							str := resource + " (" + resourceType + ")"
							if !slices.Contains(iacRes, str) {
								iacRes = append(iacRes, str)
							}
						}
					}
				} else {
					if relatedOwners[maxOwner].Direct {
						directString = "directly"
					}
					for _, resource := range resources {
						if len(relRes) < 2 {
							str := resource + " (" + resourceType + ")"
							if !slices.Contains(relRes, str) {
								relRes = append(relRes, str)
							}
						}
					}
				}
			}

			if len(iacRes) > 0 {
				var (
					initialWording    = "a resource"
					conclusionWording = ": "
				)
				if len(iacRes) > 1 {
					initialWording = "multiple resources"
					conclusionWording = ", including "
				}
				desc = "User owns " + initialWording + " from the same IaC deployment" + conclusionWording + strings.Join(iacRes, " and ")
			}

			if len(relRes) > 0 {
				if len(desc) > 0 {
					desc += ". "
				}
				var (
					initialWording    = "a resource " + directString
					conclusionWording = ": "
				)
				if len(relRes) > 1 {
					initialWording = "multiple resources " + directString
					conclusionWording = ", including "
				}
				desc += "User owns " + initialWording + " related to this resource" + conclusionWording + strings.Join(relRes, " and ")
			}

			if len(desc) > 0 {
				if len(relatedOwners[maxOwner].Via) > 0 {
					desc += " via " + strings.Join(relatedOwners[maxOwner].Via, " and ")
				}
			} else {
				desc = "User owns resources related to this resource"
			}

			resourceContextDoc.ResourceOwnerTypes.DerivedOwners = append(resourceContextDoc.ResourceOwnerTypes.DerivedOwners,
				common.ResourceContextItem{
					Name: maxOwner,
					Type: common.RELATED_RESOURCE_OWNER_USER_TYPE,
					Desc: desc,
				},
			)

			*enhancedUniqueOwners = append(*enhancedUniqueOwners, maxOwner)
		}
	}
}

type maxOwner struct {
	count    int
	resource string
}

func PostProcessSimilarResourceNames(docID string, resourceContext *rcontext.ResourceContext, resourceContextDoc *common.ResourceContextInsertDoc, uniqueOwners, uniqueApp, uniqueTeam *sync.Map, enhancedUniqueOwners, enhancedUniqueApps, enhancedUniqueTeams *[]string) {

	var (
		maxOwnerMap        = make(map[string]maxOwner)
		maxCount           int
		maxOwner, resource string
	)

	if similarResourceNameDocIDs, ok := resourceContext.GetSimilarResourceNameList(docID); ok {

		for _, similarResourceDocID := range similarResourceNameDocIDs {

			if ownersVal, ok := uniqueOwners.Load(similarResourceDocID); ok {

				if similarResourceInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(similarResourceDocID); ok {
					uniqueResourceOwners, validCast := ownersVal.([]string)
					if validCast {
						for _, uniqueResourceOwner := range uniqueResourceOwners {
							tmp := maxOwnerMap[uniqueResourceOwner]
							tmp.count++
							if len(tmp.resource) <= 0 {
								resourceID := filepath.Base(similarResourceInsertDoc.ResourceID)
								tmp.resource = resourceID + " (" + similarResourceInsertDoc.ResourceType + ")"
							}
							maxOwnerMap[uniqueResourceOwner] = tmp
						}
					}
				}
			}

			if appsVal, ok := uniqueApp.Load(similarResourceDocID); ok {
				uniqueResourceApps, validCast := appsVal.([]string)
				if validCast {
					for _, uniqueResourceApp := range uniqueResourceApps {
						if !slices.Contains(*enhancedUniqueApps, uniqueResourceApp) {

							resourceContextDoc.ResourceAppTypes.DerivedApp = append(
								resourceContextDoc.ResourceAppTypes.DerivedApp,
								common.ResourceContextItem{
									Name: uniqueResourceApp,
									Type: common.SIMILAR_RESOURCENAME_APP_TYPE,
								},
							)

							*enhancedUniqueApps = append(*enhancedUniqueApps, uniqueResourceApp)
						}
					}
				}
			}

			if teamsVal, ok := uniqueTeam.Load(similarResourceDocID); ok {
				uniqueResourceTeams, validCast := teamsVal.([]string)
				if validCast {
					for _, uniqueResourceTeam := range uniqueResourceTeams {
						if !slices.Contains(*enhancedUniqueTeams, uniqueResourceTeam) {

							resourceContextDoc.ResourceTeamTypes.DerivedTeam = append(
								resourceContextDoc.ResourceTeamTypes.DerivedTeam,
								common.ResourceContextItem{
									Name: uniqueResourceTeam,
									Type: common.SIMILAR_RESOURCENAME_TEAM_TYPE,
								},
							)

							*enhancedUniqueTeams = append(*enhancedUniqueTeams, uniqueResourceTeam)
						}
					}
				}
			}
		}
	}

	if len(*enhancedUniqueOwners) <= 0 {

		// Owner is through majority
		for owner, details := range maxOwnerMap {
			if details.count > maxCount {
				maxCount = details.count
				maxOwner = owner
				resource = details.resource
			}
		}

		if len(maxOwner) > 0 {

			resourceContextDoc.ResourceOwnerTypes.DerivedOwners = append(resourceContextDoc.ResourceOwnerTypes.DerivedOwners,
				common.ResourceContextItem{
					Name: maxOwner,
					Type: common.SIMILAR_RESOURCENAME_OWNER_USER_TYPE,
					Desc: "User owns resource(s) including " + resource + " that are likely to be related to this resource",
				},
			)

			if !slices.Contains(*enhancedUniqueOwners, maxOwner) {
				*enhancedUniqueOwners = append(*enhancedUniqueOwners, maxOwner)
			}
		}
	}
}

func PostProcessSameAppResources(docID string, resourceContext *rcontext.ResourceContext, resourceContextDoc *common.ResourceContextInsertDoc, uniqueOwners, uniqueTeam *sync.Map, enhancedUniqueOwners, enhancedUniqueTeams *[]string) {

	var (
		maxOwnerMap        = make(map[string]maxOwner)
		maxCount           int
		maxOwner, resource string
	)

	if similarAppDocIDs, ok := resourceContext.GetSameAppResourceList(docID); ok {

		for _, sameAppDocID := range similarAppDocIDs {

			if ownersVal, ok := uniqueOwners.Load(sameAppDocID); ok {

				if sameAppResourceDoc, ok := resourceContext.GetResourceContextInsertDoc(sameAppDocID); ok {
					uniqueResourceOwners, validCast := ownersVal.([]string)
					if validCast {
						for _, uniqueResourceOwner := range uniqueResourceOwners {
							tmp := maxOwnerMap[uniqueResourceOwner]
							tmp.count++
							if len(tmp.resource) <= 0 {
								resourceID := filepath.Base(sameAppResourceDoc.ResourceID)
								tmp.resource = resourceID + " (" + sameAppResourceDoc.ResourceType + ")"
							}
							maxOwnerMap[uniqueResourceOwner] = tmp
						}
					}
				}
			}

			if teamsVal, ok := uniqueTeam.Load(sameAppDocID); ok {
				uniqueResourceTeams, validCast := teamsVal.([]string)
				if validCast {
					for _, uniqueResourceTeam := range uniqueResourceTeams {
						if !slices.Contains(*enhancedUniqueTeams, uniqueResourceTeam) {

							resourceContextDoc.ResourceTeamTypes.DerivedTeam = append(
								resourceContextDoc.ResourceTeamTypes.DerivedTeam,
								common.ResourceContextItem{
									Name: uniqueResourceTeam,
									Type: common.SAME_APP_TEAM_TYPE,
								},
							)

							*enhancedUniqueTeams = append(*enhancedUniqueTeams, uniqueResourceTeam)
						}
					}
				}
			}
		}
	}

	// Owner is through majority
	for owner, details := range maxOwnerMap {
		if details.count > maxCount {
			maxCount = details.count
			maxOwner = owner
			resource = details.resource
		}
	}

	if len(maxOwner) > 0 {

		resourceContextDoc.ResourceOwnerTypes.DerivedOwners = append(resourceContextDoc.ResourceOwnerTypes.DerivedOwners,
			common.ResourceContextItem{
				Name: maxOwner,
				Type: common.SAME_APP_OWNER_USER_TYPE,
				Desc: "User owns resource(s) including " + resource + " that are part of the same application as this resource",
			},
		)

		if !slices.Contains(*enhancedUniqueOwners, maxOwner) {
			*enhancedUniqueOwners = append(*enhancedUniqueOwners, maxOwner)
		}
	}
}

func PostProcessSameTagResources(docID string, resourceContext *rcontext.ResourceContext, resourceContextDoc *common.ResourceContextInsertDoc, uniqueOwners, uniqueApp, uniqueTeam *sync.Map, enhancedUniqueOwners, enhancedUniqueApps, enhancedUniqueTeams *[]string) {

	var (
		maxOwnerMap        = make(map[string]maxOwner)
		maxCount           int
		maxOwner, resource string
	)

	if similarTagDocIDs, ok := resourceContext.GetSameTagResourceList(docID); ok {

		for _, sameTagDocID := range similarTagDocIDs {

			if ownersVal, ok := uniqueOwners.Load(sameTagDocID); ok {

				if sameTagResourceDoc, ok := resourceContext.GetResourceContextInsertDoc(sameTagDocID); ok {
					uniqueResourceOwners, validCast := ownersVal.([]string)
					if validCast {
						for _, uniqueResourceOwner := range uniqueResourceOwners {
							tmp := maxOwnerMap[uniqueResourceOwner]
							tmp.count++
							if len(tmp.resource) <= 0 {
								resourceID := filepath.Base(sameTagResourceDoc.ResourceID)
								tmp.resource = resourceID + " (" + sameTagResourceDoc.ResourceType + ")"
							}
							maxOwnerMap[uniqueResourceOwner] = tmp
						}
					}
				}
			}

			if appsVal, ok := uniqueApp.Load(sameTagDocID); ok {
				uniqueResourceApps, validCast := appsVal.([]string)
				if validCast {
					for _, uniqueResourceApp := range uniqueResourceApps {
						if !slices.Contains(*enhancedUniqueApps, uniqueResourceApp) {

							resourceContextDoc.ResourceAppTypes.DerivedApp = append(
								resourceContextDoc.ResourceAppTypes.DerivedApp,
								common.ResourceContextItem{
									Name: uniqueResourceApp,
									Type: common.SAME_TAG_APP_TYPE,
								},
							)

							*enhancedUniqueApps = append(*enhancedUniqueApps, uniqueResourceApp)
						}
					}
				}
			}

			if teamsVal, ok := uniqueTeam.Load(sameTagDocID); ok {
				uniqueResourceTeams, validCast := teamsVal.([]string)
				if validCast {
					for _, uniqueResourceTeam := range uniqueResourceTeams {
						if !slices.Contains(*enhancedUniqueTeams, uniqueResourceTeam) {

							resourceContextDoc.ResourceTeamTypes.DerivedTeam = append(
								resourceContextDoc.ResourceTeamTypes.DerivedTeam,
								common.ResourceContextItem{
									Name: uniqueResourceTeam,
									Type: common.SAME_TAG_TEAM_TYPE,
								},
							)

							*enhancedUniqueTeams = append(*enhancedUniqueTeams, uniqueResourceTeam)
						}
					}
				}
			}
		}
	}

	if len(*enhancedUniqueOwners) <= 0 {

		// Owner is through majority
		for owner, details := range maxOwnerMap {
			if details.count > maxCount {
				maxCount = details.count
				maxOwner = owner
				resource = details.resource
			}
		}

		if len(maxOwner) > 0 {

			resourceContextDoc.ResourceOwnerTypes.DerivedOwners = append(resourceContextDoc.ResourceOwnerTypes.DerivedOwners,
				common.ResourceContextItem{
					Name: maxOwner,
					Type: common.SAME_TAG_OWNER_USER_TYPE,
					Desc: "User owns resource(s) including " + resource + " that have the same tags as this resource",
				},
			)

			if !slices.Contains(*enhancedUniqueOwners, maxOwner) {
				*enhancedUniqueOwners = append(*enhancedUniqueOwners, maxOwner)
			}
		}
	}
}

func PostProcessResourceTypeOwner(resourceContext *rcontext.ResourceContext, resourceContextDoc *common.ResourceContextInsertDoc, enhancedUniqueOwners *[]string) {

	if len(resourceContextDoc.Account) > 0 {

		if rscTypeOwners, ok := resourceContext.GetResourceTypeOwner(resourceContextDoc.Account); ok {

			resourceTypeOwners := context.GetMaxActivityOwnersOfResourceType(rscTypeOwners, resourceContextDoc.ResourceType)

			for _, resourceTypeOwner := range resourceTypeOwners {
				resourceContextDoc.ResourceOwnerTypes.OpsOwners = append(resourceContextDoc.ResourceOwnerTypes.OpsOwners,
					common.ResourceContextItem{
						Name: resourceTypeOwner,
						Type: common.RESOURCE_TYPE_OWNER,
						Desc: context.GetStaticDescriptionOfUserType(common.RESOURCE_TYPE_OWNER),
					},
				)

				if !slices.Contains(*enhancedUniqueOwners, resourceTypeOwner) {
					*enhancedUniqueOwners = append(*enhancedUniqueOwners, resourceTypeOwner)
				}
			}
		}
	}
}

func PostProcessSmallAccounts(resourceContext *rcontext.ResourceContext, resourceContextDoc *common.ResourceContextInsertDoc, uniqueOwners *sync.Map, enhancedUniqueOwners *[]string) {

	if len(*enhancedUniqueOwners) <= 0 {

		switch resourceContextDoc.ResourceType {
		case common.AWS_ORG_RESOURCE_TYPE, common.GCP_ORG_RESOURCE_TYPE,
			common.OPENAI_ORG_RESOURCE_TYPE, common.AZURE_TENANT_RESOURCE_TYPE:
			// no parent owner

		default:
			if parentChildOwner, _ := resourceContext.GetParentChildOwner(resourceContextDoc.Account); len(parentChildOwner) == 0 {
				// Child resources doesn't have any owners, so possibly one/two person account

				var parentResourceDocID string

				switch resourceContext.ServiceID {
				case common.AWS_SERVICE_ID:
					if resourceContextDoc.ResourceType == common.AWS_ACCOUNT_RESOURCE_TYPE {
						parentResourceDocID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.AWS_ORG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.TenantID)
						if _, loadOk := uniqueOwners.Load(parentResourceDocID); !loadOk {
							parentResourceDocID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.AWS_ORGUNIT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.TenantID)
						}
					} else {
						parentResourceDocID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.AWS_ACCOUNT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.TenantID)
					}

				case common.AZURE_SERVICE_ID:
					if resourceContextDoc.ResourceType == common.AZURE_MGMTGRP_RESOURCE_TYPE {
						parentResourceDocID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.AZURE_TENANT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.TenantID)
					} else if resourceContextDoc.ResourceType == common.AZURE_SUBSCRIPTION_RESOURCE_TYPE {
						parentResourceDocID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.AZURE_MGMTGRP_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.TenantID)
						if _, loadOk := uniqueOwners.Load(parentResourceDocID); !loadOk {
							parentResourceDocID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.AZURE_TENANT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.TenantID)
						}
					} else {
						parentResourceDocID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.AZURE_SUBSCRIPTION_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.TenantID)
					}
				case common.GCP_SERVICE_ID:
					if resourceContextDoc.ResourceType == common.GCP_PROJECT_RESOURCE_TYPE {
						parentResourceDocID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.GCP_FOLDER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.TenantID)
						if _, loadOk := uniqueOwners.Load(parentResourceDocID); !loadOk {
							parentResourceDocID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.GCP_ORG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.TenantID)
						}
					} else if resourceContextDoc.ResourceType == common.GCP_FOLDER_RESOURCE_TYPE {
						parentResourceDocID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.GCP_FOLDER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.TenantID)
						if _, loadOk := uniqueOwners.Load(parentResourceDocID); !loadOk {
							parentResourceDocID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.GCP_ORG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.TenantID)
						}
					} else {
						parentResourceDocID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.GCP_PROJECT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.TenantID)
					}

				case common.OPENAI_SERVICE_ID:
					parentResourceDocID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.OPENAI_PROJECT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.TenantID)
				}

				if parentOwnersVal, loadOk := uniqueOwners.Load(parentResourceDocID); loadOk {
					parentOwners := parentOwnersVal.([]string)

					if len(parentOwners) > 2 {
						parentOwners = parentOwners[:2]
					}

					for _, parentOwner := range parentOwners {
						resourceContextDoc.ResourceOwnerTypes.DerivedOwners = append(resourceContextDoc.ResourceOwnerTypes.DerivedOwners,
							common.ResourceContextItem{
								Name: parentOwner,
								Type: common.NO_CHILD_OWNERS_PARENT_OWNER_USER_TYPE,
								Desc: context.GetStaticDescriptionOfUserType(common.NO_CHILD_OWNERS_PARENT_OWNER_USER_TYPE),
							},
						)

						*enhancedUniqueOwners = append(*enhancedUniqueOwners, parentOwner)
					}
				}

			} else if len(parentChildOwner) <= 2 {
				// Account resources have very few owners, so possibly one/two person account

				var parentChildOwners []string

				for childOwner := range parentChildOwner {
					parentChildOwners = append(parentChildOwners, childOwner)
				}

				slices.SortFunc(parentChildOwners, context.SortUsernameString)

				for _, childOwner := range parentChildOwners {
					resourceContextDoc.ResourceOwnerTypes.DerivedOwners = append(resourceContextDoc.ResourceOwnerTypes.DerivedOwners,
						common.ResourceContextItem{
							Name: childOwner,
							Type: common.FEW_CHILD_OWNERS_PARENT_OWNER_USER_TYPE,
							Desc: context.GetStaticDescriptionOfUserType(common.FEW_CHILD_OWNERS_PARENT_OWNER_USER_TYPE),
						},
					)

					*enhancedUniqueOwners = append(*enhancedUniqueOwners, childOwner)
				}
			}
		}
	}
}

func PostProcessParentComplianceAndSensitivity(docID string, resourceContextDoc *common.ResourceContextInsertDoc, uniqueSensitivity, uniqueCompliance, accountSensitivity, accountCompliance *sync.Map) {

	switch resourceContextDoc.ResourceType {
	case common.AWS_ACCOUNT_RESOURCE_TYPE, common.GCP_PROJECT_RESOURCE_TYPE,
		common.AZURE_RG_RESOURCE_TYPE:

		// propagate child sensitivity to parent resource
		existingSensitivities := make(map[string]struct{})
		if sensVal, ok := uniqueSensitivity.Load(docID); ok {
			for _, s := range sensVal.([]string) {
				existingSensitivities[s] = struct{}{}
			}
		}

		if childSensitivitiesVal, ok := accountSensitivity.Load(docID); ok {
			childSensitivities := childSensitivitiesVal.(map[string]struct{})
			for childSensitivity := range childSensitivities {
				resourceContextDoc.ResourceSensitivityTypes.DerivedSensitivity = append(resourceContextDoc.ResourceSensitivityTypes.DerivedSensitivity,
					common.ResourceContextItem{
						Name: childSensitivity,
						Desc: "Derived from child resource",
					},
				)

				if _, ok := existingSensitivities[childSensitivity]; !ok {
					sensitivityList := []string{}
					if sensVal, ok := uniqueSensitivity.Load(docID); ok {
						sensitivityList = sensVal.([]string)
					}
					sensitivityList = append(sensitivityList, childSensitivity)
					uniqueSensitivity.Store(docID, sensitivityList)
				}
			}
		}

		// propagate child compliance to parent resource
		existingCompliances := make(map[string]struct{})
		if compVal, ok := uniqueCompliance.Load(docID); ok {
			for _, s := range compVal.([]string) {
				existingCompliances[s] = struct{}{}
			}
		}

		if childCompliancesVal, ok := accountCompliance.Load(docID); ok {
			childCompliances := childCompliancesVal.(map[string]struct{})
			for childCompliance := range childCompliances {
				resourceContextDoc.ResourceComplianceTypes.DerivedCompliance = append(resourceContextDoc.ResourceComplianceTypes.DerivedCompliance,
					common.ResourceContextItem{
						Name: childCompliance,
						Desc: "Derived from child resource",
					},
				)

				if _, ok := existingCompliances[childCompliance]; !ok {
					complianceList := []string{}
					if compVal, ok := uniqueCompliance.Load(docID); ok {
						complianceList = compVal.([]string)
					}
					complianceList = append(complianceList, childCompliance)
					uniqueCompliance.Store(docID, complianceList)
				}
			}
		}
	}
}

func PostProcessTeamsOfOwner(resourceContext *rcontext.ResourceContext, resourceContextDoc *common.ResourceContextInsertDoc, enhancedUniqueOwners, enhancedUniqueTeams *[]string) {

	for _, uniqueOwner := range *enhancedUniqueOwners {
		if addr, err := common.ParseAddress(uniqueOwner); err == nil {
			if userResource, ok := resourceContext.GetUserResource(addr.Address); ok {
				for team := range userResource.Team {
					if !slices.Contains(*enhancedUniqueTeams, team) {
						resourceContextDoc.ResourceTeamTypes.DerivedTeam = append(
							resourceContextDoc.ResourceTeamTypes.DerivedTeam,
							common.ResourceContextItem{
								Name: team,
								Type: common.RESOURCE_OWNER_TEAM_TYPE,
								Desc: "Team of owner " + uniqueOwner,
							},
						)

						*enhancedUniqueTeams = append(*enhancedUniqueTeams, team)
					}
				}
			}
		}
	}
}

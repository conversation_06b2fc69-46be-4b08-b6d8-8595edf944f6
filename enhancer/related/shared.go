package related

import (
	"strings"

	"github.com/precize/common"
	"github.com/precize/enhancer/rcontext"
)

func processLoadBalancer(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if urlMap, ok := entityJSONMap["urlMap"].(map[string]any); ok {
		if defaultService, ok := urlMap["defaultService"].(string); ok && len(defaultService) >= 0 {
			var backendServiceID string
			splitName := strings.Split(defaultService, "/")
			for i, v := range splitName {
				if v == "projects" {
					backendServiceID = strings.Join(splitName[i:], "/")
					break
				}
			}
			backendServiceDocID := common.GenerateCombinedHashID(backendServiceID, common.GCP_BACKENDSERVICE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, backendServiceID, backendServiceDocID, common.GCP_BACKENDSERVICE_RESOURCE_TYPE)
		}
	}
	if properties, ok := entityJSONMap["properties"].(map[string]any); ok {
		if frontendIPConfigurations, ok := properties["frontendIPConfigurations"].([]any); ok {
			for _, ipConfig := range frontendIPConfigurations {
				if ipConfigMap, ok := ipConfig.(map[string]any); ok {
					if ipConfigProperties, ok := ipConfigMap["properties"].(map[string]any); ok {
						if publicIPAddress, ok := ipConfigProperties["publicIPAddress"].(map[string]any); ok {
							if publicIpAddressID, ok := publicIPAddress["id"].(string); ok && len(publicIpAddressID) > 0 {
								publicIpAddressID = strings.ToLower(publicIpAddressID)
								publicIPAddressDocID := common.GenerateCombinedHashID(publicIpAddressID, common.AZURE_PUBLICIP_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
								AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, publicIpAddressID, publicIPAddressDocID, common.AZURE_PUBLICIP_RESOURCE_TYPE)
							}
						}
					}
				}
			}
		}
	}
	if subnetIDs, ok := entityJSONMap["subnetIds"].([]any); ok {
		for _, subnetID := range subnetIDs {
			if subnetIDString, ok := subnetID.(string); ok && len(subnetIDString) > 0 {
				subnetIDString = strings.ToLower(subnetIDString)
				subnetDocID := common.GenerateCombinedHashID(subnetIDString, common.AZURE_SUBNET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, subnetIDString, subnetDocID, common.AZURE_SUBNET_RESOURCE_TYPE)
			}
		}
	}
	var clusterName, clusterRgName string
	if tags, ok := entityJSONMap["tags"].([]any); ok {
		for _, tag := range tags {
			if tagMap, ok := tag.(map[string]any); ok {
				if tagKey, ok := tagMap["key"].(string); ok && len(tagKey) > 0 {
					if tagValue, ok := tagMap["value"].(string); ok && len(tagValue) > 0 {
						switch tagKey {
						case "aks-managed-cluster-name":
							clusterName = tagValue
						case "aks-managed-cluster-rg":
							clusterRgName = tagValue
						}
					}
				}
			}
		}
	}
	if len(clusterName) > 0 && len(clusterRgName) > 0 {
		clusterID := "/subscriptions/" + resourceContextDoc.Account + "/resourcegroups/" + clusterRgName + "/providers/microsoft.containerservice/managedclusters/" + clusterName
		clusterID = strings.ToLower(clusterID)
		clusterDocID := common.GenerateCombinedHashID(clusterID, common.AZURE_AKSCLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, clusterID, clusterDocID, common.AZURE_AKSCLUSTER_RESOURCE_TYPE)
	}
}

func processNetworkInterface(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if networkInterfaceMap, ok := entityJSONMap["networkInterface"].(map[string]any); ok {
		if subnetID, ok := networkInterfaceMap["subnetId"].(string); ok && len(subnetID) > 0 {
			subnetDocID := common.GenerateCombinedHashID(subnetID, common.AWS_SUBNET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, subnetID, subnetDocID, common.AWS_SUBNET_RESOURCE_TYPE)
		}
		if vpcID, ok := networkInterfaceMap["vpcId"].(string); ok && len(vpcID) > 0 {
			vpcDocID := common.GenerateCombinedHashID(vpcID, common.AWS_VPC_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, vpcID, vpcDocID, common.AWS_VPC_RESOURCE_TYPE)
		}
		if securityGroups, ok := networkInterfaceMap["groups"].([]any); ok {
			for _, securityGroup := range securityGroups {
				if sg, ok := securityGroup.(map[string]any); ok {
					if sgID, ok := sg["groupId"].(string); ok && len(sgID) > 0 {
						sgDocID := common.GenerateCombinedHashID(sgID, common.AWS_SG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, sgID, sgDocID, common.AWS_SG_RESOURCE_TYPE)
					}
				}
			}
		}
	}
	if nwProperties, ok := entityJSONMap["properties"].(map[string]any); ok {
		if nwSecurityGroup, ok := nwProperties["networkSecurityGroup"].(map[string]any); ok {
			if nsgID, ok := nwSecurityGroup["id"].(string); ok && len(nsgID) > 0 {
				nsgID = strings.ToLower(nsgID)
				nsgDocID := common.GenerateCombinedHashID(nsgID, common.AZURE_NSG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, nsgID, nsgDocID, common.AZURE_NSG_RESOURCE_TYPE)
			}
		}
		if ipConfigs, ok := nwProperties["ipConfigurations"].([]any); ok {
			for _, ipConfig := range ipConfigs {
				if ipConfigMap, ok := ipConfig.(map[string]any); ok {
					if ipConfigProperties, ok := ipConfigMap["properties"].(map[string]any); ok {
						if subnet, ok := ipConfigProperties["subnet"].(map[string]any); ok {
							if subnetID, ok := subnet["id"].(string); ok && len(subnetID) > 0 {
								subnetID = strings.ToLower(subnetID)
								subnetDocID := common.GenerateCombinedHashID(subnetID, common.AZURE_SUBNET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
								AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, subnetID, subnetDocID, common.AZURE_SUBNET_RESOURCE_TYPE)
								var virtualNetworkID string
								subnetSplit := strings.Split(subnetID, "/")
								for i, v := range subnetSplit {
									if v == "virtualNetworks" {
										virtualNetworkID = strings.Join(subnetSplit[:i+1], "/")
										break
									}
								}
								virtualNetworkDocID := common.GenerateCombinedHashID(virtualNetworkID, common.AZURE_VIRTUALNETWORK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
								AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, virtualNetworkID, virtualNetworkDocID, common.AZURE_VIRTUALNETWORK_RESOURCE_TYPE)
							}
						}
						if publicIpAddress, ok := ipConfigProperties["publicIPAddress"].(map[string]any); ok {
							if publicIpAddressID, ok := publicIpAddress["id"].(string); ok && len(publicIpAddressID) > 0 {
								publicIpAddressID = strings.ToLower(publicIpAddressID)
								publicIPAddressDocID := common.GenerateCombinedHashID(publicIpAddressID, common.AZURE_PUBLICIP_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
								AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, publicIpAddressID, publicIPAddressDocID, common.AZURE_PUBLICIP_RESOURCE_TYPE)
							}
						}
					}
				}
			}
		}
	}
}

func handleGenericCases(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if resourceContextDoc.ServiceID == common.GCP_SERVICE_ID_INT && strings.Contains(resourceContextDoc.ResourceType, "k8s.io") {
		AssignRelatedResourcesFromSingleResourceCollector(resourceContext, resourceContextDoc, resourceDocID, entityJSONMap)
	} else if resourceContextDoc.ServiceID == common.AZURE_SERVICE_ID_INT && strings.Contains(resourceContextDoc.ResourceType, "K8s") {
		if clusterName, ok := entityJSONMap["clusterName"].(string); ok && len(clusterName) >= 0 {
			var clusterPrefix string
			splitName := strings.Split(resourceContextDoc.ResourceID, "/")
			for i, v := range splitName {
				if v == "managedclusters" {
					clusterPrefix = strings.Join(splitName[:i], "/")
					break
				}
			}
			if len(clusterPrefix) > 0 {
				clusterID := clusterPrefix + "/providers/microsoft.containerservice/managedclusters/" + strings.ToLower(clusterName)
				clusterDocID := common.GenerateCombinedHashID(clusterID, common.AZURE_AKSCLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, clusterID, clusterDocID, common.AZURE_AKSCLUSTER_RESOURCE_TYPE, RelatedResourceOpts{Parent: true, Oneway: true})
			}
		}
	}
}

func AssignRelatedResourcesFromSingleResourceCollector(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if k8sParentAssetType, ok := entityJSONMap["parentAssetType"].(string); ok && len(k8sParentAssetType) > 0 {
		k8sParentType := k8sParentAssetType
		k8sParentFullName, _ := entityJSONMap["parentFullResourceName"].(string)
		var k8sParentID string
		splitName := strings.Split(k8sParentFullName, "/")
		for i, v := range splitName {
			if v == "projects" {
				k8sParentID = strings.Join(splitName[i:], "/")
				break
			}
		}
		switch k8sParentType {
		case "rbac.authorization.k8s.io/ClusterRole":
			k8sParentType = common.GCP_GKECLUSTERROLE_RESOURCE_TYPE
		case "rbac.authorization.k8s.io/Role":
			k8sParentType = common.GCP_GKENAMESPACEROLE_RESOURCE_TYPE
		case "k8s.io/Namespace":
			k8sParentType = common.GCP_GKENAMESPACE_RESOURCE_TYPE
		case "k8s.io/ServiceAccount":
			k8sParentType = common.GCP_GKESERVICEACCOUNT_RESOURCE_TYPE
		case "apps.k8s.io/DaemonSet":
			k8sParentType = common.GCP_GKEDAEMONSET_RESOURCE_TYPE
		case "apps.k8s.io/ReplicaSet":
			k8sParentType = common.GCP_GKEREPLICASET_RESOURCE_TYPE
		case "container.googleapis.com/Cluster":
			k8sParentType = common.GCP_GKECLUSTER_RESOURCE_TYPE
		}
		if len(k8sParentID) > 0 {
			k8sParentDocID := common.GenerateCombinedHashID(k8sParentID, k8sParentType, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, k8sParentID, k8sParentDocID, k8sParentType, RelatedResourceOpts{Parent: true, Oneway: true})
		}
		if resourceContextDoc.ResourceType == common.GCP_GKENODE_RESOURCE_TYPE {
			var instancePrefix string
			splitName := strings.Split(resourceContextDoc.ResourceID, "/")
			for i, v := range splitName {
				if v == "clusters" {
					instancePrefix = strings.Join(splitName[:i], "/")
					break
				}
			}
			if len(instancePrefix) > 0 {
				instanceID := instancePrefix + "/instances/" + strings.ToLower(splitName[len(splitName)-1])
				instanceDocID := common.GenerateCombinedHashID(instanceID, common.GCP_INSTANCE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, instanceID, instanceDocID, common.GCP_INSTANCE_RESOURCE_TYPE)
			}
		}
	}
}

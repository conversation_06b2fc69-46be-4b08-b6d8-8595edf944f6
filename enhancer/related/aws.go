package related

import (
	"encoding/json"
	"path"
	"path/filepath"
	"regexp"
	"slices"
	"strings"

	"github.com/precize/common"
	"github.com/precize/enhancer/rcontext"
)

func processAWSEC2(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if subnetID, ok := entityJSONMap["subnetId"].(string); ok && len(subnetID) >= 0 {
		subnetDocID := common.GenerateCombinedHashID(subnetID, common.AWS_SUBNET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, subnetID, subnetDocID, common.AWS_SUBNET_RESOURCE_TYPE)
	}
	if vpcID, ok := entityJSONMap["vpcId"].(string); ok && len(vpcID) >= 0 {
		vpcDocID := common.GenerateCombinedHashID(vpcID, common.AWS_VPC_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, vpcID, vpcDocID, common.AWS_VPC_RESOURCE_TYPE)
	}
	if securityGroups, ok := entityJSONMap["securityGroups"].([]any); ok {
		for _, secGroup := range securityGroups {
			if sgID, ok := secGroup.(string); ok {
				sgDocID := common.GenerateCombinedHashID(sgID, common.AWS_SG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, sgID, sgDocID, common.AWS_SG_RESOURCE_TYPE)
			}
		}
	}
	if instance, ok := entityJSONMap["instance"].(map[string]any); ok {
		if blockDeviceMappings, ok := instance["blockDeviceMappings"].([]any); ok {
			for _, blockDeviceMapping := range blockDeviceMappings {
				if b, ok := blockDeviceMapping.(map[string]any); ok {
					if ebs, ok := b["ebs"].(map[string]any); ok {
						if ebsVolumeID, ok := ebs["volumeId"].(string); ok && len(ebsVolumeID) > 0 {
							ebsVolumeDocID := common.GenerateCombinedHashID(ebsVolumeID, common.AWS_EBSVOLUME_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
							AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, ebsVolumeID, ebsVolumeDocID, common.AWS_EBSVOLUME_RESOURCE_TYPE)
						}
					}
				}
			}
		}
		if networkInterfaces, ok := instance["networkInterfaces"].([]any); ok {
			for _, networkInterface := range networkInterfaces {
				if networkInterfaceMap, ok := networkInterface.(map[string]any); ok {
					if networkInterfaceID, ok := networkInterfaceMap["networkInterfaceId"].(string); ok && len(networkInterfaceID) > 0 {
						networkInterfaceDocID := common.GenerateCombinedHashID(networkInterfaceID, common.NETWORKINTERFACE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, networkInterfaceID, networkInterfaceDocID, common.NETWORKINTERFACE_RESOURCE_TYPE)
					}
				}
			}
		}
		if ipDetails, ok := entityJSONMap["publicIpAddress"].(string); ok && len(ipDetails) >= 0 {
			ipDetailsDocID := common.GenerateCombinedHashID(ipDetails, common.IPDETAILS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, ipDetails, ipDetailsDocID, common.IPDETAILS_RESOURCE_TYPE)
		}
	}
	if keyPairID, ok := entityJSONMap["keyPairId"].(string); ok && len(keyPairID) >= 0 {
		keyPairID = strings.ToLower(keyPairID)
		keyPairDocID := common.GenerateCombinedHashID(keyPairID, common.AWS_EC2KEYPAIR_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, keyPairID, keyPairDocID, common.AWS_EC2KEYPAIR_RESOURCE_TYPE, RelatedResourceOpts{Priority: true})
	}
	if amiID, ok := entityJSONMap["imageId"].(string); ok && len(amiID) >= 0 {
		amiID = strings.ToLower(amiID)
		amiDocID := common.GenerateCombinedHashID(amiID, common.AWS_AMI_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, amiID, amiDocID, common.AWS_AMI_RESOURCE_TYPE)
	}
	if clusterArn, ok := entityJSONMap["clusterArn"].(string); ok && len(clusterArn) >= 0 {
		cluster := strings.ToLower(clusterArn)
		clusterDocID := common.GenerateCombinedHashID(cluster, common.AWS_EKSCLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, cluster, clusterDocID, common.AWS_EKSCLUSTER_RESOURCE_TYPE)
	}
}

func processSubnetAndRouteTable(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if vpcID, ok := entityJSONMap["vpcId"].(string); ok && len(vpcID) >= 0 {
		vpcDocID := common.GenerateCombinedHashID(vpcID, common.AWS_VPC_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, vpcID, vpcDocID, common.AWS_VPC_RESOURCE_TYPE, RelatedResourceOpts{Parent: true})
	}
	if internetGateways, ok := entityJSONMap["gatewayIds"].([]any); ok {
		for _, internetGateway := range internetGateways {
			if internetGatewayID, ok := internetGateway.(string); ok && len(internetGatewayID) > 0 {
				if internetGatewayID != "local" {
					internetGatewayDocID := common.GenerateCombinedHashID(internetGatewayID, common.AWS_INTERNETGATEWAY_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, internetGatewayID, internetGatewayDocID, common.AWS_INTERNETGATEWAY_RESOURCE_TYPE)
				}
			}
		}
	}
}

func processAWSEBSSnapshot(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if ebsVolumeID, ok := entityJSONMap["volumeId"].(string); ok && len(ebsVolumeID) >= 0 {
		if ebsVolumeID == "vol-ffffffff" {
			if snapshotInfo, ok := entityJSONMap["snapshot"].(map[string]any); ok {
				if desc, ok := snapshotInfo["description"].(string); ok && len(desc) > 0 {
					regExpSnap := regexp.MustCompile(`snap-[a-zA-Z0-9]+`)
					snapshotID := regExpSnap.FindString(desc)
					if len(snapshotID) > 0 {
						snapshotDocID := common.GenerateCombinedHashID(snapshotID, common.AWS_EBSSNAPSHOT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, snapshotID, snapshotDocID, common.AWS_EBSSNAPSHOT_RESOURCE_TYPE, RelatedResourceOpts{Parent: true})
					}
				}
			}
		} else {
			ebsVolumeDocID := common.GenerateCombinedHashID(ebsVolumeID, common.AWS_EBSVOLUME_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, ebsVolumeID, ebsVolumeDocID, common.AWS_EBSVOLUME_RESOURCE_TYPE, RelatedResourceOpts{Parent: true})
		}
	}
	if kmsKeyID, ok := entityJSONMap["kmsKeyId"].(string); ok && len(kmsKeyID) > 0 {
		kmsKeyID = strings.ToLower(kmsKeyID)
		kmsKeyDocID := common.GenerateCombinedHashID(kmsKeyID, common.AWS_KMS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, kmsKeyID, kmsKeyDocID, common.AWS_KMS_RESOURCE_TYPE)
	}
}

func processAWSAMI(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if amiSnapshots, ok := entityJSONMap["snapshots"].([]any); ok {
		for _, amiSnapshot := range amiSnapshots {
			if snapshotID, ok := amiSnapshot.(string); ok && len(snapshotID) > 0 {
				snapshotDocID := common.GenerateCombinedHashID(snapshotID, common.AWS_EBSSNAPSHOT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, snapshotID, snapshotDocID, common.AWS_EBSSNAPSHOT_RESOURCE_TYPE, RelatedResourceOpts{Parent: true})
			}
		}
	}
}

func processAWSElasticIP(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if instanceID, ok := entityJSONMap["instanceId"].(string); ok && len(instanceID) >= 0 {
		instanceDocID := common.GenerateCombinedHashID(instanceID, common.AWS_EC2_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, instanceID, instanceDocID, common.AWS_EC2_RESOURCE_TYPE, RelatedResourceOpts{Parent: true})
	}
	if ipDetails, ok := entityJSONMap["publicIp"].(string); ok && len(ipDetails) >= 0 {
		ipDetailsDocID := common.GenerateCombinedHashID(ipDetails, common.IPDETAILS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, ipDetails, ipDetailsDocID, common.IPDETAILS_RESOURCE_TYPE)
	}
	if nwInterfaceID, ok := entityJSONMap["networkInterfaceId"].(string); ok && len(nwInterfaceID) >= 0 {
		nwInterfaceDocID := common.GenerateCombinedHashID(nwInterfaceID, common.NETWORKINTERFACE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, nwInterfaceID, nwInterfaceDocID, common.NETWORKINTERFACE_RESOURCE_TYPE)
	}
}

func processAWSS3(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if encryption, ok := entityJSONMap["encryption"].(map[string]any); ok {
		if serverSideEncryptionConfiguration, ok := encryption["serverSideEncryptionConfiguration"].(map[string]any); ok {
			if rules, ok := serverSideEncryptionConfiguration["rules"].([]any); ok {
				for _, rule := range rules {
					if ruleMap, ok := rule.(map[string]any); ok {
						if applyServerSideEncryptionByDefault, ok := ruleMap["applyServerSideEncryptionByDefault"].(map[string]any); ok {
							if kmsKeyID, ok := applyServerSideEncryptionByDefault["kmsmasterKeyID"].(string); ok && len(kmsKeyID) >= 0 {
								kmsKeyID = strings.ToLower(kmsKeyID)
								kmsKeyDocID := common.GenerateCombinedHashID(kmsKeyID, common.AWS_KMS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
								AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, kmsKeyID, kmsKeyDocID, common.AWS_KMS_RESOURCE_TYPE)
							}
						}
					}
				}
			}
		}
	}
}

func processAWSSQS(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if attributes, ok := entityJSONMap["attributes"].(map[string]any); ok {
		if kmsKeyID, ok := attributes["KmsMasterKeyId"].(string); ok && len(kmsKeyID) > 0 {
			kmsKeyID = strings.ToLower(kmsKeyID)
			kmsDocID := common.GenerateCombinedHashID(kmsKeyID, common.AWS_KMS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, kmsKeyID, kmsDocID, common.AWS_KMS_RESOURCE_TYPE)
		}
	}
}

func processAWSSNS(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if topicAttributes, ok := entityJSONMap["topicAttributes"].(map[string]any); ok {
		if kmsKeyID, ok := topicAttributes["KmsMasterKeyId"].(string); ok && len(kmsKeyID) > 0 {
			kmsKeyID = strings.ToLower(kmsKeyID)
			kmsDocID := common.GenerateCombinedHashID(kmsKeyID, common.AWS_KMS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, kmsKeyID, kmsDocID, common.AWS_KMS_RESOURCE_TYPE)
		}
	}
}

func processAWSRDS(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if vpcSecurityGroupIDs, ok := entityJSONMap["vpcSecurityGroupIds"].([]any); ok {
		for _, vpcSecurityGroupID := range vpcSecurityGroupIDs {
			if sgID, ok := vpcSecurityGroupID.(string); ok && len(sgID) > 0 {
				sgDocID := common.GenerateCombinedHashID(sgID, common.AWS_SG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, sgID, sgDocID, common.AWS_SG_RESOURCE_TYPE)
			}
		}
	}
	if dbInstance, ok := entityJSONMap["dbInstance"].(map[string]any); ok {
		if dbSubnetGroup, ok := dbInstance["dbsubnetGroup"].(map[string]any); ok {
			if vpcID, ok := dbSubnetGroup["vpcId"].(string); ok && len(vpcID) > 0 {
				vpcDocID := common.GenerateCombinedHashID(vpcID, common.AWS_VPC_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, vpcID, vpcDocID, common.AWS_VPC_RESOURCE_TYPE)
			}
			if dbSubnets, ok := dbSubnetGroup["subnets"].([]any); ok {
				for _, dbSubnet := range dbSubnets {
					if dbSubnetMap, ok := dbSubnet.(map[string]any); ok {
						if subnetID, ok := dbSubnetMap["subnetIdentifier"].(string); ok && len(subnetID) > 0 {
							subnetDocID := common.GenerateCombinedHashID(subnetID, common.AWS_SUBNET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
							AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, subnetID, subnetDocID, common.AWS_SUBNET_RESOURCE_TYPE)
						}
					}
				}
			}
		}
	}
	if dbInstance, ok := entityJSONMap["dbInstance"].(map[string]any); ok {
		if kmsKeyID, ok := dbInstance["kmsKeyId"].(string); ok && len(kmsKeyID) > 0 {
			kmsKeyID = strings.ToLower(kmsKeyID)
			kmsKeyDocID := common.GenerateCombinedHashID(kmsKeyID, common.AWS_KMS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, kmsKeyID, kmsKeyDocID, common.AWS_KMS_RESOURCE_TYPE)
		}
	}
}

func processAWSRDSSnapshot(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if dbInstanceID, ok := entityJSONMap["dbInstanceId"].(string); ok {
		dbInstanceID = strings.ToLower(dbInstanceID)
		if engine, ok := entityJSONMap["engine"].(string); ok && strings.Contains(engine, "aurora") {
			auroraDocID := common.GenerateCombinedHashID(dbInstanceID, common.AWS_AURORA_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, dbInstanceID, auroraDocID, common.AWS_AURORA_RESOURCE_TYPE, RelatedResourceOpts{Parent: true})
		} else {
			rdsDocID := common.GenerateCombinedHashID(dbInstanceID, common.AWS_RDS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, dbInstanceID, rdsDocID, common.AWS_RDS_RESOURCE_TYPE, RelatedResourceOpts{Parent: true})
		}
	}
}

func processAWSRDSClusterSnapshot(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if rdsClusterID, ok := entityJSONMap["dbClusterIdentifier"].(string); ok {
		rdsClusterID = strings.ToLower(rdsClusterID)
		rdsDocID := common.GenerateCombinedHashID(rdsClusterID, common.AWS_RDSCLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, rdsClusterID, rdsDocID, common.AWS_RDSCLUSTER_RESOURCE_TYPE, RelatedResourceOpts{Parent: true})
	}
}

func processAWSAurora(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if dbInstance, ok := entityJSONMap["dbInstance"].(map[string]any); ok {
		if kmsKeyID, ok := dbInstance["kmsKeyId"].(string); ok && len(kmsKeyID) > 0 {
			kmsKeyID = strings.ToLower(kmsKeyID)
			kmsKeyDocID := common.GenerateCombinedHashID(kmsKeyID, common.AWS_KMS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, kmsKeyID, kmsKeyDocID, common.AWS_KMS_RESOURCE_TYPE)
		}
	}
}

func processAWSLambda(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if functionConfig, ok := entityJSONMap["functionConfiguration"].(map[string]any); ok {
		if vpcConfig, ok := functionConfig["vpcConfig"].(map[string]any); ok {
			if subnetIDs, ok := vpcConfig["subnetIds"].([]any); ok {
				for _, subnetID := range subnetIDs {
					if subnetIDString, ok := subnetID.(string); ok && len(subnetIDString) > 0 {
						subnetDocID := common.GenerateCombinedHashID(subnetIDString, common.AWS_SUBNET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, subnetIDString, subnetDocID, common.AWS_SUBNET_RESOURCE_TYPE)
					}
				}
			}
			if securityGroupIDs, ok := vpcConfig["securityGroupIds"].([]any); ok {
				for _, securityGroupID := range securityGroupIDs {
					if securityGroupIDString, ok := securityGroupID.(string); ok && len(securityGroupIDString) > 0 {
						securityGroupDocID := common.GenerateCombinedHashID(securityGroupIDString, common.AWS_SG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, securityGroupIDString, securityGroupDocID, common.AWS_SG_RESOURCE_TYPE)
					}
				}
			}
			if vpcID, ok := vpcConfig["vpcId"].(string); ok && len(vpcID) > 0 {
				vpcDocID := common.GenerateCombinedHashID(vpcID, common.AWS_VPC_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, vpcID, vpcDocID, common.AWS_VPC_RESOURCE_TYPE)
			}
		}
		if roleArn, ok := functionConfig["role"].(string); ok && len(roleArn) > 0 {
			splitArn := strings.Split(roleArn, "/")
			role := strings.ToLower(splitArn[len(splitArn)-1])
			roleDocID := common.GenerateCombinedHashID(role, common.AWS_IAM_ROLE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, role, roleDocID, common.AWS_IAM_ROLE_RESOURCE_TYPE)
		}
		if kmsKeyArn, ok := functionConfig["kmskeyArn"].(string); ok && len(kmsKeyArn) > 0 {
			kmsKeyArn = strings.ToLower(kmsKeyArn)
			kmsDocID := common.GenerateCombinedHashID(kmsKeyArn, common.AWS_KMS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, kmsKeyArn, kmsDocID, common.AWS_KMS_RESOURCE_TYPE)
		}
	}
}

func processAWSAPIGateway(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if attachedLambdas, ok := entityJSONMap["attachedLambdas"].([]any); ok {
		for _, lambda := range attachedLambdas {
			if lambdaString, ok := lambda.(string); ok && len(lambdaString) > 0 {
				lambdaDocID := common.GenerateCombinedHashID(lambdaString, common.AWS_LAMBDA_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, lambdaString, lambdaDocID, common.AWS_LAMBDA_RESOURCE_TYPE)
			}
		}
	}
}

func processAWSContainerImage(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if repositoryID, ok := entityJSONMap["repositoryArn"].(string); ok && len(repositoryID) > 0 {
		repositoryID = strings.ToLower(repositoryID)
		repositoryDocID := common.GenerateCombinedHashID(repositoryID, common.AWS_CONTAINERREPOSITORY_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, repositoryID, repositoryDocID, common.AWS_CONTAINERREPOSITORY_RESOURCE_TYPE, RelatedResourceOpts{Parent: true, Oneway: true})
	}
}

func processAWSContainerRepository(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if repositoryUri, ok := entityJSONMap["repositoryUri"].(string); ok && len(repositoryUri) > 0 {
		registryID := strings.ToLower(path.Dir(repositoryUri))
		registryDocID := common.GenerateCombinedHashID(registryID, common.AWS_CONTAINERREGISTRY_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, registryID, registryDocID, common.AWS_CONTAINERREGISTRY_RESOURCE_TYPE, RelatedResourceOpts{Parent: true})
	}
}

func processAWSIAMRole(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if attachedPolicies, ok := entityJSONMap["attachedPolicies"].([]any); ok {
		for _, policy := range attachedPolicies {
			if policyMap, ok := policy.(map[string]any); ok {
				if policyArn, ok := policyMap["policyArn"].(string); ok && len(policyArn) > 0 {
					policyArnSplit := strings.Split(policyArn, "/")
					policyID := strings.ToLower(policyArnSplit[len(policyArnSplit)-1])
					policyDocID := common.GenerateCombinedHashID(policyID, common.AWS_IAMPOLICY_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, policyID, policyDocID, common.AWS_IAMPOLICY_RESOURCE_TYPE, RelatedResourceOpts{NonContextual: true})
				}
			}
		}
	}
}

func processAWSDynamoDB(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if tableDescription, ok := entityJSONMap["tableDescription"].(map[string]any); ok {
		if sseDesc, ok := tableDescription["ssedescription"].(map[string]any); ok {
			if kmsArn, ok := sseDesc["kmsmasterKeyArn"].(string); ok && len(kmsArn) > 0 {
				kmsArn = strings.ToLower(kmsArn)
				kmsDocID := common.GenerateCombinedHashID(kmsArn, common.AWS_KMS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, kmsArn, kmsDocID, common.AWS_KMS_RESOURCE_TYPE)
			}
		}
	}
}

func processAWSAthanaWorkgroup(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if workgroupDetails, ok := entityJSONMap["workgroupDetails"].(map[string]any); ok {
		if configuration, ok := workgroupDetails["configuration"].(map[string]any); ok {
			if resultConfiguration, ok := configuration["resultConfiguration"].(map[string]any); ok {
				if outputLocation, ok := resultConfiguration["outputLocation"].(string); ok && len(outputLocation) > 0 {
					outputLocation = strings.TrimPrefix(outputLocation, "s3://")
					bucket := strings.ToLower(outputLocation)
					s3Doc, err := common.GetCloudResourceDocumentForEntityIDAndType(bucket, common.AWS_S3_RESOURCE_TYPE, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
					if err != nil {
						return
					}
					if accountID, ok := s3Doc["accountId"].(string); ok && len(accountID) > 0 {
						bucketDocID := common.GenerateCombinedHashID(bucket, common.AWS_S3_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, bucket, bucketDocID, common.AWS_S3_RESOURCE_TYPE)
					}
				}
			}
			if executionRole, ok := configuration["executionRole"].(string); ok && len(executionRole) > 0 {
				roleParts := strings.Split(executionRole, "/")
				role := strings.ToLower(roleParts[len(roleParts)-1])
				roleDocID := common.GenerateCombinedHashID(role, common.AWS_IAM_ROLE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, role, roleDocID, common.AWS_IAM_ROLE_RESOURCE_TYPE)
			}
		}
	}
}

func processAWSSageMakerEndpointConfig(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if s3Buckets, ok := entityJSONMap["s3bucketNames"].([]any); ok {
		for _, s3Bucket := range s3Buckets {
			if bucket, ok := s3Bucket.(string); ok && len(bucket) > 0 {
				bucket = strings.ToLower(bucket)
				s3Doc, err := common.GetCloudResourceDocumentForEntityIDAndType(bucket, common.AWS_S3_RESOURCE_TYPE, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
				if err != nil {
					return
				}
				if accountID, ok := s3Doc["accountId"].(string); ok && len(accountID) > 0 {
					bucketDocID := common.GenerateCombinedHashID(bucket, common.AWS_S3_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, bucket, bucketDocID, common.AWS_S3_RESOURCE_TYPE)
				}
			}
		}
	}
	if endpoints, ok := entityJSONMap["endpoints"].([]any); ok {
		for _, endpoint := range endpoints {
			if endpointID, ok := endpoint.(string); ok && len(endpointID) > 0 {
				endpointID = strings.ToLower(endpointID)
				endpointDocID := common.GenerateCombinedHashID(endpointID, common.AWS_SAGEMAKERENDPOINT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, endpointID, endpointDocID, common.AWS_SAGEMAKERENDPOINT_RESOURCE_TYPE)
			}
		}
	}
	if tags, ok := entityJSONMap["tags"].([]any); ok {
		for _, tag := range tags {
			if tagMap, ok := tag.(map[string]any); ok {
				if tagKey, ok := tagMap["key"].(string); ok && len(tagKey) > 0 {
					if tagValue, ok := tagMap["value"].(string); ok && len(tagValue) > 0 {
						if tagKey == "sagemaker:domain-arn" {
							parts := strings.Split(tagValue, "/")
							sagemakerDomainId := strings.ToLower(parts[len(parts)-1])
							sagemakerDomainDocID := common.GenerateCombinedHashID(sagemakerDomainId, common.AWS_AISAGEMAKERDOMAIN_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
							AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, sagemakerDomainId, sagemakerDomainDocID, common.AWS_AISAGEMAKERDOMAIN_RESOURCE_TYPE)
						}
						if tagKey == "sagemaker:space-arn" {
							parts := strings.Split(tagValue, "/")
							sagemakerNotebookInstanceId := strings.ToLower(parts[len(parts)-1])
							sagemakerNotebookInstanceDocId := common.GenerateCombinedHashID(sagemakerNotebookInstanceId, common.AWS_AISAGEMAKERNOTEBOOKINSTANCE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
							AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, sagemakerNotebookInstanceId, sagemakerNotebookInstanceDocId, common.AWS_AISAGEMAKERNOTEBOOKINSTANCE_RESOURCE_TYPE)
						}
					}
				}
			}
		}
	}
}

func processAWSSageMakerEndpoint(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if tags, ok := entityJSONMap["tags"].([]any); ok {
		for _, tag := range tags {
			if tagMap, ok := tag.(map[string]any); ok {
				if tagKey, ok := tagMap["key"].(string); ok && len(tagKey) > 0 {
					if tagValue, ok := tagMap["value"].(string); ok && len(tagValue) > 0 {
						if tagKey == "sagemaker:domain-arn" {
							parts := strings.Split(tagValue, "/")
							sagemakerDomainId := strings.ToLower(parts[len(parts)-1])
							sagemakerDomainDocID := common.GenerateCombinedHashID(sagemakerDomainId, common.AWS_AISAGEMAKERDOMAIN_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
							AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, sagemakerDomainId, sagemakerDomainDocID, common.AWS_AISAGEMAKERDOMAIN_RESOURCE_TYPE)
						}
						if tagKey == "sagemaker:space-arn" {
							parts := strings.Split(tagValue, "/")
							sagemakerNotebookInstanceId := strings.ToLower(parts[len(parts)-1])
							sagemakerNotebookInstanceDocId := common.GenerateCombinedHashID(sagemakerNotebookInstanceId, common.AWS_AISAGEMAKERNOTEBOOKINSTANCE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
							AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, sagemakerNotebookInstanceId, sagemakerNotebookInstanceDocId, common.AWS_AISAGEMAKERNOTEBOOKINSTANCE_RESOURCE_TYPE)
						}
					}
				}
			}
		}
	}
}

func processAWSSageMakerTrainingJob(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if roleArn, ok := entityJSONMap["roleArn"].(string); ok && len(roleArn) > 0 {
		splitArn := strings.Split(roleArn, "/")
		role := strings.ToLower(splitArn[len(splitArn)-1])
		roleDocID := common.GenerateCombinedHashID(role, common.AWS_IAM_ROLE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, role, roleDocID, common.AWS_IAM_ROLE_RESOURCE_TYPE)
	}

	s3BucketIds := []string{}
	if s3Buckets, ok := entityJSONMap["inputS3Uris"].([]any); ok {
		for _, s3Bucket := range s3Buckets {
			if bucket, ok := s3Bucket.(string); ok && len(bucket) > 0 {
				bucket = strings.ToLower(bucket)
				if strings.Contains(bucket, "s3://") {
					bucket = strings.TrimPrefix(bucket, "s3://")
					bucket = strings.Split(bucket, "/")[0]
				}

				if !slices.Contains(s3BucketIds, bucket) {
					s3BucketIds = append(s3BucketIds, bucket)
				}
			}
		}
	}

	s3Docs, err := common.GetCloudResourceDocumentFromListofEntIds(s3BucketIds, common.AWS_S3_RESOURCE_TYPE, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
	if err != nil {
		return
	}

	for _, s3Doc := range s3Docs {
		if accountID, ok := s3Doc["accountId"].(string); ok && len(accountID) > 0 {
			if bucket, ok := s3Doc["entityId"].(string); ok && len(bucket) > 0 {
				bucketDocID := common.GenerateCombinedHashID(bucket, common.AWS_S3_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, bucket, bucketDocID, common.AWS_S3_RESOURCE_TYPE)
			}
		}
	}

	if containerImageTags, ok := entityJSONMap["containerImageTags"].([]any); ok {
		for _, containerImageTag := range containerImageTags {
			if imageTag, ok := containerImageTag.(string); ok && len(imageTag) > 0 {
				imageTag = strings.ToLower(imageTag)
				repo := path.Dir(imageTag)
				tag := path.Base(imageTag)
				imageDocs, err := common.GetCloudResourceDocumentFromEntityAndJSON(repo, common.AWS_CONTAINERIMAGE_RESOURCE_TYPE, tag, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
				if err != nil {
					return
				}
				for _, imageDoc := range imageDocs {
					if imageEntityJSONString, ok := imageDoc["entityJson"].(string); ok && len(imageEntityJSONString) > 0 {
						imageEntityJSONMap := make(map[string]any)
						if err = json.Unmarshal([]byte(imageEntityJSONString), &imageEntityJSONMap); err != nil {
							continue
						}
						if imageTags, ok := imageEntityJSONMap["imageTags"].(string); ok && len(imageTags) > 0 {
							if strings.Contains(imageTags, tag) {
								if accountID, ok := imageDoc["accountId"].(string); ok && len(accountID) > 0 {
									if containerImageId, ok := imageDoc["entityId"].(string); ok {
										imageDocID := common.GenerateCombinedHashID(containerImageId, common.AWS_CONTAINERIMAGE_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
										AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, containerImageId, imageDocID, common.AWS_CONTAINERIMAGE_RESOURCE_TYPE)
									}
								}
								break
							}
						}
					}
				}
			}
		}
	}
	if containerImageDigests, ok := entityJSONMap["containerImageDigests"].([]any); ok {
		for _, containerImageDigest := range containerImageDigests {
			if digest, ok := containerImageDigest.(string); ok && len(digest) > 0 {
				digest = strings.ToLower(digest)
				digestDoc, err := common.GetCloudResourceDocumentForEntityIDAndType(digest, common.AWS_CONTAINERIMAGE_RESOURCE_TYPE, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
				if err != nil {
					return
				}
				if accountID, ok := digestDoc["accountId"].(string); ok && len(accountID) > 0 {
					digestDocID := common.GenerateCombinedHashID(digest, common.AWS_CONTAINERIMAGE_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, digest, digestDocID, common.AWS_CONTAINERIMAGE_RESOURCE_TYPE)
				}
			}
		}
	}
	if artifacts, ok := entityJSONMap["artifactIds"].([]any); ok {
		for _, artifact := range artifacts {
			if artifactID, ok := artifact.(string); ok && len(artifactID) > 0 {
				artifactID = strings.ToLower(artifactID)
				artifactDocID := common.GenerateCombinedHashID(artifactID, common.AWS_SAGEMAKERARTIFACT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, artifactID, artifactDocID, common.AWS_SAGEMAKERARTIFACT_RESOURCE_TYPE)
			}
		}
	}
}

func processAWSSageMakerModel(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if roleArn, ok := entityJSONMap["roleArn"].(string); ok && len(roleArn) > 0 {
		splitArn := strings.Split(roleArn, "/")
		role := strings.ToLower(splitArn[len(splitArn)-1])
		roleDocID := common.GenerateCombinedHashID(role, common.AWS_IAM_ROLE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, role, roleDocID, common.AWS_IAM_ROLE_RESOURCE_TYPE)
	}
	if s3Buckets, ok := entityJSONMap["s3bucketNames"].([]any); ok {
		for _, s3Bucket := range s3Buckets {
			if bucket, ok := s3Bucket.(string); ok && len(bucket) > 0 {
				bucket = strings.ToLower(bucket)
				s3Doc, err := common.GetCloudResourceDocumentForEntityIDAndType(bucket, common.AWS_S3_RESOURCE_TYPE, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
				if err != nil {
					return
				}
				if accountID, ok := s3Doc["accountId"].(string); ok && len(accountID) > 0 {
					bucketDocID := common.GenerateCombinedHashID(bucket, common.AWS_S3_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, bucket, bucketDocID, common.AWS_S3_RESOURCE_TYPE)
				}
			}
		}
	}
	if containerImageTags, ok := entityJSONMap["containerImageTags"].([]any); ok {
		for _, containerImageTag := range containerImageTags {
			if imageTag, ok := containerImageTag.(string); ok && len(imageTag) > 0 {
				imageTag = strings.ToLower(imageTag)
				repo := path.Dir(imageTag)
				tag := path.Base(imageTag)
				imageDocs, err := common.GetCloudResourceDocumentFromEntityAndJSON(repo, common.AWS_CONTAINERIMAGE_RESOURCE_TYPE, tag, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
				if err != nil {
					return
				}
				for _, imageDoc := range imageDocs {
					if imageEntityJSONString, ok := imageDoc["entityJson"].(string); ok && len(imageEntityJSONString) > 0 {
						imageEntityJSONMap := make(map[string]any)
						if err = json.Unmarshal([]byte(imageEntityJSONString), &imageEntityJSONMap); err != nil {
							continue
						}
						if imageTags, ok := imageEntityJSONMap["imageTags"].(string); ok && len(imageTags) > 0 {
							if strings.Contains(imageTags, tag) {
								if accountID, ok := imageDoc["accountId"].(string); ok && len(accountID) > 0 {
									if containerImageId, ok := imageDoc["entityId"].(string); ok {
										imageDocID := common.GenerateCombinedHashID(containerImageId, common.AWS_CONTAINERIMAGE_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
										AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, containerImageId, imageDocID, common.AWS_CONTAINERIMAGE_RESOURCE_TYPE)
									}
								}
								break
							}
						}
					}
				}
			}
		}
	}
	if containerImageDigests, ok := entityJSONMap["containerImageDigests"].([]any); ok {
		for _, containerImageDigest := range containerImageDigests {
			if digest, ok := containerImageDigest.(string); ok && len(digest) > 0 {
				digest = strings.ToLower(digest)
				digestDoc, err := common.GetCloudResourceDocumentForEntityIDAndType(digest, common.AWS_CONTAINERIMAGE_RESOURCE_TYPE, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
				if err != nil {
					return
				}
				if accountID, ok := digestDoc["accountId"].(string); ok && len(accountID) > 0 {
					digestDocID := common.GenerateCombinedHashID(digest, common.AWS_CONTAINERIMAGE_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, digest, digestDocID, common.AWS_CONTAINERIMAGE_RESOURCE_TYPE)
				}
			}
		}
	}
	if trainingJob, ok := entityJSONMap["trainingJob"].(string); ok && len(trainingJob) >= 0 {
		trainingJob = strings.ToLower(trainingJob)
		trainingJobDocId := common.GenerateCombinedHashID(trainingJob, common.AWS_SAGEMAKERTRAININGJOB_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, trainingJob, trainingJobDocId, common.AWS_SAGEMAKERTRAININGJOB_RESOURCE_TYPE)
	}
	if endpointConfigs, ok := entityJSONMap["endpointConfigurations"].([]any); ok {
		for _, endpointConfig := range endpointConfigs {
			if endpointConfigId, ok := endpointConfig.(string); ok && len(endpointConfigId) > 0 {
				endpointConfigId = strings.ToLower(endpointConfigId)
				endpointConfigDocID := common.GenerateCombinedHashID(endpointConfigId, common.AWS_SAGEMAKERENDPOINTCONFIG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, endpointConfigId, endpointConfigDocID, common.AWS_SAGEMAKERENDPOINTCONFIG_RESOURCE_TYPE)
			}
		}
	}
	if tags, ok := entityJSONMap["tags"].([]any); ok {
		for _, tag := range tags {
			if tagMap, ok := tag.(map[string]any); ok {
				if tagKey, ok := tagMap["key"].(string); ok && len(tagKey) > 0 {
					if tagValue, ok := tagMap["value"].(string); ok && len(tagValue) > 0 {
						if tagKey == "sagemaker:domain-arn" {
							parts := strings.Split(tagValue, "/")
							sagemakerDomainId := strings.ToLower(parts[len(parts)-1])
							sagemakerDomainDocID := common.GenerateCombinedHashID(sagemakerDomainId, common.AWS_AISAGEMAKERDOMAIN_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
							AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, sagemakerDomainId, sagemakerDomainDocID, common.AWS_AISAGEMAKERDOMAIN_RESOURCE_TYPE)
						}
						if tagKey == "sagemaker:space-arn" {
							parts := strings.Split(tagValue, "/")
							sagemakerNotebookInstanceId := strings.ToLower(parts[len(parts)-1])
							sagemakerNotebookInstanceDocId := common.GenerateCombinedHashID(sagemakerNotebookInstanceId, common.AWS_AISAGEMAKERNOTEBOOKINSTANCE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
							AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, sagemakerNotebookInstanceId, sagemakerNotebookInstanceDocId, common.AWS_AISAGEMAKERNOTEBOOKINSTANCE_RESOURCE_TYPE)
						}
					}
				}
			}
		}
	}
	if endpoints, ok := entityJSONMap["endpoints"].([]any); ok {
		for _, endpoint := range endpoints {
			if endpointID, ok := endpoint.(string); ok && len(endpointID) > 0 {
				endpointID = strings.ToLower(endpointID)
				endpointDocID := common.GenerateCombinedHashID(endpointID, common.AWS_SAGEMAKERENDPOINT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, endpointID, endpointDocID, common.AWS_SAGEMAKERENDPOINT_RESOURCE_TYPE)
			}
		}
	}
}

func processBedrockPromptAndEvalJob(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if foundationModelIDs, ok := entityJSONMap["foundationModelIds"].([]any); ok {
		for _, foundationModelID := range foundationModelIDs {
			if bedrockModel, ok := foundationModelID.(string); ok && len(bedrockModel) > 0 {
				bedrockModel = strings.ToLower(bedrockModel)
				bedrockModelDocID := common.GenerateCombinedHashID(bedrockModel, common.AWS_BEDROCKMODEL_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, bedrockModel, bedrockModelDocID, common.AWS_BEDROCKMODEL_RESOURCE_TYPE)
			}
		}
	}
}

func processBedrockAgent(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if bedrockModel, ok := entityJSONMap["foundationModelId"].(string); ok && len(bedrockModel) > 0 {
		bedrockModel = strings.ToLower(bedrockModel)
		bedrockModelDocID := common.GenerateCombinedHashID(bedrockModel, common.AWS_BEDROCKMODEL_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, bedrockModel, bedrockModelDocID, common.AWS_BEDROCKMODEL_RESOURCE_TYPE)
	}
	if bedrockGuardrailConfig, ok := entityJSONMap["guardrailConfiguration"].(map[string]any); ok {
		if bedrockGuardrailId, ok := bedrockGuardrailConfig["guardrailIdentifier"].(string); ok && len(bedrockGuardrailId) > 0 {
			bedrockGuardrailId = strings.ToLower(bedrockGuardrailId)
			bedrockGuardrailDocId := common.GenerateCombinedHashID(bedrockGuardrailId, common.AWS_BEDROCKGUARDRAIL_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, bedrockGuardrailId, bedrockGuardrailDocId, common.AWS_BEDROCKGUARDRAIL_RESOURCE_TYPE)
		}
	}
	if knowledgeBaseIDs, ok := entityJSONMap["knowledgeBaseIds"].([]any); ok {
		for _, knowledgeBase := range knowledgeBaseIDs {
			if knowledgeBaseID, ok := knowledgeBase.(string); ok && len(knowledgeBaseID) > 0 {
				knowledgeBaseID = strings.ToLower(knowledgeBaseID)
				knowledgeBaseIDDocID := common.GenerateCombinedHashID(knowledgeBaseID, common.AWS_BEDROCKKNOWLEDGEBASE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, knowledgeBaseID, knowledgeBaseIDDocID, common.AWS_BEDROCKKNOWLEDGEBASE_RESOURCE_TYPE)
			}
		}
	}
}

func processBedrockProvModelThroughput(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if bedrockModel, ok := entityJSONMap["foundationModelId"].(string); ok && len(bedrockModel) > 0 {
		bedrockModel = strings.ToLower(bedrockModel)
		bedrockModelDocID := common.GenerateCombinedHashID(bedrockModel, common.AWS_BEDROCKMODEL_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, bedrockModel, bedrockModelDocID, common.AWS_BEDROCKMODEL_RESOURCE_TYPE)
	}
}

func processBedrockKnowledgeBase(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if modelIDs, ok := entityJSONMap["modelIds"].([]any); ok {
		for _, foundationModelID := range modelIDs {
			if bedrockModel, ok := foundationModelID.(string); ok && len(bedrockModel) > 0 {
				bedrockModel = strings.ToLower(bedrockModel)
				bedrockModelDocID := common.GenerateCombinedHashID(bedrockModel, common.AWS_BEDROCKMODEL_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, bedrockModel, bedrockModelDocID, common.AWS_BEDROCKMODEL_RESOURCE_TYPE)
			}
		}
	}
	if roleArn, ok := entityJSONMap["roleArn"].(string); ok && len(roleArn) > 0 {
		splitArn := strings.Split(roleArn, "/")
		role := strings.ToLower(splitArn[len(splitArn)-1])
		roleDocID := common.GenerateCombinedHashID(role, common.AWS_IAM_ROLE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, role, roleDocID, common.AWS_IAM_ROLE_RESOURCE_TYPE)
	}
	if storageConfiguration, ok := entityJSONMap["storageConfiguration"].(map[string]any); ok {
		if opensearchConfig, ok := storageConfiguration["opensearchServerlessConfiguration"].(map[string]any); ok {
			if collectionArn, ok := opensearchConfig["collectionArn"].(string); ok && len(collectionArn) > 0 {
				splitCollectionArn := strings.Split(collectionArn, "/")
				opensearchID := splitCollectionArn[len(splitCollectionArn)-1]
				opensearchID = strings.ToLower(opensearchID)
				openSearchDocID := common.GenerateCombinedHashID(opensearchID, common.AWS_OPENSEARCHSERVERLESSCOLLECTION_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, opensearchID, openSearchDocID, common.AWS_OPENSEARCHSERVERLESSCOLLECTION_RESOURCE_TYPE)
			}
		}
	}
}

func processBedrockAgentAliases(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if agentID, ok := entityJSONMap["agentId"].(string); ok && len(agentID) > 0 {
		agentID = strings.ToLower(agentID)
		agentDocID := common.GenerateCombinedHashID(agentID, common.AWS_BEDROCKAGENT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, agentID, agentDocID, common.AWS_BEDROCKAGENT_RESOURCE_TYPE)
	}
}

func processKnowledgeBaseDataSource(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if knowledgeBaseID, ok := entityJSONMap["knowledgeBaseId"].(string); ok && len(knowledgeBaseID) > 0 {
		knowledgeBaseID = strings.ToLower(knowledgeBaseID)
		agentDocID := common.GenerateCombinedHashID(knowledgeBaseID, common.AWS_BEDROCKKNOWLEDGEBASE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, knowledgeBaseID, agentDocID, common.AWS_BEDROCKKNOWLEDGEBASE_RESOURCE_TYPE)
	}
	if s3Buckets, ok := entityJSONMap["s3BucketNames"].([]any); ok {
		for _, s3Bucket := range s3Buckets {
			if bucket, ok := s3Bucket.(string); ok && len(bucket) > 0 {
				bucket = strings.ToLower(bucket)
				s3Doc, err := common.GetCloudResourceDocumentForEntityIDAndType(bucket, common.AWS_S3_RESOURCE_TYPE, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
				if err != nil {
					return
				}
				if accountID, ok := s3Doc["accountId"].(string); ok && len(accountID) > 0 {
					bucketDocID := common.GenerateCombinedHashID(bucket, common.AWS_S3_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, bucket, bucketDocID, common.AWS_S3_RESOURCE_TYPE)
				}
			}
		}
	}
}

func processAWSNetworkACL(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if vpcID, ok := entityJSONMap["vpcId"].(string); ok && len(vpcID) >= 0 {
		vpcDocID := common.GenerateCombinedHashID(vpcID, common.AWS_VPC_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, vpcID, vpcDocID, common.AWS_VPC_RESOURCE_TYPE, RelatedResourceOpts{Parent: true, Oneway: true})
	}
	if subnets, ok := entityJSONMap["subnetIds"].([]any); ok {
		for _, subnet := range subnets {
			if subnetID, ok := subnet.(string); ok && len(subnetID) > 0 {
				subnetDocID := common.GenerateCombinedHashID(subnetID, common.AWS_SUBNET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, subnetID, subnetDocID, common.AWS_SUBNET_RESOURCE_TYPE)
			}
		}
	}
}

func processAWSNetworkInsightsPath(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if networkInterfaces, ok := entityJSONMap["networkInterfaces"].([]any); ok {
		for _, networkInterface := range networkInterfaces {
			if networkInterfaceID, ok := networkInterface.(string); ok && len(networkInterfaceID) > 0 {
				networkInterfaceDocID := common.GenerateCombinedHashID(networkInterfaceID, common.NETWORKINTERFACE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, networkInterfaceID, networkInterfaceDocID, common.NETWORKINTERFACE_RESOURCE_TYPE)
			}
		}
	}
	if ec2Instances, ok := entityJSONMap["ec2Instances"].([]any); ok {
		for _, ec2Instance := range ec2Instances {
			if ec2InstanceID, ok := ec2Instance.(string); ok && len(ec2InstanceID) > 0 {
				ec2InstanceDocID := common.GenerateCombinedHashID(ec2InstanceID, common.AWS_EC2_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, ec2InstanceID, ec2InstanceDocID, common.AWS_EC2_RESOURCE_TYPE)
			}
		}
	}
	if internetGateways, ok := entityJSONMap["internetGateways"].([]any); ok {
		for _, internetGateway := range internetGateways {
			if internetGatewayID, ok := internetGateway.(string); ok && len(internetGatewayID) > 0 {
				internetGatewayDocID := common.GenerateCombinedHashID(internetGatewayID, common.AWS_INTERNETGATEWAY_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, internetGatewayID, internetGatewayDocID, common.AWS_INTERNETGATEWAY_RESOURCE_TYPE)
			}
		}
	}
}

func processAWSEKSCluster(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if vpcConfig, ok := entityJSONMap["associateVPCConfig"].(map[string]any); ok {
		if subnets, ok := vpcConfig["subnetIds"].([]any); ok {
			for _, subnet := range subnets {
				if subnetID, ok := subnet.(string); ok && len(subnetID) > 0 {
					subnetDocID := common.GenerateCombinedHashID(subnetID, common.AWS_SUBNET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, subnetID, subnetDocID, common.AWS_SUBNET_RESOURCE_TYPE)
				}
			}
		}
		if secGroups, ok := vpcConfig["securityGroupIds"].([]any); ok {
			for _, secGroup := range secGroups {
				if secGroupID, ok := secGroup.(string); ok && len(secGroupID) > 0 {
					secGroupDocID := common.GenerateCombinedHashID(secGroupID, common.AWS_SG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, secGroupID, secGroupDocID, common.AWS_SG_RESOURCE_TYPE)
				}
			}
		}
		if clusterSecGroupID, ok := vpcConfig["clusterSecurityGroupId"].(string); ok && len(clusterSecGroupID) > 0 {
			clusterSecGroupDocID := common.GenerateCombinedHashID(clusterSecGroupID, common.AWS_SG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, clusterSecGroupID, clusterSecGroupDocID, common.AWS_SG_RESOURCE_TYPE)
		}
		if vpcID, ok := vpcConfig["vpcId"].(string); ok && len(vpcID) > 0 {
			vpcDocID := common.GenerateCombinedHashID(vpcID, common.AWS_VPC_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, vpcID, vpcDocID, common.AWS_VPC_RESOURCE_TYPE)
		}
	}
	if encryptionConfigs, ok := entityJSONMap["encryptionConfigs"].([]any); ok {
		for _, config := range encryptionConfigs {
			if configMap, ok := config.(map[string]any); ok {
				if provider, ok := configMap["provider"].(map[string]any); ok {
					if keyAID, ok := provider["keyArn"].(string); ok && len(keyAID) > 0 {
						keyAID = strings.ToLower(keyAID)
						kmsKeyDocID := common.GenerateCombinedHashID(keyAID, common.AWS_KMS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, keyAID, kmsKeyDocID, common.AWS_KMS_RESOURCE_TYPE)
					}
				}
			}
		}
	}
	ecrImages := make([]any, 0)

	if podDetails, ok := entityJSONMap["podDetails"].([]any); ok {
		for _, podDetail := range podDetails {
			if podDetailMap, ok := podDetail.(map[string]any); ok {
				if eImages, ok := podDetailMap["images"].([]any); ok {
					ecrImages = append(ecrImages, eImages...)
				}
			}
		}
	}

	if len(ecrImages) <= 0 {
		if eImages, ok := entityJSONMap["ecrImages"].([]any); ok {
			ecrImages = append(ecrImages, eImages...)
		}
	}

	for _, ecrImage := range ecrImages {
		if ecrImageID, ok := ecrImage.(string); ok && len(ecrImageID) > 0 {

			if strings.Contains(ecrImageID, "sha256:") {

				// Image can be from any account
				imageDoc, err := common.GetCloudResourceDocumentForEntityIDAndType(ecrImageID, common.AWS_CONTAINERIMAGE_RESOURCE_TYPE, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
				if err != nil {
					continue
				}
				if accountID, ok := imageDoc["accountId"].(string); ok && len(accountID) > 0 {
					ecrImageDocID := common.GenerateCombinedHashID(ecrImageID, common.AWS_CONTAINERIMAGE_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						ecrImageID, ecrImageDocID, common.AWS_CONTAINERIMAGE_RESOURCE_TYPE)
				}
			} else {

				repoImageCombination, account, region, err := common.ParseECRArn(ecrImageID)
				if err != nil {
					continue
				}

				imageDoc, err := common.FetchECRImageDocFromArnUtils(repoImageCombination, account, region, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
				if err != nil {
					continue
				}

				if ecrImageEntityId, ok := imageDoc["entityId"].(string); ok && len(ecrImageEntityId) > 0 {
					if accountID, ok := imageDoc["accountId"].(string); ok && len(accountID) > 0 {
						ecrImageDocID := common.GenerateCombinedHashID(ecrImageEntityId, common.AWS_CONTAINERIMAGE_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							ecrImageEntityId, ecrImageDocID, common.AWS_CONTAINERIMAGE_RESOURCE_TYPE)
					}
				}
			}

			imageDetails := filepath.Base(ecrImageID)
			if imageSplit := strings.Split(imageDetails, ":"); len(imageSplit) > 1 {
				repoName := imageSplit[0]

				if accountID, region, err := common.GetAccountIDAndRegionFromARN(ecrImageID); err == nil && len(accountID) > 0 && len(region) > 0 {

					ecrRepoID := strings.ToLower("arn:aws:ecr:" + region + ":" + accountID + ":repository/" + repoName)
					ecrRepoDocID := common.GenerateCombinedHashID(ecrRepoID, common.AWS_CONTAINERREPOSITORY_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						ecrRepoID, ecrRepoDocID, common.AWS_CONTAINERREPOSITORY_RESOURCE_TYPE)
				}
			}
		}
	}
}

func processAWSElasticsearch(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if domainConfig, ok := entityJSONMap["domainConfig"].(map[string]any); ok {
		if vpcOptions, ok := domainConfig["vpcoptions"].(map[string]any); ok {
			if options, ok := vpcOptions["options"].(map[string]any); ok {
				if subnets, ok := options["subnetIds"].([]any); ok {
					for _, subnet := range subnets {
						if subnetID, ok := subnet.(string); ok && len(subnetID) > 0 {
							subnetDocID := common.GenerateCombinedHashID(subnetID, common.AWS_SUBNET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
							AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, subnetID, subnetDocID, common.AWS_SUBNET_RESOURCE_TYPE)
						}
					}
				}
				if secGroups, ok := options["securityGroupIds"].([]any); ok {
					for _, secGroup := range secGroups {
						if secGroupID, ok := secGroup.(string); ok && len(secGroupID) > 0 {
							secGroupDocID := common.GenerateCombinedHashID(secGroupID, common.AWS_SG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
							AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, secGroupID, secGroupDocID, common.AWS_SG_RESOURCE_TYPE)
						}
					}
				}
				if vpcID, ok := options["vpcid"].(string); ok && len(vpcID) > 0 {
					vpcDocID := common.GenerateCombinedHashID(vpcID, common.AWS_VPC_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, vpcID, vpcDocID, common.AWS_VPC_RESOURCE_TYPE)
				}
			}
		}
	}
}

func processAWSComprehendAndSageMakerLabeling(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if s3Buckets, ok := entityJSONMap["s3BucketNames"].([]any); ok {
		for _, s3Bucket := range s3Buckets {
			if s3BucketID, ok := s3Bucket.(string); ok && len(s3BucketID) > 0 {
				s3BucketDocID, err := common.GetCloudResourceDocumentForEntityIDAndType(s3BucketID, common.AWS_S3_RESOURCE_TYPE, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
				if err != nil {
					return
				}
				if accountID, ok := s3BucketDocID["accountId"].(string); ok && len(accountID) > 0 {
					bucketDocID := common.GenerateCombinedHashID(s3BucketID, common.AWS_S3_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, s3BucketID, bucketDocID, common.AWS_S3_RESOURCE_TYPE)
				}
			}
		}
	}
}

func processAWSSageMakerArtifact(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if s3BucketID, ok := entityJSONMap["bucketName"].(string); ok && len(s3BucketID) > 0 {
		s3BucketDocID, err := common.GetCloudResourceDocumentForEntityIDAndType(s3BucketID, common.AWS_S3_RESOURCE_TYPE, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
		if err != nil {
			return
		}
		if accountID, ok := s3BucketDocID["accountId"].(string); ok && len(accountID) > 0 {
			bucketDocID := common.GenerateCombinedHashID(s3BucketID, common.AWS_S3_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, s3BucketID, bucketDocID, common.AWS_S3_RESOURCE_TYPE)
		}
	}
}

func processBedrockAgentGroup(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if bedrockAgent, ok := entityJSONMap["agentId"].(string); ok && len(bedrockAgent) > 0 {
		bedrockAgentID := strings.ToLower(bedrockAgent)
		bedrockAgentDocID := common.GenerateCombinedHashID(bedrockAgentID, common.AWS_BEDROCKAGENT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, bedrockAgentID, bedrockAgentDocID, common.AWS_BEDROCKAGENT_RESOURCE_TYPE)
	}
}

func processAWSSSMInstance(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if instanceID, ok := entityJSONMap["sourceId"].(string); ok {
		instanceDocID := common.GenerateCombinedHashID(instanceID, common.AWS_EC2_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, instanceID, instanceDocID, common.AWS_EC2_RESOURCE_TYPE, RelatedResourceOpts{Parent: true})
	}
}

func processAWSGlueCatalogTable(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if databaseName, ok := entityJSONMap["databaseName"].(string); ok {
		databaseDocID := common.GenerateCombinedHashID(databaseName, common.AWS_GLUECATALOGDATABASE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, databaseName, databaseDocID, common.AWS_GLUECATALOGDATABASE_RESOURCE_TYPE, RelatedResourceOpts{Parent: true})
	}
	if s3buckets, ok := entityJSONMap["s3BucketNames"].([]any); ok {
		for _, s3bucket := range s3buckets {
			if s3bucket, ok := s3bucket.(string); ok {
				s3bucket = strings.ToLower(s3bucket)
				s3Doc, err := common.GetCloudResourceDocumentForEntityIDAndType(s3bucket, common.AWS_S3_RESOURCE_TYPE, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
				if err != nil {
					return
				}
				if accountID, ok := s3Doc["accountId"].(string); ok && len(accountID) > 0 {
					bucketDocID := common.GenerateCombinedHashID(s3bucket, common.AWS_S3_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, s3bucket, bucketDocID, common.AWS_S3_RESOURCE_TYPE)
				}
			}
		}
	}
	if createdByRole, ok := entityJSONMap["createdBy"].(string); ok {
		splitArn := strings.Split(createdByRole, "/")
		role := strings.ToLower(splitArn[len(splitArn)-1])
		roleDocID := common.GenerateCombinedHashID(role, common.AWS_IAM_ROLE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, role, roleDocID, common.AWS_IAM_ROLE_RESOURCE_TYPE)
	}
}

func processAWSGuardDutyFinding(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if gDFinding, ok := entityJSONMap["finding"].(map[string]any); ok {
		if resource, ok := gDFinding["resource"].(map[string]any); ok {
			if instanceDetails, ok := resource["instanceDetails"].(map[string]any); ok {
				if instanceID, ok := instanceDetails["instanceId"].(string); ok && len(instanceID) > 0 {
					instanceID = strings.ToLower(instanceID)
					instanceDocID := common.GenerateCombinedHashID(instanceID, common.AWS_EC2_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, instanceID, instanceDocID, common.AWS_EC2_RESOURCE_TYPE)
				}
				if amiID, ok := instanceDetails["imageId"].(string); ok && len(amiID) >= 0 {
					amiID = strings.ToLower(amiID)
					amiDocID := common.GenerateCombinedHashID(amiID, common.AWS_AMI_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, amiID, amiDocID, common.AWS_AMI_RESOURCE_TYPE)
				}
				if networkInterfaces, ok := instanceDetails["networkInterfaces"].([]any); ok {
					for _, networkInterface := range networkInterfaces {
						if networkInterfaceMap, ok := networkInterface.(map[string]any); ok {
							if networkInterfaceID, ok := networkInterfaceMap["networkInterfaceId"].(string); ok && len(networkInterfaceID) > 0 {
								networkInterfaceID = strings.ToLower(networkInterfaceID)
								networkInterfaceDocID := common.GenerateCombinedHashID(networkInterfaceID, common.NETWORKINTERFACE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
								AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, networkInterfaceID, networkInterfaceDocID, common.NETWORKINTERFACE_RESOURCE_TYPE)
							}
							if securityGroups, ok := networkInterfaceMap["securityGroups"].([]any); ok {
								for _, securityGroup := range securityGroups {
									if sgMap, ok := securityGroup.(map[string]any); ok {
										if sgID, ok := sgMap["groupId"].(string); ok && len(sgID) > 0 {
											sgID = strings.ToLower(sgID)
											sgDocID := common.GenerateCombinedHashID(sgID, common.AWS_SG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
											AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, sgID, sgDocID, common.AWS_SG_RESOURCE_TYPE)
										}
									}
								}
							}
							if subnetID, ok := networkInterfaceMap["subnetId"].(string); ok && len(subnetID) > 0 {
								subnetID = strings.ToLower(subnetID)
								subnetDocID := common.GenerateCombinedHashID(subnetID, common.AWS_SUBNET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
								AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, subnetID, subnetDocID, common.AWS_SUBNET_RESOURCE_TYPE)
							}
							if vpcID, ok := networkInterfaceMap["vpcId"].(string); ok && len(vpcID) > 0 {
								vpcID = strings.ToLower(vpcID)
								vpcDocID := common.GenerateCombinedHashID(vpcID, common.AWS_VPC_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
								AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, vpcID, vpcDocID, common.AWS_VPC_RESOURCE_TYPE)
							}
						}
					}
				}
			}
		}
	}
}

func processAWSSageMakerDomain(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if vpcID, ok := entityJSONMap["vpcId"].(string); ok && len(vpcID) > 0 {
		vpcID = strings.ToLower(vpcID)
		vpcDocID := common.GenerateCombinedHashID(vpcID, common.AWS_VPC_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, vpcID, vpcDocID, common.AWS_VPC_RESOURCE_TYPE)
	}
	if subnetIds, ok := entityJSONMap["subnetIds"].([]any); ok {
		for _, subnetID := range subnetIds {
			if subnetID, ok := subnetID.(string); ok && len(subnetID) > 0 {
				subnetID = strings.ToLower(subnetID)
				subnetDocID := common.GenerateCombinedHashID(subnetID, common.AWS_SUBNET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, subnetID, subnetDocID, common.AWS_SUBNET_RESOURCE_TYPE)
			}
		}
	}
}

func processAWSELB(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if ec2Instances, ok := entityJSONMap["ec2Instances"].([]any); ok {
		for _, ec2Instance := range ec2Instances {
			if ec2InstanceID, ok := ec2Instance.(string); ok && len(ec2InstanceID) > 0 {
				ec2InstanceID = strings.ToLower(ec2InstanceID)
				ec2InstanceDocID := common.GenerateCombinedHashID(ec2InstanceID, common.AWS_EC2_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, ec2InstanceID, ec2InstanceDocID, common.AWS_EC2_RESOURCE_TYPE)
			}
		}
	}
	if vpcIds, ok := entityJSONMap["vpcIds"].([]any); ok {
		for _, vpcID := range vpcIds {
			if vpcID, ok := vpcID.(string); ok && len(vpcID) > 0 {
				vpcID = strings.ToLower(vpcID)
				vpcDocID := common.GenerateCombinedHashID(vpcID, common.AWS_VPC_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, vpcID, vpcDocID, common.AWS_VPC_RESOURCE_TYPE)
			}
		}
	}
	if subnetIds, ok := entityJSONMap["subnets"].([]any); ok {
		for _, subnetID := range subnetIds {
			if subnetID, ok := subnetID.(string); ok && len(subnetID) > 0 {
				subnetID = strings.ToLower(subnetID)
				subnetDocID := common.GenerateCombinedHashID(subnetID, common.AWS_SUBNET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, subnetID, subnetDocID, common.AWS_SUBNET_RESOURCE_TYPE)
			}
		}
	}
	if securityGroups, ok := entityJSONMap["securityGroups"].([]any); ok {
		for _, securityGroup := range securityGroups {
			if sgID, ok := securityGroup.(string); ok && len(sgID) > 0 {
				sgID = strings.ToLower(sgID)
				sgDocID := common.GenerateCombinedHashID(sgID, common.AWS_SG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, sgID, sgDocID, common.AWS_SG_RESOURCE_TYPE)
			}
		}
	}
}

func processAWSEBSVolume(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if clusterArn, ok := entityJSONMap["clusterArn"].(string); ok && len(clusterArn) >= 0 {
		cluster := strings.ToLower(clusterArn)
		clusterDocID := common.GenerateCombinedHashID(cluster, common.AWS_EKSCLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, cluster, clusterDocID, common.AWS_EKSCLUSTER_RESOURCE_TYPE)
	}
	if kmsKeyID, ok := entityJSONMap["kmsKeyId"].(string); ok && len(kmsKeyID) > 0 {
		kmsKeyID = strings.ToLower(kmsKeyID)
		kmsKeyDocID := common.GenerateCombinedHashID(kmsKeyID, common.AWS_KMS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, kmsKeyID, kmsKeyDocID, common.AWS_KMS_RESOURCE_TYPE)
	}
}

func processAWSECSCluster(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if instances, ok := entityJSONMap["ec2InstanceIds"].([]any); ok {
		for _, instance := range instances {
			if instanceID, ok := instance.(string); ok && len(instanceID) > 0 {
				instanceID = strings.ToLower(instanceID)
				instanceDocID := common.GenerateCombinedHashID(instanceID, common.AWS_EC2_RESOURCE_TYPE, resourceContextDoc.Account, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, instanceID, instanceDocID, common.AWS_EC2_RESOURCE_TYPE)
			}
		}
	}
}

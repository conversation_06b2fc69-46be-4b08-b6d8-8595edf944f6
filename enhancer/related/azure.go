package related

import (
	"strings"

	"github.com/precize/common"
	"github.com/precize/enhancer/rcontext"
)

func processAzureVM(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if nwInterfaces, ok := entityJSONMap["azureNetworkInterfaces"].([]any); ok {
		for _, nwInterface := range nwInterfaces {
			if nwInterfaceMap, ok := nwInterface.(map[string]any); ok {
				if nwInterfaceID, ok := nwInterfaceMap["id"].(string); ok && len(nwInterfaceID) > 0 {
					nwInterfaceID = strings.ToLower(nwInterfaceID)
					nwInterfaceDocID := common.GenerateCombinedHashID(nwInterfaceID, common.NETWORKINTERFACE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, nwInterfaceID, nwInterfaceDocID, common.NETWORKINTERFACE_RESOURCE_TYPE)
				}
				if nwProperties, ok := nwInterfaceMap["properties"].(map[string]any); ok {
					if nwSecurityGroup, ok := nwProperties["networkSecurityGroup"].(map[string]any); ok {
						if nsgID, ok := nwSecurityGroup["id"].(string); ok && len(nsgID) > 0 {
							nsgID = strings.ToLower(nsgID)
							nsgDocID := common.GenerateCombinedHashID(nsgID, common.AZURE_NSG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
							AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, nsgID, nsgDocID, common.AZURE_NSG_RESOURCE_TYPE)
						}
					}
					if ipConfigs, ok := nwProperties["ipConfigurations"].([]any); ok {
						for _, ipConfig := range ipConfigs {
							if ipConfigMap, ok := ipConfig.(map[string]any); ok {
								if ipConfigProperties, ok := ipConfigMap["properties"].(map[string]any); ok {
									if subnet, ok := ipConfigProperties["subnet"].(map[string]any); ok {
										if subnetID, ok := subnet["id"].(string); ok && len(subnetID) > 0 {
											subnetID = strings.ToLower(subnetID)
											subnetDocID := common.GenerateCombinedHashID(subnetID, common.AZURE_SUBNET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
											AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, subnetID, subnetDocID, common.AZURE_SUBNET_RESOURCE_TYPE)
										}
									}
									if publicIpAddress, ok := ipConfigProperties["publicIPAddress"].(map[string]any); ok {
										if publicIpAddressID, ok := publicIpAddress["id"].(string); ok && len(publicIpAddressID) > 0 {
											publicIpAddressID = strings.ToLower(publicIpAddressID)
											publicIPAddressDocID := common.GenerateCombinedHashID(publicIpAddressID, common.AZURE_PUBLICIP_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
											AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, publicIpAddressID, publicIPAddressDocID, common.AZURE_PUBLICIP_RESOURCE_TYPE)
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}
	if publicIP, ok := entityJSONMap["publicIp"].(string); ok {
		publicIPAddressDocID := common.GenerateCombinedHashID(publicIP, common.AZURE_IPDETAILS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, publicIP, publicIPAddressDocID, common.AZURE_IPDETAILS_RESOURCE_TYPE)
	}
	if scaleSetID, ok := entityJSONMap["scaleSetId"].(string); ok {
		scaleSetID = strings.ToLower(scaleSetID)
		scaleSetDocID := common.GenerateCombinedHashID(scaleSetID, common.AZURE_VMSCALESET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, scaleSetID, scaleSetDocID, common.AZURE_VMSCALESET_RESOURCE_TYPE)
	}
}

func processAzureSubnet(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if virtualNetworkID, ok := entityJSONMap["virtualNetworkId"].(string); ok && len(virtualNetworkID) > 0 {
		virtualNetworkID = strings.ToLower(virtualNetworkID)
		virtualNetworkDocID := common.GenerateCombinedHashID(virtualNetworkID, common.AZURE_VIRTUALNETWORK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, virtualNetworkID, virtualNetworkDocID, common.AZURE_VIRTUALNETWORK_RESOURCE_TYPE, RelatedResourceOpts{Parent: true})
	}
}

func processAzureVMDisk(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if vmID, ok := entityJSONMap["managedBy"].(string); ok && len(vmID) > 0 {
		vmID = strings.ToLower(vmID)
		vmDocID := common.GenerateCombinedHashID(vmID, common.AZURE_VM_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, vmID, vmDocID, common.AZURE_VM_RESOURCE_TYPE)
	}
}

func processAzureSnapshot(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if snapShotProperties, ok := entityJSONMap["properties"].(map[string]any); ok {
		if snapShotCreationData, ok := snapShotProperties["creationData"].(map[string]any); ok {
			if vmDiskID, ok := snapShotCreationData["sourceResourceId"].(string); ok && len(vmDiskID) > 0 {
				vmDiskID = strings.ToLower(vmDiskID)
				vmDiskDocID := common.GenerateCombinedHashID(vmDiskID, common.AZURE_VMDISK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, vmDiskID, vmDiskDocID, common.AZURE_VMDISK_RESOURCE_TYPE, RelatedResourceOpts{Parent: true})
			}
		}
	}
}

func processAzureNATGateway(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if natProperties, ok := entityJSONMap["properties"].(map[string]any); ok {
		if subnets, ok := natProperties["subnets"].([]any); ok {
			for _, subnet := range subnets {
				if subnetMap, ok := subnet.(map[string]any); ok {
					if subnetID, ok := subnetMap["id"].(string); ok && len(subnetID) > 0 {
						subnetID = strings.ToLower(subnetID)
						subnetDocID := common.GenerateCombinedHashID(subnetID, common.AZURE_SUBNET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, subnetID, subnetDocID, common.AZURE_SUBNET_RESOURCE_TYPE)
					}
				}
			}
		}
		if publicIpAddresses, ok := natProperties["publicIpAddresses"].([]any); ok {
			for _, publicIpAddress := range publicIpAddresses {
				if publicIpAddressMap, ok := publicIpAddress.(map[string]any); ok {
					if publicIpAddressID, ok := publicIpAddressMap["id"].(string); ok && len(publicIpAddressID) > 0 {
						publicIpAddressID = strings.ToLower(publicIpAddressID)
						publicIPAddressDocID := common.GenerateCombinedHashID(publicIpAddressID, common.AZURE_PUBLICIP_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, publicIpAddressID, publicIPAddressDocID, common.AZURE_PUBLICIP_RESOURCE_TYPE)
					}
				}
			}
		}
	}
}

func processAzureSQLDB(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if sqlServerName, ok := entityJSONMap["sqlServerName"].(string); ok && len(sqlServerName) > 0 {
		var sqlServerPrefix string
		splitName := strings.Split(resourceContextDoc.ResourceID, "/")
		for i, v := range splitName {
			if v == "servers" {
				sqlServerPrefix = strings.Join(splitName[:i+1], "/")
				break
			}
		}
		if len(sqlServerPrefix) > 0 {
			sqlServerID := sqlServerPrefix + "/" + strings.ToLower(sqlServerName)
			sqlServerDocID := common.GenerateCombinedHashID(sqlServerID, common.AZURE_SQLSERVER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, sqlServerID, sqlServerDocID, common.AZURE_SQLSERVER_RESOURCE_TYPE, RelatedResourceOpts{Parent: true})
		}
	}
}

func processAzureArcDataSQLDB(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if arcDataInstanceName, ok := entityJSONMap["instance"].(string); ok && len(arcDataInstanceName) > 0 {
		var arcDataInstancePrefix string
		splitName := strings.Split(resourceContextDoc.ResourceID, "/")
		for i, v := range splitName {
			if v == "sqlserverinstances" {
				arcDataInstancePrefix = strings.Join(splitName[:i+1], "/")
				break
			}
		}
		if len(arcDataInstancePrefix) > 0 {
			arcDataInstanceId := arcDataInstancePrefix + "/" + strings.ToLower(arcDataInstanceName)
			arcDataInstanceDocID := common.GenerateCombinedHashID(arcDataInstanceId, common.AZURE_ARCDATASQLINSTANCE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, arcDataInstanceId, arcDataInstanceDocID, common.AZURE_ARCDATASQLINSTANCE_RESOURCE_TYPE, RelatedResourceOpts{Parent: true})
		}
	}
}

func processAzureMLWorkspace(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if properties, ok := entityJSONMap["properties"].(map[string]any); ok {
		if storageAccountID, ok := properties["storageAccount"].(string); ok && len(storageAccountID) > 0 {
			storageAccountID = strings.ToLower(storageAccountID)
			storageAccountDocID := common.GenerateCombinedHashID(storageAccountID, common.AZURE_STORAGEACCOUNT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, storageAccountID, storageAccountDocID, common.AZURE_STORAGEACCOUNT_RESOURCE_TYPE)
		}
		if keyVaultID, ok := properties["keyVault"].(string); ok && len(keyVaultID) > 0 {
			keyVaultID = strings.ToLower(keyVaultID)
			keyVaultDocID := common.GenerateCombinedHashID(keyVaultID, common.AZURE_KEYVAULT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, keyVaultID, keyVaultDocID, common.AZURE_KEYVAULT_RESOURCE_TYPE)
		}
		if containerRegistryID, ok := properties["containerRegistry"].(string); ok && len(containerRegistryID) > 0 {
			containerRegistryID = strings.ToLower(containerRegistryID)
			containerRegistryDocID := common.GenerateCombinedHashID(containerRegistryID, common.AZURE_CONTAINERREGISTRY_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, containerRegistryID, containerRegistryDocID, common.AZURE_CONTAINERREGISTRY_RESOURCE_TYPE)
		}
	}
}

func processAzureMLWorkspaceDataStore(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if mlWorkspaceName, ok := entityJSONMap["workspaceName"].(string); ok && len(mlWorkspaceName) > 0 {
		var mlWorkspacePrefix string
		splitName := strings.Split(resourceContextDoc.ResourceID, "/")
		for i, v := range splitName {
			if v == "workspaces" {
				mlWorkspacePrefix = strings.Join(splitName[:i+1], "/")
				break
			}
		}
		if len(mlWorkspacePrefix) > 0 {
			mlWorkspaceID := mlWorkspacePrefix + "/" + strings.ToLower(mlWorkspaceName)
			mlWorkspaceDocID := common.GenerateCombinedHashID(mlWorkspaceID, common.AZURE_MLWORKSPACE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, mlWorkspaceID, mlWorkspaceDocID, common.AZURE_MLWORKSPACE_RESOURCE_TYPE)
		}
	}
}

func processAzureMLWorkspaceModels(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if mlWorkspaceName, ok := entityJSONMap["workspaceName"].(string); ok && len(mlWorkspaceName) > 0 {
		var mlWorkspacePrefix string
		splitName := strings.Split(resourceContextDoc.ResourceID, "/")
		for i, v := range splitName {
			if v == "workspaces" {
				mlWorkspacePrefix = strings.Join(splitName[:i+1], "/")
				break
			}
		}
		if len(mlWorkspacePrefix) > 0 {
			mlWorkspaceID := mlWorkspacePrefix + "/" + strings.ToLower(mlWorkspaceName)
			mlWorkspaceDocID := common.GenerateCombinedHashID(mlWorkspaceID, common.AZURE_MLWORKSPACE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, mlWorkspaceID, mlWorkspaceDocID, common.AZURE_MLWORKSPACE_RESOURCE_TYPE)
		}
	}
	var mlWorkspace string
	splitName := strings.Split(resourceContextDoc.ResourceID, "/")
	for i, v := range splitName {
		if v == "models" {
			mlWorkspace = strings.Join(splitName[:i], "/")
			break
		}
	}
	if len(mlWorkspace) > 0 {
		if mlExperiment, ok := entityJSONMap["experimentId"].(string); ok && len(mlExperiment) > 0 {
			mlExperimentID := mlWorkspace + "/experiments/" + strings.ToLower(mlExperiment)
			mlExperimentDocID := common.GenerateCombinedHashID(mlExperimentID, common.AZURE_MLWORKSPACEEXPERIMENTS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, mlExperimentID, mlExperimentDocID, common.AZURE_MLWORKSPACEEXPERIMENTS_RESOURCE_TYPE)
		}
		if mlJob, ok := entityJSONMap["runId"].(string); ok && len(mlJob) > 0 {
			mlJobID := mlWorkspace + "/jobs/" + strings.ToLower(mlJob)
			mlJobDocID := common.GenerateCombinedHashID(mlJobID, common.AZURE_MLWORKSPACEJOBS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, mlJobID, mlJobDocID, common.AZURE_MLWORKSPACEJOBS_RESOURCE_TYPE)
		}
		if mlDataStore, ok := entityJSONMap["dataStore"].(string); ok && len(mlDataStore) > 0 {
			mlDataStoreID := mlWorkspace + "/datastores/" + strings.ToLower(mlDataStore)
			mlDataStoreDocID := common.GenerateCombinedHashID(mlDataStoreID, common.AZURE_MLWORKSPACEDATASTORE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, mlDataStoreID, mlDataStoreDocID, common.AZURE_MLWORKSPACEDATASTORE_RESOURCE_TYPE)
		}
	}
}

func processAzureMLWorkspaceJobs(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if mlWorkspaceName, ok := entityJSONMap["workspaceName"].(string); ok && len(mlWorkspaceName) > 0 {
		var mlWorkspacePrefix string
		splitName := strings.Split(resourceContextDoc.ResourceID, "/")
		for i, v := range splitName {
			if v == "workspaces" {
				mlWorkspacePrefix = strings.Join(splitName[:i+1], "/")
				break
			}
		}
		if len(mlWorkspacePrefix) > 0 {
			mlWorkspaceID := mlWorkspacePrefix + "/" + strings.ToLower(mlWorkspaceName)
			mlWorkspaceDocID := common.GenerateCombinedHashID(mlWorkspaceID, common.AZURE_MLWORKSPACE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, mlWorkspaceID, mlWorkspaceDocID, common.AZURE_MLWORKSPACE_RESOURCE_TYPE)
		}
	}
	if mlWorkspaceCompute, ok := entityJSONMap["computeId"].(string); ok && len(mlWorkspaceCompute) > 0 {
		mlWorkspaceComputeID := strings.ToLower(mlWorkspaceCompute)
		mlWorkspaceComputeDocID := common.GenerateCombinedHashID(mlWorkspaceComputeID, common.AZURE_MLWORKSPACECOMPUTEMACHINE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, mlWorkspaceComputeID, mlWorkspaceComputeDocID, common.AZURE_MLWORKSPACECOMPUTEMACHINE_RESOURCE_TYPE)
	}
	var mlWorkspace string
	splitName := strings.Split(resourceContextDoc.ResourceID, "/")
	for i, v := range splitName {
		if v == "jobs" {
			mlWorkspace = strings.Join(splitName[:i], "/")
			break
		}
	}
	if len(mlWorkspace) > 0 {
		if mlExperiment, ok := entityJSONMap["experimentId"].(string); ok && len(mlExperiment) > 0 {
			mlExperimentID := mlWorkspace + "/experiments/" + strings.ToLower(mlExperiment)
			mlExperimentDocID := common.GenerateCombinedHashID(mlExperimentID, common.AZURE_MLWORKSPACEEXPERIMENTS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, mlExperimentID, mlExperimentDocID, common.AZURE_MLWORKSPACEEXPERIMENTS_RESOURCE_TYPE)
		}
		if mlDataAsset, ok := entityJSONMap["dataAsset"].(string); ok && len(mlDataAsset) > 0 {
			mlDataAssetID := mlWorkspace + "/data/" + strings.ToLower(mlDataAsset)
			mlDataAssetDocID := common.GenerateCombinedHashID(mlDataAssetID, common.AZURE_MLWORKSPACEDATAASSET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, mlDataAssetID, mlDataAssetDocID, common.AZURE_MLWORKSPACEDATAASSET_RESOURCE_TYPE)
		}
		if mlDatastoreID, ok := entityJSONMap["blobStoreId"].(string); ok && len(mlDatastoreID) > 0 {
			mlDatastoreID := strings.ToLower(mlDatastoreID)
			mlDataStoreDocID := common.GenerateCombinedHashID(mlDatastoreID, common.AZURE_MLWORKSPACEDATASTORE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, mlDatastoreID, mlDataStoreDocID, common.AZURE_MLWORKSPACEDATASTORE_RESOURCE_TYPE)
		}
	}
}

func processAzureMLWorkspaceComputeMachine(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if mlWorkspaceName, ok := entityJSONMap["workspaceName"].(string); ok && len(mlWorkspaceName) > 0 {
		var mlWorkspacePrefix string
		splitName := strings.Split(resourceContextDoc.ResourceID, "/")
		for i, v := range splitName {
			if v == "workspaces" {
				mlWorkspacePrefix = strings.Join(splitName[:i+1], "/")
				break
			}
		}
		if len(mlWorkspacePrefix) > 0 {
			mlWorkspaceID := mlWorkspacePrefix + "/" + strings.ToLower(mlWorkspaceName)
			mlWorkspaceDocID := common.GenerateCombinedHashID(mlWorkspaceID, common.AZURE_MLWORKSPACE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, mlWorkspaceID, mlWorkspaceDocID, common.AZURE_MLWORKSPACE_RESOURCE_TYPE)
		}
	}
}

func processAzureMLWorkspaceDeployments(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if mlWorkspaceName, ok := entityJSONMap["workspaceName"].(string); ok && len(mlWorkspaceName) > 0 {
		var mlWorkspacePrefix string
		splitName := strings.Split(resourceContextDoc.ResourceID, "/")
		for i, v := range splitName {
			if v == "workspaces" {
				mlWorkspacePrefix = strings.Join(splitName[:i+1], "/")
				break
			}
		}
		if len(mlWorkspacePrefix) > 0 {
			mlWorkspaceID := mlWorkspacePrefix + "/" + strings.ToLower(mlWorkspaceName)
			mlWorkspaceDocID := common.GenerateCombinedHashID(mlWorkspaceID, common.AZURE_MLWORKSPACE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, mlWorkspaceID, mlWorkspaceDocID, common.AZURE_MLWORKSPACE_RESOURCE_TYPE)
		}
	}
	if mlWorkspaceCompute, ok := entityJSONMap["computeId"].(string); ok && len(mlWorkspaceCompute) > 0 {
		mlWorkspaceComputeID := strings.ToLower(mlWorkspaceCompute)
		mlWorkspaceComputeDocID := common.GenerateCombinedHashID(mlWorkspaceComputeID, common.AZURE_MLWORKSPACECOMPUTEMACHINE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, mlWorkspaceComputeID, mlWorkspaceComputeDocID, common.AZURE_MLWORKSPACECOMPUTEMACHINE_RESOURCE_TYPE)
	}
	if mlWorkspaceModel, ok := entityJSONMap["model"].(string); ok && len(mlWorkspaceModel) > 0 {
		var mlWorkspaceModelPrefix string
		splitName := strings.Split(resourceContextDoc.ResourceID, "/")
		for i, v := range splitName {
			if v == "workspaces" {
				if len(splitName) >= i+2 {
					mlWorkspaceModelPrefix = strings.Join(splitName[:i+2], "/")
					break
				}
			}
		}
		if len(mlWorkspaceModelPrefix) > 0 {
			mlWorkspaceModelID := mlWorkspaceModelPrefix + "/models/" + strings.ToLower(mlWorkspaceModel)
			mlWorkspaceModelDocID := common.GenerateCombinedHashID(mlWorkspaceModelID, common.AZURE_MLWORKSPACEMODELS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, mlWorkspaceModelID, mlWorkspaceModelDocID, common.AZURE_MLWORKSPACEMODELS_RESOURCE_TYPE)
		}
	}
}

func processAzureMLWorkspaceEndpoints(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if mlWorkspaceName, ok := entityJSONMap["workspaceName"].(string); ok && len(mlWorkspaceName) > 0 {
		var mlWorkspacePrefix string
		splitName := strings.Split(resourceContextDoc.ResourceID, "/")
		for i, v := range splitName {
			if v == "workspaces" {
				mlWorkspacePrefix = strings.Join(splitName[:i+1], "/")
				break
			}
		}
		if len(mlWorkspacePrefix) > 0 {
			mlWorkspaceID := mlWorkspacePrefix + "/" + strings.ToLower(mlWorkspaceName)
			mlWorkspaceDocID := common.GenerateCombinedHashID(mlWorkspaceID, common.AZURE_MLWORKSPACE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, mlWorkspaceID, mlWorkspaceDocID, common.AZURE_MLWORKSPACE_RESOURCE_TYPE)
		}
	}
	if deployments, ok := entityJSONMap["deployments"].([]any); ok {
		for _, deployment := range deployments {
			if deploymentString, ok := deployment.(string); ok && len(deploymentString) > 0 {
				deploymentID := resourceContextDoc.ResourceID + "/deployments/" + strings.ToLower(deploymentString)
				deploymentDocID := common.GenerateCombinedHashID(deploymentID, common.AZURE_MLWORKSPACEDEPLOYMENTS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, deploymentID, deploymentDocID, common.AZURE_MLWORKSPACEDEPLOYMENTS_RESOURCE_TYPE)
			}
		}
	}
	if models, ok := entityJSONMap["models"].([]any); ok {
		var mlWorkspaceModelPrefix string
		splitName := strings.Split(resourceContextDoc.ResourceID, "/")
		for i, v := range splitName {
			if v == "workspaces" {
				if len(splitName) >= i+2 {
					mlWorkspaceModelPrefix = strings.Join(splitName[:i+2], "/")
					break
				}
			}
		}
		if len(mlWorkspaceModelPrefix) > 0 {
			for _, model := range models {
				if modelString, ok := model.(string); ok && len(modelString) > 0 {
					modelID := mlWorkspaceModelPrefix + "/models/" + strings.ToLower(modelString)
					modelDocID := common.GenerateCombinedHashID(modelID, common.AZURE_MLWORKSPACEMODELS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, modelID, modelDocID, common.AZURE_MLWORKSPACEMODELS_RESOURCE_TYPE)
				}
			}
		}
	}
}

func processAzureMLHubConnection(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if properties, ok := entityJSONMap["properties"].(map[string]any); ok {
		if mlWorkspaceName, ok := properties["workspace"].(string); ok && len(mlWorkspaceName) > 0 {
			var mlWorkspacePrefix string
			splitName := strings.Split(resourceContextDoc.ResourceID, "/")
			for i, v := range splitName {
				if v == "workspaces" {
					mlWorkspacePrefix = strings.Join(splitName[:i+1], "/")
					break
				}
			}
			if len(mlWorkspacePrefix) > 0 {
				mlWorkspaceID := mlWorkspacePrefix + "/" + strings.ToLower(mlWorkspaceName)
				mlWorkspaceDocID := common.GenerateCombinedHashID(mlWorkspaceID, common.AZURE_MLWORKSPACE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, mlWorkspaceID, mlWorkspaceDocID, common.AZURE_MLWORKSPACE_RESOURCE_TYPE)
			}
		}
	}
}

func processAzureOpenAIResource(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if openAIAccountName, ok := entityJSONMap["accountName"].(string); ok && len(openAIAccountName) > 0 {
		var openAIAccountNamePrefix string
		splitName := strings.Split(resourceContextDoc.ResourceID, "/")
		for i, v := range splitName {
			if v == "accounts" {
				openAIAccountNamePrefix = strings.Join(splitName[:i+1], "/")
				break
			}
		}
		if len(openAIAccountNamePrefix) > 0 {
			openAIAccountID := openAIAccountNamePrefix + "/" + strings.ToLower(openAIAccountName)
			openAIAccountDocID := common.GenerateCombinedHashID(openAIAccountID, common.AZURE_OPENAI_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, openAIAccountID, openAIAccountDocID, common.AZURE_OPENAI_RESOURCE_TYPE, RelatedResourceOpts{Parent: true})
		}
	}
	if resourceContextDoc.ResourceType == common.AZURE_OPENAIDEPLOYMENT_RESOURCE_TYPE {
		if openAIModelID, ok := entityJSONMap["modelId"].(string); ok && len(openAIModelID) > 0 {
			openAIModelID = strings.ToLower(openAIModelID)
			openAIModelDocID := common.GenerateCombinedHashID(openAIModelID, common.AZURE_OPENAIMODEL_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, openAIModelID, openAIModelDocID, common.AZURE_OPENAIMODEL_RESOURCE_TYPE)
		}
		if contentFilterID, ok := entityJSONMap["cfId"].(string); ok && len(contentFilterID) > 0 {
			contentFilterID = strings.ToLower(contentFilterID)
			contentFilterDocID := common.GenerateCombinedHashID(contentFilterID, common.AZURE_OPENAICONTENTFILTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, contentFilterID, contentFilterDocID, common.AZURE_OPENAICONTENTFILTER_RESOURCE_TYPE)
		}
	}
}

func processAzureMetricAlert(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if actionGroupIds, ok := entityJSONMap["actionGroupIds"].([]any); ok {
		for _, actionGroupId := range actionGroupIds {
			if actionGroupId, ok := actionGroupId.(string); ok && len(actionGroupId) > 0 {
				actionGroupIdDocID := common.GenerateCombinedHashID(actionGroupId, common.AZURE_ACTIONGROUP_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, actionGroupId, actionGroupIdDocID, common.AZURE_ACTIONGROUP_RESOURCE_TYPE)
			}
		}
	}
}

func processAzureScheduledQueryRules(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if actionGroupIds, ok := entityJSONMap["actionGroupIds"].([]any); ok {
		for _, actionGroupId := range actionGroupIds {
			if actionGroupId, ok := actionGroupId.(string); ok && len(actionGroupId) > 0 {
				actionGroupIdDocID := common.GenerateCombinedHashID(actionGroupId, common.AZURE_ACTIONGROUP_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, actionGroupId, actionGroupIdDocID, common.AZURE_ACTIONGROUP_RESOURCE_TYPE)
			}
		}
	}
}

func processAzureAKSCluster(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if identityProfile, ok := entityJSONMap["identityProfile"].(map[string]any); ok {
		if kubeletIdentity, ok := identityProfile["kubeletidentity"].(map[string]any); ok {
			if userAssignedIdentityID, ok := kubeletIdentity["resourceId"].(string); ok && len(userAssignedIdentityID) > 0 {
				userAssignedIdentityID = strings.ToLower(userAssignedIdentityID)
				userAssignedIdentityIDDocID := common.GenerateCombinedHashID(userAssignedIdentityID, common.AZURE_USERASSIGNEDIDENTITY_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, userAssignedIdentityID, userAssignedIdentityIDDocID, common.AZURE_USERASSIGNEDIDENTITY_RESOURCE_TYPE)
			}
		}
	}
	if networkProfile, ok := entityJSONMap["networkProfile"].(map[string]any); ok {
		if loadBalancerProfile, ok := networkProfile["loadBalancerProfile"].(map[string]any); ok {
			if outboundIPs, ok := loadBalancerProfile["effectiveOutboundIPs"].([]any); ok {
				for _, outboundIP := range outboundIPs {
					if outboundIPMap, ok := outboundIP.(map[string]any); ok {
						if publicIpAddressID, ok := outboundIPMap["id"].(string); ok && len(publicIpAddressID) > 0 {
							publicIpAddressID = strings.ToLower(publicIpAddressID)
							publicIPAddressDocID := common.GenerateCombinedHashID(publicIpAddressID, common.AZURE_PUBLICIP_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
							AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, publicIpAddressID, publicIPAddressDocID, common.AZURE_PUBLICIP_RESOURCE_TYPE)
						}
					}
				}
			}
		}
	}
}

func processAzureK8Node(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if clusterName, ok := entityJSONMap["clusterName"].(string); ok && len(clusterName) >= 0 {
		var clusterPrefix string
		splitName := strings.Split(resourceContextDoc.ResourceID, "/")
		for i, v := range splitName {
			if v == "managedclusters" {
				clusterPrefix = strings.Join(splitName[:i], "/")
				break
			}
		}
		if len(clusterPrefix) > 0 {
			clusterID := clusterPrefix + "/providers/microsoft.containerservice/managedclusters/" + strings.ToLower(clusterName)
			clusterDocID := common.GenerateCombinedHashID(clusterID, common.AZURE_AKSCLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, clusterID, clusterDocID, common.AZURE_AKSCLUSTER_RESOURCE_TYPE, RelatedResourceOpts{Parent: true, Oneway: true})
		}
	}
	if k8Spec, ok := entityJSONMap["spec"].(map[string]any); ok {
		if vmID, ok := k8Spec["providerID"].(string); ok && len(vmID) > 0 {
			vmID = strings.TrimPrefix(strings.ToLower(vmID), "azure://")
			vmDocID := common.GenerateCombinedHashID(vmID, common.AZURE_VM_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, vmID, vmDocID, common.AZURE_VM_RESOURCE_TYPE)
		}
	}
	if status, ok := entityJSONMap["status"].(map[string]any); ok {
		if volumesInUse, ok := status["volumesInUse"].([]any); ok {
			for _, volumeInUse := range volumesInUse {
				if volumeInUseString, ok := volumeInUse.(string); ok && len(volumeInUseString) > 0 {
					var vmDiskID string
					volumeSplit := strings.Split(volumeInUseString, "^")
					if len(volumeSplit) > 0 {
						vmDiskID = strings.ToLower(volumeSplit[1])
					}
					vmDiskDocID := common.GenerateCombinedHashID(vmDiskID, common.AZURE_VMDISK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, vmDiskID, vmDiskDocID, common.AZURE_VMDISK_RESOURCE_TYPE)
				}
			}
		}
	}
}

func processAzureComputeImage(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if properties, ok := entityJSONMap["properties"].(map[string]any); ok {
		if sourceVirtualMachine, ok := properties["sourceVirtualMachine"].(map[string]any); ok {
			if vmID, ok := sourceVirtualMachine["id"].(string); ok && len(vmID) > 0 {
				vmID = strings.ToLower(vmID)
				vmDocID := common.GenerateCombinedHashID(vmID, common.AZURE_VM_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, vmID, vmDocID, common.AZURE_VM_RESOURCE_TYPE)
			}
		}
		if storageProfile, ok := properties["storageProfile"].(map[string]any); ok {
			if osDisk, ok := storageProfile["osDisk"].(map[string]any); ok {
				if managedDisk, ok := osDisk["managedDisk"].(map[string]any); ok {
					if vmDiskID, ok := managedDisk["id"].(string); ok && len(vmDiskID) > 0 {
						vmDiskID = strings.ToLower(vmDiskID)
						vmDiskDocID := common.GenerateCombinedHashID(vmDiskID, common.AZURE_VMDISK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, vmDiskID, vmDiskDocID, common.AZURE_VMDISK_RESOURCE_TYPE)
					}
				}
			}
		}
	}
}

func processAzureGraphApp(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if alternativeNames, ok := entityJSONMap["alternativeNames"].([]any); ok {
		for _, alternativeName := range alternativeNames {
			if alternativeNameString, ok := alternativeName.(string); ok && len(alternativeNameString) > 0 {
				if strings.Contains(alternativeNameString, "isExplicit") || strings.Contains(alternativeNameString, "appRoleAssignmentRequired") {
					continue
				}
				userIdentityID := strings.ToLower(alternativeNameString)
				userIdentityDocID := common.GenerateCombinedHashID(userIdentityID, common.AZURE_USERASSIGNEDIDENTITY_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, userIdentityID, userIdentityDocID, common.AZURE_USERASSIGNEDIDENTITY_RESOURCE_TYPE)
			}
		}
	}
}

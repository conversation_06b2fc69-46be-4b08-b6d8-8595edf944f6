package related

import (
	"strings"

	"github.com/precize/common"
	"github.com/precize/enhancer/rcontext"
)

func processOpenAIVectorStoreFile(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if vectorStoreID, ok := entityJSONMap["vector_store_id"].(string); ok && len(vectorStoreID) > 0 {
		vectorStoreID = strings.ToLower(vectorStoreID)
		vectorStoreDocID := common.GenerateCombinedHashID(vectorStoreID, common.OPENAI_VECTORSTORE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, vectorStoreID, vectorStoreDocID, common.OPENAI_VECTORSTORE_RESOURCE_TYPE)
	}
}

func processOpenAIFineTunedModel(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if openAITrainingFile, ok := entityJSONMap["training_file"].(string); ok && len(openAITrainingFile) > 0 {
		openAITrainingFileDocID := common.GenerateCombinedHashID(openAITrainingFile, common.OPENAI_FILE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, openAITrainingFile, openAITrainingFileDocID, common.OPENAI_FILE_RESOURCE_TYPE)
	}
	if openAIValidationFile, ok := entityJSONMap["validation_file"].(string); ok && len(openAIValidationFile) > 0 {
		openAIValidationFileDocID := common.GenerateCombinedHashID(openAIValidationFile, common.OPENAI_FILE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, openAIValidationFile, openAIValidationFileDocID, common.OPENAI_FILE_RESOURCE_TYPE)
	}
	if resultFiles, ok := entityJSONMap["result_files"].([]any); ok {
		for _, resultFile := range resultFiles {
			if openAIResultFile, ok := resultFile.(string); ok && len(openAIResultFile) > 0 {
				openAIResultFileDocID := common.GenerateCombinedHashID(openAIResultFile, common.OPENAI_FILE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, openAIResultFile, openAIResultFileDocID, common.OPENAI_FILE_RESOURCE_TYPE)
			}
		}
	}
}

func processOpenAIAssistant(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if openAIModelOrFineTunedModel, ok := entityJSONMap["model"].(string); ok && len(openAIModelOrFineTunedModel) > 0 {
		entityType := common.OPENAI_MODEL_RESOURCE_TYPE
		if strings.HasPrefix(openAIModelOrFineTunedModel, "ft:") {
			entityType = common.OPENAI_FINETUNEDMODEL_RESOURCE_TYPE
		}
		openAIModelOrFineTunedModel = `/projects/` + resourceContextDoc.Account + `/models/` + openAIModelOrFineTunedModel
		openAIModelOrFineTunedModelDocID := common.GenerateCombinedHashID(openAIModelOrFineTunedModel, entityType, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, openAIModelOrFineTunedModel, openAIModelOrFineTunedModelDocID, entityType)
	}
}

func processOpenAITuningJob(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if openAIFineTunedModel, ok := entityJSONMap["fine_tuned_model"].(string); ok && len(openAIFineTunedModel) > 0 {
		openAIFineTunedModel = `/projects/` + resourceContextDoc.Account + `/models/` + openAIFineTunedModel
		openAIFineTunedModelDocID := common.GenerateCombinedHashID(openAIFineTunedModel, common.OPENAI_FINETUNEDMODEL_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, openAIFineTunedModel, openAIFineTunedModelDocID, common.OPENAI_FINETUNEDMODEL_RESOURCE_TYPE)
	}
	if openAIModel, ok := entityJSONMap["model"].(string); ok && len(openAIModel) > 0 {
		openAIModel = `/projects/` + resourceContextDoc.Account + `/models/` + openAIModel
		openAIModelDocID := common.GenerateCombinedHashID(openAIModel, common.OPENAI_MODEL_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, openAIModel, openAIModelDocID, common.OPENAI_MODEL_RESOURCE_TYPE)
	}
	if openAITrainingFile, ok := entityJSONMap["training_file"].(string); ok && len(openAITrainingFile) > 0 {
		openAITrainingFileDocID := common.GenerateCombinedHashID(openAITrainingFile, common.OPENAI_FILE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, openAITrainingFile, openAITrainingFileDocID, common.OPENAI_FILE_RESOURCE_TYPE)
	}
	if openAIValidationFile, ok := entityJSONMap["validation_file"].(string); ok && len(openAIValidationFile) > 0 {
		openAIValidationFileDocID := common.GenerateCombinedHashID(openAIValidationFile, common.OPENAI_FILE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, openAIValidationFile, openAIValidationFileDocID, common.OPENAI_FILE_RESOURCE_TYPE)
	}
	if resultFiles, ok := entityJSONMap["result_files"].([]any); ok {
		for _, resultFile := range resultFiles {
			if openAIResultFile, ok := resultFile.(string); ok && len(openAIResultFile) > 0 {
				openAIResultFileDocID := common.GenerateCombinedHashID(openAIResultFile, common.OPENAI_FILE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, openAIResultFile, openAIResultFileDocID, common.OPENAI_FILE_RESOURCE_TYPE)
			}
		}
	}
	if checkPointModels, ok := entityJSONMap["checkpoint_models"].([]any); ok {
		for _, checkpointModel := range checkPointModels {
			if openAICheckPointModel, ok := checkpointModel.(string); ok && len(openAICheckPointModel) > 0 {
				openAICheckPointModel = `/projects/` + resourceContextDoc.Account + `/models/` + openAICheckPointModel
				openAICheckpointModelDocID := common.GenerateCombinedHashID(openAICheckPointModel, common.OPENAI_FINETUNEDMODEL_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, openAICheckPointModel, openAICheckpointModelDocID, common.OPENAI_FINETUNEDMODEL_RESOURCE_TYPE)
			}
		}
	}
}

func processOpenAITuningJobCheckpoint(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if openAIFineTuningJob, ok := entityJSONMap["fine_tuning_job_id"].(string); ok && len(openAIFineTuningJob) > 0 {
		openAIFineTuningJobDocID := common.GenerateCombinedHashID(openAIFineTuningJob, common.OPENAI_TUNINGJOB_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, openAIFineTuningJob, openAIFineTuningJobDocID, common.OPENAI_TUNINGJOB_RESOURCE_TYPE)
	}
	if openAIFineTunedModel, ok := entityJSONMap["fine_tuned_model_checkpoint"].(string); ok && len(openAIFineTunedModel) > 0 {
		openAIFineTunedModel = `/projects/` + resourceContextDoc.Account + `/models/` + openAIFineTunedModel
		openAIFineTunedModelDocID := common.GenerateCombinedHashID(openAIFineTunedModel, common.OPENAI_FINETUNEDMODEL_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, openAIFineTunedModel, openAIFineTunedModelDocID, common.OPENAI_FINETUNEDMODEL_RESOURCE_TYPE)
	}
}

func processOpenAIApiKey(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if owner, ok := entityJSONMap["owner"].(map[string]any); ok {
		if ownerType, ok := owner["type"].(string); ok && ownerType == "service_account" {
			if serviceAccountMap, ok := owner["service_account"].(map[string]any); ok {
				if serviceAccountID, ok := serviceAccountMap["id"].(string); ok && len(serviceAccountID) > 0 {
					serviceAccountDocID := common.GenerateCombinedHashID(serviceAccountID, common.OPENAI_SERVICEACCOUNT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, serviceAccountID, serviceAccountDocID, common.OPENAI_SERVICEACCOUNT_RESOURCE_TYPE)
				}
			}
		}
	}
}

package related

import (
	"encoding/json"
	"path"
	"strings"

	"github.com/precize/common"
	"github.com/precize/enhancer/rcontext"
)

func processGCPInstance(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if vmInstance, ok := entityJSONMap["vmInstance"].(map[string]any); ok {
		if vmDisks, ok := vmInstance["disks"].([]any); ok {
			for _, vmDisk := range vmDisks {
				if vmDiskMap, ok := vmDisk.(map[string]any); ok {
					if diskSource, ok := vmDiskMap["source"].(string); ok && len(diskSource) > 0 {
						var vmDiskID string
						splitName := strings.Split(diskSource, "/")
						for i, v := range splitName {
							if v == "projects" {
								vmDiskID = strings.Join(splitName[i:], "/")
								break
							}
						}
						vmDiskDocID := common.GenerateCombinedHashID(vmDiskID, common.GCP_VMDISK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, vmDiskID, vmDiskDocID, common.GCP_VMDISK_RESOURCE_TYPE)
					}
				}
			}
		}
		if nwInterfaces, ok := vmInstance["networkInterfaces"].([]any); ok {
			for _, nwInterface := range nwInterfaces {
				if nwInterfaceMap, ok := nwInterface.(map[string]any); ok {
					if network, ok := nwInterfaceMap["network"].(string); ok && len(network) > 0 {
						var networkID string
						splitName := strings.Split(network, "/")
						for i, v := range splitName {
							if v == "projects" {
								networkID = strings.Join(splitName[i:], "/")
								break
							}
						}
						networkDocID := common.GenerateCombinedHashID(networkID, common.GCP_NETWORK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, networkID, networkDocID, common.GCP_NETWORK_RESOURCE_TYPE)
						resourceContext.RangeGCPFirewallTargets(func(network string, targets map[string][]string) bool {
							if allTargets, ok := targets["ALL"]; ok {
								for _, firewallRuleID := range allTargets {
									firewallRuleDocID := common.GenerateCombinedHashID(firewallRuleID, common.GCP_FIREWALL_RESOURCE_TYPE, resourceContextDoc.Account, resourceContextDoc.TenantID)
									AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, firewallRuleID, firewallRuleDocID, common.GCP_FIREWALL_RESOURCE_TYPE)
								}
							}
							return true
						})
						if networkTags, ok := vmInstance["tags"].(map[string]any); ok {
							if tagItems, ok := networkTags["items"].([]any); ok {
								for _, tagItem := range tagItems {
									if networkTag, ok := tagItem.(string); ok && len(networkTag) > 0 {
										processFirewallRulesForTarget(resourceContext, network, "Tag:"+networkTag, resourceContextDoc, resourceDocID)
									}
								}
							}
						}
						if vmServiceAccounts, ok := vmInstance["serviceAccounts"].([]any); ok {
							for _, vmServiceAccount := range vmServiceAccounts {
								if vmServiceAccountMap, ok := vmServiceAccount.(map[string]any); ok {
									if serviceAccountEmail, ok := vmServiceAccountMap["email"].(string); ok && len(serviceAccountEmail) > 0 {
										processFirewallRulesForTarget(resourceContext, network, "SA:"+serviceAccountEmail, resourceContextDoc, resourceDocID)
									}
								}
							}
						}
					}
					if subNetwork, ok := nwInterfaceMap["subnetwork"].(string); ok && len(subNetwork) > 0 {
						var subNetworkID string
						splitName := strings.Split(subNetwork, "/")
						for i, v := range splitName {
							if v == "projects" {
								subNetworkID = strings.Join(splitName[i:], "/")
								break
							}
						}
						subNetworkDocID := common.GenerateCombinedHashID(subNetworkID, common.GCP_SUBNETWORK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, subNetworkID, subNetworkDocID, common.GCP_SUBNETWORK_RESOURCE_TYPE)
					}
				}
			}
		}
	}
	if serviceAccount, ok := entityJSONMap["vmServiceAccountEmail"].(string); ok && len(serviceAccount) > 0 {
		serviceAccount = strings.ToLower(serviceAccount)
		serviceAccountDocID := common.GenerateCombinedHashID(serviceAccount, common.GCP_SERVICEACCOUNT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, serviceAccount, serviceAccountDocID, common.GCP_SERVICEACCOUNT_RESOURCE_TYPE)
	}
	if portInfo, ok := entityJSONMap["portAdditionalInfo"].(map[string]any); ok {
		if ipDetails, ok := portInfo["ipAddress"].(string); ok && len(ipDetails) > 0 {
			ipDetailsDocID := common.GenerateCombinedHashID(ipDetails, common.GCP_IPDETAILS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, ipDetails, ipDetailsDocID, common.GCP_IPDETAILS_RESOURCE_TYPE)
		}
	}
	if clusterID, ok := entityJSONMap["clusterName"].(string); ok && len(clusterID) > 0 {
		clusterID = strings.ToLower(clusterID)
		clusterDocID := common.GenerateCombinedHashID(clusterID, common.GCP_GKECLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, clusterID, clusterDocID, common.GCP_GKECLUSTER_RESOURCE_TYPE)
	}
	if instance, ok := entityJSONMap["vmInstance"].(map[string]any); ok {
		if encryptionKey, ok := instance["instanceEncryptionKey"].(map[string]any); ok {
			if kmsKey, ok := encryptionKey["kmsKeyName"].(string); ok && len(kmsKey) > 0 {
				kmsKey = strings.ToLower(kmsKey)
				kmsDocID := common.GenerateCombinedHashID(kmsKey, common.GCP_KMS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, kmsKey, kmsDocID, common.GCP_KMS_RESOURCE_TYPE)
			}
		}
	}
}

func processGCPNetwork(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if network, ok := entityJSONMap["network"].(map[string]any); ok {
		if subNetworks, ok := network["subnetworks"].([]any); ok {
			for _, subNetwork := range subNetworks {
				if subNetworkString, ok := subNetwork.(string); ok {
					var subNetworkID string
					splitName := strings.Split(subNetworkString, "/")
					for i, v := range splitName {
						if v == "projects" {
							subNetworkID = strings.Join(splitName[i:], "/")
							break
						}
					}
					subNetworkID = strings.ToLower(subNetworkID)
					subNetworkDocID := common.GenerateCombinedHashID(subNetworkID, common.GCP_SUBNETWORK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, subNetworkID, subNetworkDocID, common.GCP_SUBNETWORK_RESOURCE_TYPE)
				}
			}
		}
	}
}

func processGCPServiceAccountKey(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if serviceAccount, ok := entityJSONMap["serviceAccount"].(string); ok {
		serviceAccount = strings.ToLower(serviceAccount)
		serviceAccountDocID := common.GenerateCombinedHashID(serviceAccount, common.GCP_SERVICEACCOUNT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, serviceAccount, serviceAccountDocID, common.GCP_SERVICEACCOUNT_RESOURCE_TYPE, RelatedResourceOpts{Parent: true})
	}
}

func processGCPVMDisk(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if disk, ok := entityJSONMap["disk"].(map[string]any); ok {
		if encryptionKey, ok := disk["diskEncryptionKey"].(map[string]any); ok {
			if kmsKey, ok := encryptionKey["kmsKeyName"].(string); ok && len(kmsKey) > 0 {
				kmsKey = strings.ToLower(kmsKey)
				kmsDocID := common.GenerateCombinedHashID(kmsKey, common.GCP_KMS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, kmsKey, kmsDocID, common.GCP_KMS_RESOURCE_TYPE)
			}
		}
	}
}

func processGCPDiskSnapshot(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if sourceDisk, ok := entityJSONMap["sourceDisk"].(string); ok && len(sourceDisk) >= 0 {
		var vmDiskID string
		splitName := strings.Split(sourceDisk, "/")
		for i, v := range splitName {
			if v == "projects" {
				vmDiskID = strings.Join(splitName[i:], "/")
				break
			}
		}
		vmDiskDocID := common.GenerateCombinedHashID(vmDiskID, common.GCP_VMDISK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, vmDiskID, vmDiskDocID, common.GCP_VMDISK_RESOURCE_TYPE, RelatedResourceOpts{Parent: true})
	}
}

func processGCPAutoscaler(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if autoscaler, ok := entityJSONMap["autoscaler"].(map[string]any); ok {
		if target, ok := autoscaler["target"].(string); ok && len(target) >= 0 {
			var instanceGroupManagerID string
			splitName := strings.Split(target, "/")
			for i, v := range splitName {
				if v == "projects" {
					instanceGroupManagerID = strings.Join(splitName[i:], "/")
					break
				}
			}
			instanceGroupManagerID = strings.ToLower(instanceGroupManagerID)
			instanceGroupManagerDocID := common.GenerateCombinedHashID(instanceGroupManagerID, common.GCP_INSTANCEGROUPMANAGER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, instanceGroupManagerID, instanceGroupManagerDocID, common.GCP_INSTANCEGROUPMANAGER_RESOURCE_TYPE)
		}
	}
}

func processGCPInstanceGroupManager(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if instanceGroupManager, ok := entityJSONMap["instanceGroupManager"].(map[string]any); ok {
		if instanceGroup, ok := instanceGroupManager["instanceGroup"].(string); ok && len(instanceGroup) >= 0 {
			var instanceGroupID string
			splitName := strings.Split(instanceGroup, "/")
			for i, v := range splitName {
				if v == "projects" {
					instanceGroupID = strings.Join(splitName[i:], "/")
					break
				}
			}
			instanceGroupID = strings.ToLower(instanceGroupID)
			instanceGroupDocID := common.GenerateCombinedHashID(instanceGroupID, common.GCP_INSTANCEGROUP_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, instanceGroupID, instanceGroupDocID, common.GCP_INSTANCEGROUP_RESOURCE_TYPE)
		}
	}
	if instanceTemplate, ok := entityJSONMap["instanceTemplate"].(string); ok && len(instanceTemplate) > 0 {
		instanceTemplate = strings.ToLower(instanceTemplate)
		instanceTemplateDocID := common.GenerateCombinedHashID(instanceTemplate, common.GCP_CONTAINERINSTANCETEMPLATE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, instanceTemplate, instanceTemplateDocID, common.GCP_CONTAINERINSTANCETEMPLATE_RESOURCE_TYPE, RelatedResourceOpts{TransitiveResourceType: common.GCP_GKECLUSTER_RESOURCE_TYPE})
	}
}

func processGCPInstanceGroup(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if groupInstances, ok := entityJSONMap["instanceWithNamedPortsList"].([]any); ok {
		for _, groupInstance := range groupInstances {
			if instanceMap, ok := groupInstance.(map[string]any); ok {
				if instance, ok := instanceMap["instance"].(string); ok && len(instance) > 0 {
					var instanceID string
					splitName := strings.Split(instance, "/")
					for i, v := range splitName {
						if v == "projects" {
							instanceID = strings.Join(splitName[i:], "/")
							break
						}
					}
					instanceDocID := common.GenerateCombinedHashID(instanceID, common.GCP_INSTANCE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, instanceID, instanceDocID, common.GCP_INSTANCE_RESOURCE_TYPE)
				}
			}
		}
	}
}

func processGCPCloudStorage(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if bucket, ok := entityJSONMap["bucket"].(map[string]any); ok {
		if encryption, ok := bucket["encryption"].(map[string]any); ok {
			if kmsKey, ok := encryption["defaultKmsKeyName"].(string); ok && len(kmsKey) > 0 {
				kmsKey = strings.ToLower(kmsKey)
				kmsDocID := common.GenerateCombinedHashID(kmsKey, common.GCP_KMS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, kmsKey, kmsDocID, common.GCP_KMS_RESOURCE_TYPE)
			}
		}
	}
}

func processGCPBackendService(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if igs, ok := entityJSONMap["instanceGroups"].([]any); ok {
		for _, ig := range igs {
			if igString, ok := ig.(string); ok && len(igString) >= 0 {
				igString = strings.ToLower(igString)
				if strings.Contains(igString, "/networkendpointgroups/") {
					networkEndpointGroupID := igString
					networkEndpointGroupDocID := common.GenerateCombinedHashID(networkEndpointGroupID, common.GCP_NETWORKENDPOINTGROUP_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, networkEndpointGroupID, networkEndpointGroupDocID, common.GCP_NETWORKENDPOINTGROUP_RESOURCE_TYPE)
				} else if strings.Contains(igString, "/instancegroups/") {
					instanceGroupID := igString
					instanceGroupDocID := common.GenerateCombinedHashID(instanceGroupID, common.GCP_INSTANCEGROUP_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, instanceGroupID, instanceGroupDocID, common.GCP_INSTANCEGROUP_RESOURCE_TYPE)
				}
			}
		}
	}
	if loadBalancers, ok := entityJSONMap["loadBalancers"].([]any); ok {
		for _, loadBalancer := range loadBalancers {
			if loadBalancerID, ok := loadBalancer.(string); ok && len(loadBalancerID) >= 0 {
				loadBalancerID = strings.ToLower(loadBalancerID)
				loadBalancerDocID := common.GenerateCombinedHashID(loadBalancerID, common.LOADBALANCER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, loadBalancerID, loadBalancerDocID, common.LOADBALANCER_RESOURCE_TYPE)
			}
		}
	}
	if healthChecks, ok := entityJSONMap["healthChecks"].([]any); ok {
		for _, healthCheck := range healthChecks {
			if computeHealthCheckID, ok := healthCheck.(string); ok && len(computeHealthCheckID) > 0 {
				computeHealthCheckID = strings.ToLower(computeHealthCheckID)
				computeHealthCheckDocID := common.GenerateCombinedHashID(computeHealthCheckID, common.GCP_COMPUTEHEALTHCHECK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, computeHealthCheckID, computeHealthCheckDocID, common.GCP_COMPUTEHEALTHCHECK_RESOURCE_TYPE)
			}
		}
	}
}

func processGCPFunction(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if fn, ok := entityJSONMap["function"].(map[string]any); ok {
		if src, ok := fn["source"].(map[string]any); ok {
			if storageSource, ok := src["storageSource"].(map[string]any); ok {
				if bucket, ok := storageSource["bucket"].(string); ok && len(bucket) > 0 {
					bucket = strings.ToLower(bucket)
					bucketDocID := common.GenerateCombinedHashID(bucket, common.GCP_CLOUDSTORAGE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, bucket, bucketDocID, common.GCP_CLOUDSTORAGE_RESOURCE_TYPE)
				}
			}
		}
	}
}

func processGCPDockerImage(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	var repositoryID string
	splitName := strings.Split(resourceContextDoc.ResourceID, "/")
	for i, v := range splitName {
		if v == "dockerimages" {
			repositoryID = strings.Join(splitName[:i], "/")
			break
		}
	}
	repositoryDocID := common.GenerateCombinedHashID(repositoryID, common.GCP_ARTIFACTREPOSITORY_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
	AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, repositoryID, repositoryDocID, common.GCP_ARTIFACTREPOSITORY_RESOURCE_TYPE, RelatedResourceOpts{Parent: true, Oneway: true})
}

func processGCPVertexAIEndpoint(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if deployedModels, ok := entityJSONMap["deployedModels"].([]any); ok {
		for _, deployedModel := range deployedModels {
			if vertexModel, ok := deployedModel.(map[string]any); ok {
				if vertextModelID, ok := vertexModel["model"].(string); ok && len(vertextModelID) > 0 {
					vertextModelID = strings.ToLower(vertextModelID)
					vertextModelDocID := common.GenerateCombinedHashID(vertextModelID, common.GCP_VERTEXAIMODEL_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, vertextModelID, vertextModelDocID, common.GCP_VERTEXAIMODEL_RESOURCE_TYPE)
				}
			}
		}
	}
	if networkID, ok := entityJSONMap["network"].(string); ok && len(networkID) > 0 {
		networkID = strings.ToLower(networkID)
		networkDocID := common.GenerateCombinedHashID(networkID, common.GCP_NETWORK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, networkID, networkDocID, common.GCP_NETWORK_RESOURCE_TYPE)
	}
}

func processGCPDialogFlowAgent(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if agentTools, ok := entityJSONMap["tools"].([]any); ok {
		for _, agentTool := range agentTools {
			if agentToolMap, ok := agentTool.(map[string]any); ok {
				if dataStoreSpec, ok := agentToolMap["dataStoreSpec"].(map[string]any); ok {
					if dataStoreConnections, ok := dataStoreSpec["dataStoreConnections"].([]any); ok {
						for _, dataStoreConnection := range dataStoreConnections {
							if dataStoreConnectionMap, ok := dataStoreConnection.(map[string]any); ok {
								if dataStoreID, ok := dataStoreConnectionMap["dataStore"].(string); ok && len(dataStoreID) > 0 {
									dataStoreID = strings.ToLower(dataStoreID)
									dataStoreDocID := common.GenerateCombinedHashID(dataStoreID, common.GCP_DISCOVERYENGINEDATASTORE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
									AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, dataStoreID, dataStoreDocID, common.GCP_DISCOVERYENGINEDATASTORE_RESOURCE_TYPE)
								}
							}
						}
					}
				}
			}
		}
	}
	if dialogFlowPlaybookIDs, ok := entityJSONMap["playbookIds"].([]any); ok {
		for _, dialogFlowPlaybookID := range dialogFlowPlaybookIDs {
			if dialogFlowPlaybook, ok := dialogFlowPlaybookID.(string); ok && len(dialogFlowPlaybook) > 0 {
				dialogFlowPlaybook = strings.ToLower(dialogFlowPlaybook)
				dialogFlowPlaybookDocID := common.GenerateCombinedHashID(dialogFlowPlaybook, common.GCP_DIALOGFLOWAGENTPLAYBOOK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, dialogFlowPlaybook, dialogFlowPlaybookDocID, common.GCP_DIALOGFLOWAGENTPLAYBOOK_RESOURCE_TYPE)
			}
		}
	}
	if dialogFlowToolIDs, ok := entityJSONMap["toolIds"].([]any); ok {
		for _, dialogFlowToolID := range dialogFlowToolIDs {
			if dialogFlowTool, ok := dialogFlowToolID.(string); ok && len(dialogFlowTool) > 0 {
				dialogFlowTool = strings.ToLower(dialogFlowTool)
				dialogFlowToolDocID := common.GenerateCombinedHashID(dialogFlowTool, common.GCP_DIALOGFLOWAGENTTOOL_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, dialogFlowTool, dialogFlowToolDocID, common.GCP_DIALOGFLOWAGENTTOOL_RESOURCE_TYPE)
			}
		}
	}
	if dialogFlowWebookIDs, ok := entityJSONMap["webhookIds"].([]any); ok {
		for _, dialogFlowWebookID := range dialogFlowWebookIDs {
			if dialogFlowWebook, ok := dialogFlowWebookID.(string); ok && len(dialogFlowWebook) > 0 {
				dialogFlowWebook = strings.ToLower(dialogFlowWebook)
				dialogFlowWebookDocID := common.GenerateCombinedHashID(dialogFlowWebook, common.GCP_DIALOGFLOWAGENTWEBHOOK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, dialogFlowWebook, dialogFlowWebookDocID, common.GCP_DIALOGFLOWAGENTWEBHOOK_RESOURCE_TYPE)
			}
		}
	}
	if dialogFlowSecSettings, ok := entityJSONMap["securitySettings"].(string); ok && len(dialogFlowSecSettings) > 0 {
		dialogFlowSecSettings = strings.ToLower(dialogFlowSecSettings)
		dialogFlowSecSettingsDocID := common.GenerateCombinedHashID(dialogFlowSecSettings, common.GCP_DIALOGFLOWAGENTSECSETTINGS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, dialogFlowSecSettings, dialogFlowSecSettingsDocID, common.GCP_DIALOGFLOWAGENTSECSETTINGS_RESOURCE_TYPE)
	}
}

func processGCPDialogFlowAgentTool(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if discoveryEngineDataStoreIDs, ok := entityJSONMap["dataStoreIds"].([]any); ok {
		for _, discoveryEngineDataStoreID := range discoveryEngineDataStoreIDs {
			if discoveryEngineDataStore, ok := discoveryEngineDataStoreID.(string); ok && len(discoveryEngineDataStore) > 0 {
				discoveryEngineDataStore = strings.ToLower(discoveryEngineDataStore)
				discoveryEngineDataStoreDocID := common.GenerateCombinedHashID(discoveryEngineDataStore, common.GCP_DISCOVERYENGINEDATASTORE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, discoveryEngineDataStore, discoveryEngineDataStoreDocID, common.GCP_DISCOVERYENGINEDATASTORE_RESOURCE_TYPE)
			}
		}
	}
}

func processGCPDiscoveryEngineDataStore(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if buckets, ok := entityJSONMap["cloudStorageIds"].([]any); ok {
		for _, bucket := range buckets {
			if bucketID, ok := bucket.(string); ok && len(bucketID) > 0 {
				bucketID = strings.ToLower(bucketID)
				cloudStorage, err := common.GetCloudResourceDocumentForEntityIDAndType(bucketID, common.GCP_CLOUDSTORAGE_RESOURCE_TYPE, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
				if err != nil {
					return
				}
				if accountID, ok := cloudStorage["accountId"].(string); ok && len(accountID) > 0 {
					bucketDocID := common.GenerateCombinedHashID(bucketID, common.GCP_CLOUDSTORAGE_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, bucketID, bucketDocID, common.GCP_CLOUDSTORAGE_RESOURCE_TYPE)
				}
			}
		}
	}
}

func processGCPBigQueryTable(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if bigQueryDataSetID, ok := entityJSONMap["bigQueryDatasetId"].(string); ok && len(bigQueryDataSetID) > 0 {
		bigQueryDataSetID = strings.ToLower(bigQueryDataSetID)
		bigQueryDatasetDocID := common.GenerateCombinedHashID(bigQueryDataSetID, common.GCP_BIGQUERYDATASET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, bigQueryDataSetID, bigQueryDatasetDocID, common.GCP_BIGQUERYDATASET_RESOURCE_TYPE)
	}
}

func processGCPBigQueryDataset(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if kmsKey, ok := entityJSONMap["kmsKey"].(string); ok && len(kmsKey) > 0 {
		kmsKey = strings.ToLower(kmsKey)
		kmsDocID := common.GenerateCombinedHashID(kmsKey, common.GCP_KMS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, kmsKey, kmsDocID, common.GCP_KMS_RESOURCE_TYPE)
	}
}

func processGCPVertexFeatureView(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if bigQueryTableID, ok := entityJSONMap["bigQueryTableId"].(string); ok && len(bigQueryTableID) > 0 {
		bigQueryTableID = strings.ToLower(bigQueryTableID)
		bigQueryTableDocID := common.GenerateCombinedHashID(bigQueryTableID, common.GCP_BIGQUERYTABLE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, bigQueryTableID, bigQueryTableDocID, common.GCP_BIGQUERYTABLE_RESOURCE_TYPE)
	}
	if bigQueryDataSetID, ok := entityJSONMap["bigQueryDatasetId"].(string); ok && len(bigQueryDataSetID) > 0 {
		bigQueryDataSetID = strings.ToLower(bigQueryDataSetID)
		bigQueryDatasetDocID := common.GenerateCombinedHashID(bigQueryDataSetID, common.GCP_BIGQUERYDATASET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, bigQueryDataSetID, bigQueryDatasetDocID, common.GCP_BIGQUERYDATASET_RESOURCE_TYPE)
	}
	if vertexFeatureStoreID, ok := entityJSONMap["featureStoreId"].(string); ok && len(vertexFeatureStoreID) > 0 {
		vertexFeatureStoreID = strings.ToLower(vertexFeatureStoreID)
		vertexFeatureStoreDocID := common.GenerateCombinedHashID(vertexFeatureStoreID, common.GCP_VERTEXFEATURESTORE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, vertexFeatureStoreID, vertexFeatureStoreDocID, common.GCP_VERTEXFEATURESTORE_RESOURCE_TYPE)
	}
}

func processGCPVertexAIModel(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if buckets, ok := entityJSONMap["bucketNames"].([]any); ok {
		for _, bucket := range buckets {
			if bucketID, ok := bucket.(string); ok && len(bucketID) > 0 {
				bucketID = strings.ToLower(bucketID)
				cloudStorage, err := common.GetCloudResourceDocumentForEntityIDAndType(bucketID, common.GCP_CLOUDSTORAGE_RESOURCE_TYPE, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
				if err != nil {
					return
				}
				if accountID, ok := cloudStorage["accountId"].(string); ok && len(accountID) > 0 {
					bucketDocID := common.GenerateCombinedHashID(bucketID, common.GCP_CLOUDSTORAGE_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, bucketID, bucketDocID, common.GCP_CLOUDSTORAGE_RESOURCE_TYPE)
				}
			}
		}
	}
	if containerImages, ok := entityJSONMap["containerRegistryIds"].([]any); ok {
		for _, containerImage := range containerImages {
			if imageTag, ok := containerImage.(string); ok && len(imageTag) > 0 {
				imageTag = strings.ToLower(imageTag)
				repo := path.Dir(imageTag)
				tag := path.Base(imageTag)
				tagSplit := strings.Split(tag, ":")
				if len(tagSplit) > 1 {
					repo = repo + "/" + tagSplit[0]
					tag = strings.Join(tagSplit[1:], ":")
					imageDocs, err := common.GetCloudResourceDocumentFromEntityAndJSON(repo, common.GCP_DOCKERIMAGE_RESOURCE_TYPE, tag, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
					if err != nil {
						return
					}
					for _, imageDoc := range imageDocs {
						if imageEntityJSONString, ok := imageDoc["entityJson"].(string); ok && len(imageEntityJSONString) > 0 {
							imageEntityJSONMap := make(map[string]any)
							if err = json.Unmarshal([]byte(imageEntityJSONString), &imageEntityJSONMap); err != nil {
								continue
							}
							if tags, ok := imageEntityJSONMap["tags"].(string); ok && len(tags) > 0 {
								if strings.Contains(tags, tag) {
									if accountID, ok := imageDoc["accountId"].(string); ok && len(accountID) > 0 {
										if containerImageId, ok := imageDoc["entityId"].(string); ok {
											imageDocID := common.GenerateCombinedHashID(containerImageId, common.GCP_DOCKERIMAGE_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
											AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, containerImageId, imageDocID, common.GCP_DOCKERIMAGE_RESOURCE_TYPE)
										}
									}
									break
								}
							}
						}
					}
				}
			}
		}
	}
}

func processGCPVectorSearchIndexEndpoint(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if deployedIndexes, ok := entityJSONMap["deployedIndexes"].([]any); ok {
		for _, deployedIndex := range deployedIndexes {
			if deployedIndexMap, ok := deployedIndex.(map[string]any); ok {
				if searchIndex, ok := deployedIndexMap["index"].(string); ok && len(searchIndex) > 0 {
					searchIndex = strings.ToLower(searchIndex)
					searchIndexDocID := common.GenerateCombinedHashID(searchIndex, common.GCP_VECTORSEARCHINDEXES_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, searchIndex, searchIndexDocID, common.GCP_VECTORSEARCHINDEXES_RESOURCE_TYPE)
				}
			}
		}
	}
}

func processGCPNotebookRuntime(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if notebookTemplateRef, ok := entityJSONMap["notebookRuntimeTemplateRef"].(map[string]any); ok {
		if notebookTemplate, ok := notebookTemplateRef["notebookRuntimeTemplate"].(string); ok {
			notebookTemplate = strings.ToLower(notebookTemplate)
			notebookTemplateDocID := common.GenerateCombinedHashID(notebookTemplate, common.GCP_NOTEBOOKRUNTIMETEMPLATE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, notebookTemplate, notebookTemplateDocID, common.GCP_NOTEBOOKRUNTIMETEMPLATE_RESOURCE_TYPE)
		}
	}
}

func processGCPVertexAINotebookInstance(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if serviceAccounts, ok := entityJSONMap["serviceAccounts"].([]any); ok {
		for _, sa := range serviceAccounts {
			if serviceAccountMap, ok := sa.(map[string]any); ok {
				if serviceAccount, ok := serviceAccountMap["email"].(string); ok {
					serviceAccount = strings.ToLower(serviceAccount)
					serviceAccountDocID := common.GenerateCombinedHashID(serviceAccount, common.GCP_SERVICEACCOUNT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, serviceAccount, serviceAccountDocID, common.GCP_SERVICEACCOUNT_RESOURCE_TYPE)
				}
			}
		}
	}
	if subNetworks, ok := entityJSONMap["subnetIds"].([]any); ok {
		for _, subNetwork := range subNetworks {
			if subNetworkID, ok := subNetwork.(string); ok && len(subNetworkID) > 0 {
				subNetworkID = strings.ToLower(subNetworkID)
				subNetworkDocID := common.GenerateCombinedHashID(subNetworkID, common.GCP_SUBNETWORK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, subNetworkID, subNetworkDocID, common.GCP_SUBNETWORK_RESOURCE_TYPE)
			}
		}
	}
	if networks, ok := entityJSONMap["networkIds"].([]any); ok {
		for _, network := range networks {
			if networkID, ok := network.(string); ok && len(networkID) > 0 {
				networkID = strings.ToLower(networkID)
				networkDocID := common.GenerateCombinedHashID(networkID, common.GCP_NETWORK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, networkID, networkDocID, common.GCP_NETWORK_RESOURCE_TYPE)
			}
		}
	}
}

func processGCPVertexAITensorboard(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if bucketID, ok := entityJSONMap["blobStoragePath"].(string); ok && len(bucketID) > 0 {
		bucketID = strings.ToLower(bucketID)
		cloudStorage, err := common.GetCloudResourceDocumentForEntityIDAndType(bucketID, common.GCP_CLOUDSTORAGE_RESOURCE_TYPE, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
		if err != nil {
			return
		}
		if accountID, ok := cloudStorage["accountId"].(string); ok && len(accountID) > 0 {
			bucketDocID := common.GenerateCombinedHashID(bucketID, common.GCP_CLOUDSTORAGE_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, bucketID, bucketDocID, common.GCP_CLOUDSTORAGE_RESOURCE_TYPE)
		}
	}
}

func processGCPDocumentAIProcessor(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if processorVersions, ok := entityJSONMap["processorVersions"].([]any); ok {
		for _, processorVersion := range processorVersions {
			if processorVersionString, ok := processorVersion.(string); ok {
				processorVersionString = strings.ToLower(processorVersionString)
				processorVersionDocID := common.GenerateCombinedHashID(processorVersionString, common.GCP_DOCUMENTAIPROCESSORVERSION_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, processorVersionString, processorVersionDocID, common.GCP_DOCUMENTAIPROCESSORVERSION_RESOURCE_TYPE)
			}
		}
	}
}

func processGCPGKECluster(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if instanceGroupManagers, ok := entityJSONMap["instanceGroupManagers"].([]any); ok {
		for _, instanceGroupManager := range instanceGroupManagers {
			if instanceGroupManager, ok := instanceGroupManager.(string); ok {
				instanceGroupManagerDocID := common.GenerateCombinedHashID(instanceGroupManager, common.GCP_INSTANCEGROUPMANAGER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, instanceGroupManager, instanceGroupManagerDocID, common.GCP_INSTANCEGROUPMANAGER_RESOURCE_TYPE)
			}
		}
	}
}

func processGCPGKENamespace(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if clusterId, ok := entityJSONMap["clusterEntityId"].(string); ok {
		clusterDocID := common.GenerateCombinedHashID(clusterId, common.GCP_GKECLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, clusterId, clusterDocID, common.GCP_GKECLUSTER_RESOURCE_TYPE, RelatedResourceOpts{Parent: true, Oneway: true})
	}
	AssignRelatedResourcesFromSingleResourceCollector(resourceContext, resourceContextDoc, resourceDocID, entityJSONMap)
}

func processGCPGKEClusterRole(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if clusterId, ok := entityJSONMap["clusterEntityId"].(string); ok {
		clusterDocID := common.GenerateCombinedHashID(clusterId, common.GCP_GKECLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, clusterId, clusterDocID, common.GCP_GKECLUSTER_RESOURCE_TYPE, RelatedResourceOpts{Parent: true, Oneway: true})
	}
	AssignRelatedResourcesFromSingleResourceCollector(resourceContext, resourceContextDoc, resourceDocID, entityJSONMap)
}

func processGCPGKENamespaceRole(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if clusterId, ok := entityJSONMap["clusterEntityId"].(string); ok {
		clusterDocID := common.GenerateCombinedHashID(clusterId, common.GCP_GKECLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, clusterId, clusterDocID, common.GCP_GKECLUSTER_RESOURCE_TYPE, RelatedResourceOpts{Parent: true, Oneway: true})
	}
	if namespaceId, ok := entityJSONMap["namespace"].(string); ok {
		clusterDocID := common.GenerateCombinedHashID(namespaceId, common.GCP_GKECLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, namespaceId, clusterDocID, common.GCP_GKECLUSTER_RESOURCE_TYPE, RelatedResourceOpts{Parent: true, Oneway: true})
	}
	AssignRelatedResourcesFromSingleResourceCollector(resourceContext, resourceContextDoc, resourceDocID, entityJSONMap)
}

func processGCPGKEServiceAccount(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if clusterId, ok := entityJSONMap["clusterEntityId"].(string); ok {
		clusterDocID := common.GenerateCombinedHashID(clusterId, common.GCP_GKECLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, clusterId, clusterDocID, common.GCP_GKECLUSTER_RESOURCE_TYPE, RelatedResourceOpts{Parent: true, Oneway: true})
	}
	if namespaceId, ok := entityJSONMap["namespace"].(string); ok {
		clusterDocID := common.GenerateCombinedHashID(namespaceId, common.GCP_GKECLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, namespaceId, clusterDocID, common.GCP_GKECLUSTER_RESOURCE_TYPE, RelatedResourceOpts{Parent: true, Oneway: true})
	}
	AssignRelatedResourcesFromSingleResourceCollector(resourceContext, resourceContextDoc, resourceDocID, entityJSONMap)
}

func processGCPGKEReplicaSet(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if clusterId, ok := entityJSONMap["clusterEntityId"].(string); ok {
		clusterDocID := common.GenerateCombinedHashID(clusterId, common.GCP_GKECLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, clusterId, clusterDocID, common.GCP_GKECLUSTER_RESOURCE_TYPE, RelatedResourceOpts{Parent: true, Oneway: true})
	}
	if namespaceId, ok := entityJSONMap["namespace"].(string); ok {
		clusterDocID := common.GenerateCombinedHashID(namespaceId, common.GCP_GKECLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, namespaceId, clusterDocID, common.GCP_GKECLUSTER_RESOURCE_TYPE, RelatedResourceOpts{Parent: true, Oneway: true})
	}
	AssignRelatedResourcesFromSingleResourceCollector(resourceContext, resourceContextDoc, resourceDocID, entityJSONMap)
}

func processGCPGKEDaemonSet(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if clusterId, ok := entityJSONMap["clusterEntityId"].(string); ok {
		clusterDocID := common.GenerateCombinedHashID(clusterId, common.GCP_GKECLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, clusterId, clusterDocID, common.GCP_GKECLUSTER_RESOURCE_TYPE, RelatedResourceOpts{Parent: true, Oneway: true})
	}
	if namespaceId, ok := entityJSONMap["namespace"].(string); ok {
		clusterDocID := common.GenerateCombinedHashID(namespaceId, common.GCP_GKECLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, namespaceId, clusterDocID, common.GCP_GKECLUSTER_RESOURCE_TYPE, RelatedResourceOpts{Parent: true, Oneway: true})
	}
	AssignRelatedResourcesFromSingleResourceCollector(resourceContext, resourceContextDoc, resourceDocID, entityJSONMap)
}

func processGCPGKEService(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if namespaceId, ok := entityJSONMap["namespace"].(string); ok {
		clusterDocID := common.GenerateCombinedHashID(namespaceId, common.GCP_GKECLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, namespaceId, clusterDocID, common.GCP_GKECLUSTER_RESOURCE_TYPE, RelatedResourceOpts{Parent: true, Oneway: true})
	}
	AssignRelatedResourcesFromSingleResourceCollector(resourceContext, resourceContextDoc, resourceDocID, entityJSONMap)
}

func processGCPAppEngineService(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if appEngineApplication, ok := entityJSONMap["appName"].(string); ok {
		appEngineApplicationDocID := common.GenerateCombinedHashID(appEngineApplication, common.GCP_APPENGINEAPPLICATION_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, appEngineApplication, appEngineApplicationDocID, common.GCP_APPENGINEAPPLICATION_RESOURCE_TYPE)
	}
}

func processGCPAppEngineServiceVersion(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if appEngineApplication, ok := entityJSONMap["serviceName"].(string); ok {
		appEngineApplicationDocID := common.GenerateCombinedHashID(appEngineApplication, common.GCP_APPENGINEAPPLICATION_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
		AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, appEngineApplication, appEngineApplicationDocID, common.GCP_APPENGINEAPPLICATION_RESOURCE_TYPE)
	}
}

func processGCPContainerInstanceTemplate(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if networks, ok := entityJSONMap["networks"].([]any); ok {
		for _, network := range networks {
			if network, ok := network.(string); ok && len(network) > 0 {
				networkDocID := common.GenerateCombinedHashID(network, common.GCP_NETWORK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, network, networkDocID, common.GCP_NETWORK_RESOURCE_TYPE)
			}
		}
	}
	if subNetworks, ok := entityJSONMap["subNetworks"].([]any); ok {
		for _, subNetwork := range subNetworks {
			if subNetwork, ok := subNetwork.(string); ok && len(subNetwork) > 0 {
				subNetworkDocID := common.GenerateCombinedHashID(subNetwork, common.GCP_SUBNETWORK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, subNetwork, subNetworkDocID, common.GCP_SUBNETWORK_RESOURCE_TYPE)
			}
		}
	}
	var clusterLocation, clusterName string
	if tags, ok := entityJSONMap["tags"].([]any); ok {
		for _, tag := range tags {
			if tagMap, ok := tag.(map[string]any); ok {
				if tagKey, ok := tagMap["key"].(string); ok && len(tagKey) > 0 {
					if tagValue, ok := tagMap["value"].(string); ok && len(tagValue) > 0 {
						if tagKey == "goog-k8s-cluster-location" {
							clusterLocation = tagValue
						}
						if tagKey == "goog-k8s-cluster-name" {
							clusterName = tagValue
						}
					}
				}
			}
		}
	}
	if len(clusterName) > 0 && len(clusterLocation) > 0 {
		parts := strings.SplitN(resourceContextDoc.ResourceID, "/", 3)
		if len(parts) >= 2 && parts[0] == "projects" {
			clusterID := strings.ToLower("projects/" + parts[1] + "/locations/" + clusterLocation + "/clusters/" + clusterName)
			clusterDocID := common.GenerateCombinedHashID(clusterID, common.GCP_GKECLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, clusterID, clusterDocID, common.GCP_GKECLUSTER_RESOURCE_TYPE)
		}
	}
}

func processFirewallRulesForTarget(resourceContext *rcontext.ResourceContext, network string, targetKey string, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string) {
	firewallTargets, exists := resourceContext.GetGCPFirewallTargets(network)
	if !exists {
		return
	}
	firewallRuleIDs, ok := firewallTargets[targetKey]
	if !ok {
		return
	}
	for _, firewallRuleID := range firewallRuleIDs {
		firewallDoc, err := common.GetCloudResourceDocumentForEntityIDAndType(firewallRuleID, common.GCP_FIREWALL_RESOURCE_TYPE, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
		if err != nil {
			break
		}
		if accountID, ok := firewallDoc["accountId"].(string); ok && len(accountID) > 0 {
			firewallDocID := common.GenerateCombinedHashID(firewallRuleID, common.GCP_FIREWALL_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, firewallRuleID, firewallDocID, common.GCP_FIREWALL_RESOURCE_TYPE, RelatedResourceOpts{NonContextual: true})
		}
	}
}

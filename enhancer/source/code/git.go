package code

import (
	"encoding/json"
	"regexp"
	"strings"

	"github.com/precize/common"
	"github.com/precize/common/context"
	"github.com/precize/elastic"
	"github.com/precize/enhancer/context"
	"github.com/precize/enhancer/internal/email"
	"github.com/precize/enhancer/rcontext"
	"github.com/precize/enhancer/related"
	"github.com/precize/logger"
)

func IsRepoNameKey(tagKey string) bool {
	tagKey = strings.ToLower(tagKey)

	if _, ok := contextutils.RepoNameTagKeys[strings.ToLower(tagKey)]; ok {
		return true
	}

	for k := range contextutils.RepoNameTagKeys {
		if strings.Contains(strings.ToLower(tagKey), k) {
			return true
		}
	}

	for r := range contextutils.RepoNameTagKeysRegex {
		regex := regexp.MustCompile(r)
		if regex.MatchString(strings.ToLower(tagKey)) {
			return true
		}
	}

	return false
}

func IsFileNameKey(tagKey string) bool {
	tagKey = strings.ToLower(tagKey)

	if _, ok := contextutils.FileNameTagKeys[strings.ToLower(tagKey)]; ok {
		return true
	}

	for k := range contextutils.FileNameTagKeys {
		if strings.Contains(strings.ToLower(tagKey), k) {
			return true
		}
	}

	for r := range contextutils.FileNameTagKeysRegex {
		regex := regexp.MustCompile(r)
		if regex.MatchString(strings.ToLower(tagKey)) {
			return true
		}
	}

	return false
}

func IsCommitIDKey(tagKey string) bool {
	tagKey = strings.ToLower(tagKey)

	if _, ok := contextutils.CommitIDTagKeys[strings.ToLower(tagKey)]; ok {
		return true
	}

	for k := range contextutils.CommitIDTagKeys {
		if strings.Contains(strings.ToLower(tagKey), k) {
			return true
		}
	}

	for r := range contextutils.CommitIDTagKeysRegex {
		regex := regexp.MustCompile(r)
		if regex.MatchString(strings.ToLower(tagKey)) {
			return true
		}
	}

	return false
}

func GetCodeOwnerFromCommitIDs(commitIDs map[string]struct{}, resourceContext *rcontext.ResourceContext, resourceContextInsertDoc *common.ResourceContextInsertDoc) (err error) {

	var (
		commitIDSlice = make([]string, 0, len(commitIDs))
	)

	for commitID := range commitIDs {
		commitIDSlice = append(commitIDSlice, commitID)
	}

	gitCommitSearchQuery := `{"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"terms":{"commitId.keyword":["` + strings.Join(commitIDSlice, `","`) + `"]}}]}}}`
	commitDocs, err := elastic.ExecuteSearchQuery([]string{elastic.IAC_GIT_COMMITS_INDEX}, gitCommitSearchQuery)
	if err != nil {
		return err
	}

	for _, commitDoc := range commitDocs {
		commitDocBytes, err := json.Marshal(commitDoc)
		if err != nil {
			logger.Print(logger.ERROR, "Error marshalling alert data to JSON", []string{resourceContext.TenantID}, err)
			continue
		}

		var commitDataMap common.IACGitCommitDoc
		if err = json.Unmarshal(commitDocBytes, &commitDataMap); err != nil {
			logger.Print(logger.ERROR, "Error unmarshalling JSON", []string{resourceContext.TenantID}, err)
			continue
		}

		if len(commitDataMap.AuthorEmail) > 0 {
			username := commitDataMap.AuthorEmail
			name := ""
			userType := ""

			if len(commitDataMap.Author) > 0 {
				name = commitDataMap.Author

				if strings.HasSuffix(username, "@users.noreply.github.com") {
					modifiedEmail := common.GitIdentity(username, commitDataMap.Author, resourceContext.TenantID)
					if len(modifiedEmail) > 0 {
						username = modifiedEmail
					} else {
						username = emailutils.ProcessGithubPrivateEmail(username)
					}
				}
			}

			if len(commitDataMap.GitClient) > 0 {
				userType = commitDataMap.GitClient
			}

			if len(name) > 0 {
				username = emailutils.FormCompleteEmailFormat(name, username)
			}

			resourceContextInsertDoc.CommitInfo.FileName = commitDataMap.Filename
			resourceContextInsertDoc.CommitInfo.RepoName = commitDataMap.RepoName
			resourceContextInsertDoc.CommitInfo.CommitDocIDs = append(resourceContextInsertDoc.CommitInfo.CommitDocIDs, commitDoc["_id"].(string))

			resourceContextInsertDoc.ResourceOwnerTypes.CodeOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.CodeOwners,
				context.GetUserContextItem(resourceContext, username, common.ConvertToTitleCase(userType), "User has checked in IaC code in git repository relating to resource", "", resourceContextInsertDoc.Account, nil),
			)
		}
	}

	return
}

func PostProcessCommitContext(resourceContext *rcontext.ResourceContext, resourceContextDoc *common.ResourceContextInsertDoc, resourceDocID string) {

	if commitFile := resourceContextDoc.CommitInfo.FileName; len(commitFile) > 0 {

		commitFileDocID := contextutils.INTERNAL_PREFIX + contextutils.COMMIT_FILE_SUBSTRING + resourceContextDoc.CommitInfo.FileName

		related.AssignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
			commitFile, commitFileDocID, common.PRECIZEINTERNAL_COMMITFILE_RESOURCE_TYPE)
	}
}

package attribute

import (
	"strings"

	"github.com/precize/common"
	"github.com/precize/common/context"
	"github.com/precize/enhancer/context"
	"github.com/precize/enhancer/internal/email"
	"github.com/precize/enhancer/rcontext"
)

func IsDescriptionKey(tagKey string) bool {

	if _, ok := contextutils.DescriptionTagKeys[strings.ToLower(tagKey)]; ok {
		return true
	}

	for key := range contextutils.DescriptionTagKeys {
		if strings.Contains(strings.ToLower(tagKey), key) {
			return true
		}
	}
	return false
}

func GetContextFromDescription(description string, resourceContext *rcontext.ResourceContext, resourceContextInsertDoc *common.ResourceContextInsertDoc) {

	var (
		uniqueOwners        = make(map[string]struct{})
		uniqueDeployments   = make(map[string]struct{})
		uniqueSoftwares     = make(map[string]struct{})
		uniqueApps          = make(map[string]struct{})
		uniqueCompliances   = make(map[string]struct{})
		uniqueSensitivities = make(map[string]struct{})
		uniqueTeams         = make(map[string]struct{})
	)

	if softwareNames := context.GetSoftwareNameListFromValue(description); len(softwareNames) > 0 {

		for _, softwareName := range softwareNames {
			if _, ok := uniqueSoftwares[softwareName]; !ok {

				uniqueSoftwares[softwareName] = struct{}{}

				resourceContextInsertDoc.ResourceSoftwareTypes.DerivedSoftware = append(
					resourceContextInsertDoc.ResourceSoftwareTypes.DerivedSoftware,
					common.ResourceContextItem{
						Name: softwareName,
						Type: common.DESC_SOFTWARE_TYPE,
					},
				)
			}
		}
	}

	if appNames := context.GetAppNameListFromValue(description, rcontext.WithResourceType(resourceContextInsertDoc.ResourceType)); len(appNames) > 0 {

		for _, appName := range appNames {
			if _, ok := uniqueApps[appName]; !ok {

				uniqueApps[appName] = struct{}{}

				resourceContextInsertDoc.ResourceAppTypes.DerivedApp = append(
					resourceContextInsertDoc.ResourceAppTypes.DerivedApp,
					common.ResourceContextItem{
						Name: appName,
						Type: common.DESC_APP_TYPE,
					},
				)
			}
		}
	}

	if teamNames := context.GetTeamNameListFromValue(description); len(teamNames) > 0 {

		for _, teamName := range teamNames {
			if _, ok := uniqueTeams[teamName]; !ok {

				uniqueTeams[teamName] = struct{}{}

				resourceContextInsertDoc.ResourceTeamTypes.DerivedTeam = append(
					resourceContextInsertDoc.ResourceTeamTypes.DerivedTeam,
					common.ResourceContextItem{
						Name: teamName,
						Type: common.DESC_TEAM_TYPE,
					},
				)
			}
		}
	}

	if deploymentNames := context.GetDeploymentNamesFromValue(description); len(deploymentNames) > 0 {

		for _, deploymentName := range deploymentNames {
			if _, ok := uniqueDeployments[deploymentName]; !ok {

				uniqueDeployments[deploymentName] = struct{}{}

				resourceContextInsertDoc.ResourceDeploymentTypes.DerivedDeployment = append(
					resourceContextInsertDoc.ResourceDeploymentTypes.DerivedDeployment,
					common.ResourceContextItem{
						Name: deploymentName,
						Type: common.DESC_DEPLOYMENT_TYPE,
					},
				)
			}
		}
	}

	if sensitivityNames := context.GetSensitivityNameListFromValue(description); len(sensitivityNames) > 0 {

		for _, sensitivityName := range sensitivityNames {
			if _, ok := uniqueSensitivities[sensitivityName]; !ok {

				uniqueSensitivities[sensitivityName] = struct{}{}

				resourceContextInsertDoc.ResourceSensitivityTypes.DerivedSensitivity = append(
					resourceContextInsertDoc.ResourceSensitivityTypes.DerivedSensitivity,
					common.ResourceContextItem{
						Name: sensitivityName,
						Type: common.DESC_SENSITIVITY_TYPE,
					},
				)
			}
		}
	}

	if complianceNames := context.GetComplianceNameListFromValue(description); len(complianceNames) > 0 {

		for _, complianceName := range complianceNames {
			if _, ok := uniqueCompliances[complianceName]; !ok {

				uniqueCompliances[complianceName] = struct{}{}

				resourceContextInsertDoc.ResourceComplianceTypes.DerivedCompliance = append(
					resourceContextInsertDoc.ResourceComplianceTypes.DerivedCompliance,
					common.ResourceContextItem{
						Name: complianceName,
						Type: common.DESC_COMPLIANCE_TYPE,
					},
				)
			}
		}
	}

	users := emailutils.SeparateEmailsFromDescription(description)

	names := context.GetNamesFromDescription(description, resourceContext.TenantID)

	names = emailutils.RemoveNamesOfTaggedEmails(names, users)
	users = append(users, names...)

	for _, user := range users {
		if _, ok := uniqueOwners[user]; !ok {

			uniqueOwners[user] = struct{}{}

			resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
				resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
				context.GetUserContextItem(resourceContext, user, common.DESC_USER_TYPE, "User has been derived from resource description", "", resourceContextInsertDoc.Account, nil),
			)
		}
	}
}

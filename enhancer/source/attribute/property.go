package attribute

import (
	"strings"

	"github.com/precize/common"
	contextutils "github.com/precize/common/context"
	"github.com/precize/enhancer/activity"
	"github.com/precize/enhancer/context"
	emailutils "github.com/precize/enhancer/internal/email"
	resourceutils "github.com/precize/enhancer/internal/resource"
	"github.com/precize/enhancer/rcontext"
)

func GetResourceInbuiltProperty(resourceContext *rcontext.ResourceContext, entityJSON map[string]any,
	resourceContextInsertDoc *common.ResourceContextInsertDoc) {

	switch resourceContextInsertDoc.ResourceType {

	case common.AWS_ACCOUNT_RESOURCE_TYPE:
		if organization, ok := entityJSON["organization"].(map[string]any); ok {
			if masterEmail, ok := organization["masterAccountEmail"].(string); ok && len(masterEmail) > 0 {
				resourceContextInsertDoc.ResourceOwnerTypes.InheritedOwners = append(
					resourceContextInsertDoc.ResourceOwnerTypes.InheritedOwners,
					context.GetUserContextItem(resourceContext, masterEmail, common.ORG_ACCOUNT_OWNER_USER_TYPE, "", "", resourceContextInsertDoc.Account, nil),
				)
			}
		}

	case common.AWS_S3_RESOURCE_TYPE:

		s3Owner := ""

		if bucketOwner, ok := entityJSON["bucketOwner"].(string); ok && len(bucketOwner) > 0 {
			s3Owner = bucketOwner
		}

		if len(s3Owner) <= 0 {
			if bucket, ok := entityJSON["bucket"].(map[string]any); ok {
				if bucketOwnerObj, ok := bucket["owner"].(map[string]any); ok {
					if name, ok := bucketOwnerObj["displayName"].(string); ok && len(name) > 0 {
						s3Owner = name
					}
				}
			}
		}

		if len(s3Owner) > 0 {

			rctxItem := context.GetUserContextItem(resourceContext, s3Owner, common.RESOURCE_OWNER_USER_TYPE, "", "", resourceContextInsertDoc.Account, nil)
			resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
				resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
				rctxItem,
			)

			if _, err := common.ParseAddress(rctxItem.Name); err != nil {
				// valid email could not be derived
				// try to derive email from the owner itself

				if len(resourceContext.PrimaryDomains) > 0 {

					var (
						isValidEmail, isValidEmailDerived bool
					)

					primaryDomain := resourceContext.PrimaryDomains[0]
					email := s3Owner + "@" + primaryDomain
					emailName := common.GetFormattedNameFromEmail(email)

					if _, isValidEmailDerived = resourceContext.GetEmailStatus(email); !isValidEmailDerived {
						emailNameMap := map[string]string{
							email: emailName,
						}

						emailStatusMap, err := emailutils.CheckEmailValidity(emailNameMap, true, resourceContext)
						if err != nil {
							return
						}

						if len(emailStatusMap) > 0 {
							isValidEmail = emailStatusMap[email]
							resourceContext.SetEmailStatus(email, isValidEmail)
							_, isValidEmailDerived = emailStatusMap[email]
						}
					}

					if isValidEmailDerived {
						rctxItem := context.GetUserContextItem(resourceContext, email, common.RESOURCE_OWNER_USER_TYPE, "", "", resourceContextInsertDoc.Account, nil)
						resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
							resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
							rctxItem,
						)
					}
				}
			}
		}

	case common.AWS_LAMBDA_RESOURCE_TYPE:
		if funcConfig, ok := entityJSON["functionConfiguration"].(map[string]any); ok {
			if env, ok := funcConfig["environment"].(map[string]any); ok && len(env) > 0 {
				if variables, ok := env["variables"].(map[string]any); ok && len(variables) > 0 {

					for key, value := range variables {
						if key == "KEEPER_USER" {

							if email, ok := value.(string); ok {

								// Format: "<user>+vaultadmin@<domain>",
								atIndex := strings.LastIndex(email, "@")
								if atIndex > 0 {
									username := email[:atIndex]
									domain := email[atIndex:]

									plusIndex := strings.Index(username, "+")
									if plusIndex > 0 {
										email = username[:plusIndex] + domain
									}
								}

								resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
									resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
									context.GetUserContextItem(resourceContext, email, common.RESOURCE_OWNER_USER_TYPE, "", "", resourceContextInsertDoc.Account, nil),
								)
							}
						}
					}

				}
			}
		}

	case common.AWS_EC2_RESOURCE_TYPE:

		if rightSizingRecommendation, ok := entityJSON["rightsizingRecommendation"].(map[string]any); ok {
			if currentInstance, ok := rightSizingRecommendation["currentInstance"].(map[string]any); ok {
				if tags, ok := currentInstance["tags"].([]any); ok {
					for _, tag := range tags {
						if tagMap, ok := tag.(map[string]any); ok {
							if key, ok := tagMap["key"].(string); ok {

								if key == "aws:createdBy" || key == "aws:modifiedBy" || key == "aws:updatedBy" {
									if values, ok := tagMap["values"].([]any); ok {

										for _, value := range values {
											if valueString, ok := value.(string); ok {
												// Format is typically "AssumedRole:<Role Id>:<User>" or "User:<user>"
												parts := strings.Split(valueString, ":")
												username := parts[len(parts)-1]

												rctxItem := context.GetUserContextItem(resourceContext, username, common.RESOURCE_OWNER_USER_TYPE, "", "", resourceContextInsertDoc.Account, nil)

												if key == "aws:createdBy" {
													rctxItem.OwnerSubType = common.CREATOR_RCTXITEM_OWNERTYPE
												}

												resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
													resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
													rctxItem,
												)
											}
										}
									}
								}
							}
						}
					}
				}

			}
		}

		if instanceProperties, ok := entityJSON["instance"].(map[string]any); ok {
			if clientToken, ok := instanceProperties["clientToken"].(string); ok && len(clientToken) > 0 && strings.Contains(strings.ToLower(clientToken), "terraform") {

				resourceContextInsertDoc.ResourceDeploymentTypes.DerivedDeployment = append(
					resourceContextInsertDoc.ResourceDeploymentTypes.DerivedDeployment,
					common.ResourceContextItem{
						Name: contextutils.TERRAFORM_DEPLOYMENT,
						Type: common.RESOURCEPROPERTY_DEPLOYMENT_TYPE,
					},
				)
			}

		}

	case common.AZURE_TENANT_RESOURCE_TYPE:
		if aDInfo, ok := entityJSON["activeDirectoryInfo"].(map[string]any); ok && len(aDInfo) > 0 {
			if contactInfo, ok := aDInfo["technicalNotificationMails"].([]any); ok && len(contactInfo) > 0 {
				for _, email := range contactInfo {
					if emailString, ok := email.(string); ok {
						resourceContextInsertDoc.ResourceOwnerTypes.OpsOwners = append(
							resourceContextInsertDoc.ResourceOwnerTypes.OpsOwners,
							context.GetUserContextItem(resourceContext, emailString, common.RESOURCE_OPS_CONTACT_USER_TYPE, "", "", resourceContextInsertDoc.Account, nil),
						)
					}
				}
			}
		}

	case common.AZURE_RG_RESOURCE_TYPE:

		if policies, ok := entityJSON["policies"].(map[string]any); ok {

			if systemData, ok := policies["systemData"].(map[string]any); ok {

				if lastModifiedBy, ok := systemData["lastModifiedBy"].(string); ok {
					if lastModifiedByType, ok := systemData["lastModifiedByType"].(string); ok {
						if len(lastModifiedBy) > 0 {
							userType := common.RESOURCE_OWNER_USER_TYPE
							identityID := ""
							if lastModifiedByType == "Application" {
								identityID = lastModifiedBy
								lastModifiedBy = resourceutils.GetAzureAppName(resourceContext, lastModifiedBy, "") + contextutils.APP_USER_SUFFIX
								userType = common.RESOURCE_OWNERAPP_USER_TYPE

								for _, azureTenantID := range resourceContext.AzureTenantIDs {
									uniqueIdentities := make(map[string]struct{})
									activity.GetApplicationOwnersForPolicy(resourceContext, identityID, azureTenantID, resourceContextInsertDoc, uniqueIdentities,
										"User owned Application "+identityID+" has been specified as an owner in resource properties", 1)
								}
							}
							resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
								resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
								context.GetUserContextItem(resourceContext, lastModifiedBy, userType, "", identityID, resourceContextInsertDoc.Account, nil),
							)
						}
					}
				}

				if createdBy, ok := systemData["createdBy"].(string); ok {
					if createdByType, ok := systemData["createdByType"].(string); ok {
						if len(createdBy) > 0 {
							userType := common.RESOURCE_OWNER_USER_TYPE
							identityID := ""
							if createdByType == "Application" {
								identityID = createdBy
								createdBy = resourceutils.GetAzureAppName(resourceContext, createdBy, "") + contextutils.APP_USER_SUFFIX
								userType = common.RESOURCE_OWNERAPP_USER_TYPE

								for _, azureTenantID := range resourceContext.AzureTenantIDs {
									uniqueIdentities := make(map[string]struct{})
									activity.GetApplicationOwnersForPolicy(resourceContext, identityID, azureTenantID, resourceContextInsertDoc, uniqueIdentities,
										"User owned Application "+identityID+" has been specified as an owner in resource properties", 1)
								}
							}

							rctxItem := context.GetUserContextItem(resourceContext, createdBy, userType, "", identityID, resourceContextInsertDoc.Account, nil)
							rctxItem.OwnerSubType = common.CREATOR_RCTXITEM_OWNERTYPE
							resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
								resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
								rctxItem,
							)
						}
					}
				}
			}
		}

	case common.AZURE_GRAPHAPP_RESOURCE_TYPE:

		if ownersInterface, ok := entityJSON["owners"].([]any); ok {
			for _, ownerInterface := range ownersInterface {
				if ownerMap, ok := ownerInterface.(map[string]any); ok {
					if userPrincipalName, ok := ownerMap["userPrincipalName"].(string); ok && len(userPrincipalName) > 0 {
						resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(
							resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
							context.GetUserContextItem(resourceContext, userPrincipalName, common.RESOURCE_OWNER_USER_TYPE, "", "", resourceContextInsertDoc.Account, nil),
						)
					}
				}
			}
		}

	case common.AZURE_MLWORKSPACEDEPLOYMENTS_RESOURCE_TYPE, common.AZURE_CONTAINERREGISTRY_RESOURCE_TYPE:

		if systemData, ok := entityJSON["systemData"].(map[string]any); ok {

			if lastModifiedBy, ok := systemData["lastModifiedBy"].(string); ok && len(lastModifiedBy) > 0 {
				resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
					resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
					context.GetUserContextItem(resourceContext, lastModifiedBy, common.RESOURCE_OWNER_USER_TYPE, "", "", resourceContextInsertDoc.Account, nil),
				)
			}

			if createdBy, ok := systemData["createdBy"].(string); ok && len(createdBy) > 0 {
				rctxItem := context.GetUserContextItem(resourceContext, createdBy, common.RESOURCE_OWNER_USER_TYPE, "", "", resourceContextInsertDoc.Account, nil)
				rctxItem.OwnerSubType = common.CREATOR_RCTXITEM_OWNERTYPE
				resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
					resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
					rctxItem,
				)
			}
		}

	case common.AZURE_OPENAIMODEL_RESOURCE_TYPE:

		if model, ok := entityJSON["model"].(map[string]any); ok {

			if systemData, ok := model["systemData"].(map[string]any); ok {

				if lastModifiedBy, ok := systemData["lastModifiedBy"].(string); ok {
					if lastModifiedByType, ok := systemData["lastModifiedByType"].(string); ok {
						if len(lastModifiedBy) > 0 {
							userType := common.RESOURCE_OWNER_USER_TYPE
							identityID := ""
							if lastModifiedByType == "Application" {
								identityID = lastModifiedBy
								lastModifiedBy = resourceutils.GetAzureAppName(resourceContext, lastModifiedBy, "") + contextutils.APP_USER_SUFFIX
								userType = common.RESOURCE_OWNERAPP_USER_TYPE

								for _, azureTenantID := range resourceContext.AzureTenantIDs {
									uniqueIdentities := make(map[string]struct{})
									activity.GetApplicationOwnersForPolicy(resourceContext, identityID, azureTenantID, resourceContextInsertDoc, uniqueIdentities,
										"User owned Application "+identityID+" has been specified as an owner in resource properties", 1)
								}
							}
							resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
								resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
								context.GetUserContextItem(resourceContext, lastModifiedBy, userType, "", identityID, resourceContextInsertDoc.Account, nil),
							)
						}
					}
				}

				if createdBy, ok := systemData["createdBy"].(string); ok {
					if createdByType, ok := systemData["createdByType"].(string); ok {
						if len(createdBy) > 0 {
							userType := common.RESOURCE_OWNER_USER_TYPE
							identityID := ""
							if createdByType == "Application" {
								identityID = createdBy
								createdBy = resourceutils.GetAzureAppName(resourceContext, createdBy, "") + contextutils.APP_USER_SUFFIX
								userType = common.RESOURCE_OWNERAPP_USER_TYPE

								for _, azureTenantID := range resourceContext.AzureTenantIDs {
									uniqueIdentities := make(map[string]struct{})
									activity.GetApplicationOwnersForPolicy(resourceContext, identityID, azureTenantID, resourceContextInsertDoc, uniqueIdentities,
										"User owned Application "+identityID+" has been specified as an owner in resource properties", 1)
								}
							}

							rctxItem := context.GetUserContextItem(resourceContext, createdBy, userType, "", identityID, resourceContextInsertDoc.Account, nil)
							rctxItem.OwnerSubType = common.CREATOR_RCTXITEM_OWNERTYPE
							resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
								resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
								rctxItem,
							)
						}
					}
				}
			}
		}

	case common.AZURE_MLWORKSPACEMODELS_RESOURCE_TYPE:

		if lastModifiedBy, ok := entityJSON["lastModifiedBy"].(map[string]any); ok {
			if userName, ok := lastModifiedBy["userName"].(string); ok && len(userName) > 0 {
				resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
					resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
					context.GetUserContextItem(resourceContext, userName, common.RESOURCE_OWNER_USER_TYPE, "", "", resourceContextInsertDoc.Account, nil),
				)
			}
		}

		if createdBy, ok := entityJSON["createdBy"].(map[string]any); ok {
			if userName, ok := createdBy["userName"].(string); ok && len(userName) > 0 {

				rctxItem := context.GetUserContextItem(resourceContext, userName, common.RESOURCE_OWNER_USER_TYPE, "", "", resourceContextInsertDoc.Account, nil)
				rctxItem.OwnerSubType = common.CREATOR_RCTXITEM_OWNERTYPE
				resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
					resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
					rctxItem,
				)
			}
		}

	case common.AZURE_SPEECHSERVICE_RESOURCE_TYPE:

		if lastModifiedBy, ok := entityJSON["lastModifiedBy"].(string); ok && len(lastModifiedBy) > 0 {
			resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
				resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
				context.GetUserContextItem(resourceContext, lastModifiedBy, common.RESOURCE_OWNER_USER_TYPE, "", "", resourceContextInsertDoc.Account, nil),
			)
		}

		if createdBy, ok := entityJSON["createdBy"].(string); ok && len(createdBy) > 0 {
			rctxItem := context.GetUserContextItem(resourceContext, createdBy, common.RESOURCE_OWNER_USER_TYPE, "", "", resourceContextInsertDoc.Account, nil)
			rctxItem.OwnerSubType = common.CREATOR_RCTXITEM_OWNERTYPE
			resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
				resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
				rctxItem,
			)
		}

	case common.AZURE_MGMTGRP_RESOURCE_TYPE:
		if updatedBy, ok := entityJSON["updatedBy"].(string); ok {
			resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
				resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
				context.GetUserContextItem(resourceContext, updatedBy, common.RESOURCE_OWNER_USER_TYPE, "", "", resourceContextInsertDoc.Account, nil),
			)
		}

	case common.AZURE_BUDGET_RESOURCE_TYPE:

		if properties, ok := entityJSON["properties"].(map[string]any); ok {
			if notifications, ok := properties["notifications"].(map[string]any); ok {
				for _, notification := range notifications {
					if notificationMap, ok := notification.(map[string]any); ok {
						if contactEmails, ok := notificationMap["contactEmails"].([]any); ok {
							for _, contactEmail := range contactEmails {
								if email, ok := contactEmail.(string); ok && len(email) > 0 {
									resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
										resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
										context.GetUserContextItem(resourceContext, email, common.RESOURCE_OWNER_USER_TYPE, "", "", resourceContextInsertDoc.Account, nil),
									)
								}
							}
						}
					}
				}
			}
		}

	case common.GCP_BIGQUERYTABLE_RESOURCE_TYPE:

		if accesses, ok := entityJSON["access"].([]any); ok {
			for _, access := range accesses {
				if accessMap, ok := access.(map[string]any); ok {
					if userEmail, ok := accessMap["userByEmail"].(string); ok && len(userEmail) > 0 {
						resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(
							resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
							context.GetUserContextItem(resourceContext, userEmail, common.RESOURCE_OWNER_USER_TYPE, "", "", resourceContextInsertDoc.Account, nil),
						)
					}
				}
			}
		}

	case common.GCP_BIGQUERYDATASET_RESOURCE_TYPE:

		if accesses, ok := entityJSON["access"].([]any); ok {
			for _, access := range accesses {
				if accessMap, ok := access.(map[string]any); ok {
					if userEmail, ok := accessMap["userByEmail"].(string); ok && len(userEmail) > 0 && !strings.HasSuffix(userEmail, ".gserviceaccount.com") {
						resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(
							resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
							context.GetUserContextItem(resourceContext, userEmail, common.RESOURCE_OWNER_USER_TYPE, "", "", resourceContextInsertDoc.Account, nil),
						)
					}
				}
			}
		}

	case common.GCP_NOTEBOOKRUNTIME_RESOURCE_TYPE:

		if runtimeUser, ok := entityJSON["runtimeUser"].(string); ok && len(runtimeUser) > 0 {
			resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(
				resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
				context.GetUserContextItem(resourceContext, runtimeUser, common.RESOURCE_OWNER_USER_TYPE, "", "", resourceContextInsertDoc.Account, nil),
			)
		}

	case common.OPENAI_APIKEY_RESOURCE_TYPE:
		if apiKeyOwner, ok := entityJSON["owner"].(map[string]any); ok {
			if ownerType, ok := apiKeyOwner["type"].(string); ok && ownerType == "user" {
				if userMap, ok := apiKeyOwner["user"].(map[string]any); ok {
					user, _ := userMap["email"].(string)
					if len(user) <= 0 {
						user, _ = userMap["name"].(string)
					}
					resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
						resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
						context.GetUserContextItem(resourceContext, user, common.RESOURCE_OWNER_USER_TYPE, "", "", resourceContextInsertDoc.Account, nil),
					)
				}
			}
		}

	case common.APP_RESOURCE_TYPE, common.AWS_AISAGEMAKERMODELTYPE_RESOURCE_TYPE:

		precizeOwner := emailutils.FormCompleteEmailFormat("Precize Support", "<EMAIL>")

		resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(
			resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
			context.GetUserContextItem(resourceContext, precizeOwner, common.PRECIZE_DETECTED_USER_TYPE, "", "", resourceContextInsertDoc.Account, nil),
		)

	case common.AZURE_ADUSER_RESOURCE_TYPE, common.AWS_IAM_USER_RESOURCE_TYPE, common.AWS_SSOUSER_RESOURCE_TYPE, common.GCP_IAM_RESOURCE_TYPE, common.AWS_ROOTUSER_RESOURCE_TYPE:
		if hasConsoleLogin, ok := entityJSON["hasLoginProfile"].(bool); ok {
			resourceContextInsertDoc.HasConsoleLogin = hasConsoleLogin
		}

	case common.AZURE_DEVICE_RESOURCE_TYPE:

		registeredDeviceEmail := make(map[string]struct{})

		if registeredOwners, ok := entityJSON["registeredOwners"].([]any); ok {
			for _, registeredOwner := range registeredOwners {
				if registeredOwnerMap, ok := registeredOwner.(map[string]any); ok {
					if userPrincipal, ok := registeredOwnerMap["userPrincipalName"].(string); ok {
						registeredDeviceEmail[userPrincipal] = struct{}{}
						resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(
							resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
							context.GetUserContextItem(resourceContext, userPrincipal, common.RESOURCE_OWNER_USER_TYPE, "", "", resourceContextInsertDoc.Account, nil),
						)
					}
				}
			}
		}

		if registeredUsers, ok := entityJSON["registeredUsers"].([]any); ok {
			for _, registeredUser := range registeredUsers {
				if registeredUserMap, ok := registeredUser.(map[string]any); ok {
					if userPrincipal, ok := registeredUserMap["userPrincipalName"].(string); ok {
						if _, ok := registeredDeviceEmail[userPrincipal]; !ok {
							resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
								resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
								context.GetUserContextItem(resourceContext, userPrincipal, common.RESOURCE_OWNER_USER_TYPE, "", "", resourceContextInsertDoc.Account, nil),
							)
						}
					}
				}
			}
		}

	case common.AWS_GLUECATALOGTABLE_RESOURCE_TYPE:

		if createdBy, ok := entityJSON["createdBy"].(string); ok && len(createdBy) > 0 {

			if strings.Contains(createdBy, "assumed-role/") {
				splitString := strings.Split(createdBy, "/")
				if len(splitString) >= 2 {
					iamRole := strings.ToLower(splitString[len(splitString)-2])
					sessionName := strings.ToLower(splitString[len(splitString)-1])

					if ssoUser, ok := resourceContext.GetChildPrimaryEmail(sessionName); ok && len(ssoUser) > 0 {

						rctxItem := context.GetUserContextItem(resourceContext, ssoUser, common.RESOURCE_OWNER_USER_TYPE, "User has performed activities on the resource via Assumed Role "+iamRole, ssoUser, resourceContextInsertDoc.Account, nil)
						rctxItem.OwnerSubType = common.CREATOR_RCTXITEM_OWNERTYPE
						resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
							resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
							rctxItem,
						)
					} else {

						uniqueIdentities := make(map[string]struct{})
						// Limitation today - Human owner of role propagated only if role is in same account
						activity.GetAWSIAMEntityOwnersForPolicy(resourceContext, iamRole, resourceContextInsertDoc.Account, common.AWS_IAM_ROLE_RESOURCE_TYPE, resourceContextInsertDoc, uniqueIdentities, "User owned IAM Role "+iamRole+" has been specified as an owner in resource properties", 1)

						rctxItem := context.GetUserContextItem(resourceContext, iamRole+contextutils.IAM_ROLE_SUFFIX, common.RESOURCE_OWNERIAMROLE_USER_TYPE, "", iamRole, resourceContextInsertDoc.Account, nil)
						rctxItem.OwnerSubType = common.CREATOR_RCTXITEM_OWNERTYPE
						resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
							resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
							rctxItem,
						)
					}
				}
			}
		}

	default:

		if resourceContextInsertDoc.ServiceID == common.AZURE_SERVICE_ID_INT {

			if systemData, ok := entityJSON["systemData"].(map[string]any); ok {

				if lastModifiedBy, ok := systemData["lastModifiedBy"].(string); ok {
					userType := common.RESOURCE_OWNER_USER_TYPE
					if lastModifiedByType, ok := systemData["lastModifiedByType"].(string); ok {
						if len(lastModifiedBy) > 0 {
							identityID := ""
							if lastModifiedByType == "Application" {
								identityID = lastModifiedBy
								lastModifiedBy = resourceutils.GetAzureAppName(resourceContext, lastModifiedBy, "") + contextutils.APP_USER_SUFFIX
								userType = common.RESOURCE_OWNERAPP_USER_TYPE

								for _, azureTenantID := range resourceContext.AzureTenantIDs {
									uniqueIdentities := make(map[string]struct{})
									activity.GetApplicationOwnersForPolicy(resourceContext, identityID, azureTenantID, resourceContextInsertDoc, uniqueIdentities,
										"User owned Application "+identityID+" has been specified as an owner in resource properties", 1)
								}
							}
							resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
								resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
								context.GetUserContextItem(resourceContext, lastModifiedBy, userType, "", identityID, resourceContextInsertDoc.Account, nil),
							)
						}
					} else {
						resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
							resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
							context.GetUserContextItem(resourceContext, lastModifiedBy, userType, "", "", resourceContextInsertDoc.Account, nil),
						)
					}
				}

				if createdBy, ok := systemData["createdBy"].(string); ok {
					userType := common.RESOURCE_OWNER_USER_TYPE
					if createdByType, ok := systemData["createdByType"].(string); ok {
						if len(createdBy) > 0 {
							identityID := ""
							if createdByType == "Application" {
								identityID = createdBy
								createdBy = resourceutils.GetAzureAppName(resourceContext, createdBy, "") + contextutils.APP_USER_SUFFIX
								userType = common.RESOURCE_OWNERAPP_USER_TYPE

								for _, azureTenantID := range resourceContext.AzureTenantIDs {
									uniqueIdentities := make(map[string]struct{})
									activity.GetApplicationOwnersForPolicy(resourceContext, identityID, azureTenantID, resourceContextInsertDoc, uniqueIdentities,
										"User owned Application "+identityID+" has been specified as an owner in resource properties", 1)
								}
							}

							rctxItem := context.GetUserContextItem(resourceContext, createdBy, userType, "", identityID, resourceContextInsertDoc.Account, nil)
							rctxItem.OwnerSubType = common.CREATOR_RCTXITEM_OWNERTYPE
							resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
								resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
								rctxItem,
							)
						}
					} else {
						resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
							resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
							context.GetUserContextItem(resourceContext, createdBy, userType, "", "", resourceContextInsertDoc.Account, nil),
						)
					}
				}
			}
		}

		description := ""
		switch resourceContextInsertDoc.ResourceType {
		case common.AWS_IAM_ROLE_RESOURCE_TYPE:
			if role, ok := entityJSON["role"].(map[string]any); ok {
				if desc, ok := role["description"].(string); ok && len(desc) > 0 {
					description = strings.ToLower(desc)
				}
			}
		default:
			if desc, ok := entityJSON["description"].(string); ok && len(desc) > 0 {
				description = strings.ToLower(desc)
			}
		}

		if len(description) > 0 {
			GetContextFromDescription(description, resourceContext, resourceContextInsertDoc)
		}
	}
}

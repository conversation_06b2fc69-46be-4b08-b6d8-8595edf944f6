package similarity

import (
	"sort"
	"strings"

	"github.com/precize/common"
	"github.com/precize/common/context"
	"github.com/precize/enhancer/source/attribute"
	"github.com/precize/logger"
)

var keysToAvoid = make(map[string]struct{})

func GetSimilarResourceNameList(similarResources map[string][]string, docID string, resourceContextDoc common.ResourceContextInsertDoc) {

	resourceName := strings.ToLower(resourceContextDoc.ResourceName)
	resourceType := resourceContextDoc.ResourceType

	if len(resourceName) > 0 {

		if excludeResourceFromSimilarResourceCheck(resourceName, resourceType) {
			return
		}

		tokens := tokenizeString(resourceName)
		key := derivePrefixKey(tokens)

		contextutils.GlobalValuesMutex.Lock()

		if _, ok := keysToAvoid[key]; !ok && len(key) > 0 {
			similarResources[key] = append(similarResources[key], docID)
			if len(similarResources[key]) > 50 {
				keysToAvoid[key] = struct{}{}
				delete(similarResources, key)
				logger.Print(logger.INFO, "Similar ResourceName Key avoided", key)
			}
		}

		contextutils.GlobalValuesMutex.Unlock()
	}
}

func GetSameApplicationResourceList(sameApps map[string][]string, docID string, resourceContextDoc common.ResourceContextInsertDoc) {

	uniqueApps := make(map[string]struct{})

	for _, v := range resourceContextDoc.ResourceAppTypes.DefinedApp {
		uniqueApps[v.Name] = struct{}{}
	}
	for _, v := range resourceContextDoc.ResourceAppTypes.DerivedApp {
		uniqueApps[v.Name] = struct{}{}
	}

	for appName := range uniqueApps {
		appName = strings.ToLower(appName)
		if len(appName) > 0 {
			contextutils.GlobalValuesMutex.Lock()
			sameApps[appName] = append(sameApps[appName], docID)
			contextutils.GlobalValuesMutex.Unlock()
		}
	}
}

func GetSameTagsResourceList(sameTags map[string][]string, docID string, tags []attribute.TagStruct) {

	var criticalTags []string

	for _, tag := range tags {
		tagKey := strings.ToLower(tag.Key)
		tagValue := strings.ToLower(tag.Value)

		if strings.HasPrefix(tagKey, "aws:") || strings.HasPrefix(tagKey, "hidden-") || strings.HasPrefix(tagKey, "goog-") || strings.Contains(tagKey, "env") || strings.Contains(tagKey, "name") || strings.Contains(tagKey, "deployment") {
			continue
		}

		if len(tagValue) == 0 || strings.Contains(tagValue, "null") || strings.Contains(tagValue, "nil") || strings.Contains(tagValue, "yes") || strings.Contains(tagValue, "no") {
			continue
		}

		switch tagKey {
		case "resourcegroup", "region", "bu", "group", "team":
		default:
			criticalTags = append(criticalTags, tagKey+":"+tagValue)
		}
	}

	if len(criticalTags) < 4 {
		return
	}

	sort.Strings(criticalTags)

	tagString := strings.ToLower(strings.Join(criticalTags, "+"))
	contextutils.GlobalValuesMutex.Lock()
	if _, ok := keysToAvoid[tagString]; !ok && len(tagString) > 0 {
		sameTags[tagString] = append(sameTags[tagString], docID)
		if len(sameTags[tagString]) > 50 {
			keysToAvoid[tagString] = struct{}{}
			delete(sameTags, tagString)
			logger.Print(logger.INFO, "Same Tag Resource key avoided", tagString)
		}
	}
	contextutils.GlobalValuesMutex.Unlock()
}

func tokenizeString(s string) []string {
	for _, sep := range []string{"-", "_", "."} {
		s = strings.ReplaceAll(s, sep, " ")
	}
	return strings.Fields(s)
}

func derivePrefixKey(tokens []string) string {
	switch len(tokens) {
	case 0:
		return ""
	case 1:
		if len(tokens[0]) >= 10 {
			prefix := (len(tokens[0]) * 60) / 100
			return tokens[0][:prefix]
		}
		return ""
	case 2:
		if len(tokens[0]) >= 5 {
			return tokens[0]
		}
		return ""
	default:
		return strings.Join(tokens[:len(tokens)-1], "-")
	}
}

func excludeResourceFromSimilarResourceCheck(resourceName, resourceType string) bool {

	switch resourceType {
	case common.AZURE_ADUSER_RESOURCE_TYPE, common.AWS_SSOUSER_RESOURCE_TYPE, common.GCP_IAM_RESOURCE_TYPE, common.AWS_ROOTUSER_RESOURCE_TYPE:

		return true
	}

	if strings.HasPrefix(resourceName, "amazon") {
		// To avoid amazon managed resources like roles
		return true
	}

	return false
}

package context

import (
	"regexp"
	"strings"

	"github.com/precize/common"
	"github.com/precize/common/context"
	"github.com/precize/enhancer/internal/resource"
)

func IsCostCenterKey(tagKey string) bool {

	for r := range contextutils.CostCenterTagKeys {
		regex := regexp.MustCompile(r)
		if regex.MatchString(strings.ToLower(tagKey)) {
			return true
		}
	}

	return false
}

func GetInheritedCostCenter(parentDoc common.ResourceContextInsertDoc) (inheritedCostCenter []common.ResourceContextItem) {

	if len(parentDoc.DefinedCostCenter) > 0 {
		inheritedCostCenter = append(inheritedCostCenter, parentDoc.DefinedCostCenter...)
	} else if len(parentDoc.InheritedCostCenter) > 0 {
		inheritedCostCenter = append(inheritedCostCenter, parentDoc.InheritedCostCenter...)
	}

	return
}

func GetUniqueCostCenterContext(resourceContextDoc *common.ResourceContextInsertDoc) (costCenter []string) {

	uniqueCostCenter := make(map[string]struct{})

	resourceutils.GetUniqueContext(&resourceContextDoc.DefinedCostCenter, uniqueCostCenter)
	resourceutils.GetUniqueContext(&resourceContextDoc.InheritedCostCenter, uniqueCostCenter)

	for costCenterName := range uniqueCostCenter {
		costCenter = append(costCenter, costCenterName)
	}

	return
}

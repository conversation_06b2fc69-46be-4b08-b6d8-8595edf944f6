package context

import (
	"regexp"
	"strings"

	"github.com/precize/common"
	"github.com/precize/common/context"
	"github.com/precize/enhancer/internal/resource"
)

func GetSoftwareNameFromValue(str string) string {

	str = strings.ToLower(str)

	for software, values := range contextutils.SoftwareValues {
		for _, val := range values {
			if strings.Contains(val, `\b`) {
				// \b whole word match does not consider _ as special character
				str = strings.ReplaceAll(str, "_", "-")
			}

			regex := regexp.MustCompile(val)
			if regex.MatchString(str) {
				return software
			}
		}
	}

	return ""
}

func GetSoftwareNameListFromValue(str string) []string {

	softwareNames := make([]string, 0)
	str = strings.ToLower(str)

	for software, values := range contextutils.SoftwareValues {
		for _, val := range values {
			if strings.Contains(val, `\b`) {
				// \b whole word match does not consider _ as special character
				str = strings.ReplaceAll(str, "_", "-")
			}

			regex := regexp.MustCompile(val)
			if regex.MatchString(str) {
				softwareNames = append(softwareNames, software)
			}
		}
	}

	return softwareNames
}

func GetUniqueSoftwareContext(resourceContextDoc *common.ResourceContextInsertDoc) (software []string) {

	uniqueSoftware := make(map[string]struct{})

	resourceutils.GetUniqueContext(&resourceContextDoc.DefinedSoftware, uniqueSoftware)
	resourceutils.GetUniqueContext(&resourceContextDoc.DerivedSoftware, uniqueSoftware)

	for softwareName := range uniqueSoftware {
		software = append(software, softwareName)
	}

	return
}

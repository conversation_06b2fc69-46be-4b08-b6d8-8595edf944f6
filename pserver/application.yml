server:
  name: "providerServer"
  port : 19090
  servlet:
    context-path: /provider
spring:
  elasticsearch:
    host: localhost
    port: 8888
    scheme: http
    batchSize: 5000
    identity:
      batchSize: 1000
openai:
  providerApiKey: "s3JFP3NN5sYSrSQjxX9eNmTmuIOT14XWjRAWPfl3yqVab7QiFPyIRGInYK3xHgeNn3tjXtEBw0IovU10pzucZbNpwNepqUn1VFg+1eFDT0zFsG7AifBov6+J1HJJXOZ4ydJdXzU+rPctxe/8rsOuLVABL0JwOkBUch2OUpk8aDId1nQro4wNCW4oFg3MTlaCcHuZseIqdeAH6dhBak8a9spiO8IAkZlC18dDO2gwHAYuW0SN1aS1GxWZZ6/U4JEptemAnyMWBNgJGDp0X+AwZFIcMY80mW96zt3JnZVsxGp6uXurMSTFkKJ4zWxlP05ULa69ffJDPp6E1MroKdXCtx8VMEE="
scan-server:
  host: localhost
  port: 8889
  scheme: http
analyzer:
  fetch_interval: "15m"
weaviate:
  host: localhost:8080
  scheme: http
  openAIAPIKey: "RqKGcBq69R21rKr6Kw3LvCTSqIMzyHlSwBYBen9pAdwyJzoF71v1BKpVpaVcq4cj2uRmLcbyFkl7XHQI8FAjaWcomVY1Rgp1a2yfuPLTOTNlFKcHvJ9bmcLDt3DBRrbrePeHjRX/Kamb6kfsSFZUU/gC+8j4guCNizKjRij6P8ui2qKGNsKimkcCzPK/3HeFwPQF4AQivqx/j3LBOXVephCXH6dEI0X8KvabUpr5SSTjVVfOUn/V2B5uIcpn32o8QBf8EnnpkeHuA6ttxI5pVkUY9KwS60N+Alnlh2FoY8Vr7U7EaA28sFAv8eI0fynb"
env: "QA"
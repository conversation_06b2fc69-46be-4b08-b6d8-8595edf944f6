server:
  name: "providerServer"
  port : 19090
  servlet:
    context-path: /provider
spring:
  elasticsearch:
    host: **********
    port: 9200
    scheme: http
    batchSize: 5000
    identity:
      batchSize: 1000
openai:
  providerApiKey: "s3JFP3NN5sYSrSQjxX9eNmTmuIOT14XWjRAWPfl3yqVab7QiFPyIRGInYK3xHgeNn3tjXtEBw0IovU10pzucZbNpwNepqUn1VFg+1eFDT0zFsG7AifBov6+J1HJJXOZ4ydJdXzU+rPctxe/8rsOuLVABL0JwOkBUch2OUpk8aDId1nQro4wNCW4oFg3MTlaCcHuZseIqdeAH6dhBak8a9spiO8IAkZlC18dDO2gwHAYuW0SN1aS1GxWZZ6/U4JEptemAnyMWBNgJGDp0X+AwZFIcMY80mW96zt3JnZVsxGp6uXurMSTFkKJ4zWxlP05ULa69ffJDPp6E1MroKdXCtx8VMEE="
scan-server:
  host: *********
  port: 18090
  scheme: http
analyzer:
  fetch_interval: "15m"
weaviate:
  host: localhost:8080
  scheme: http
env: "QA"
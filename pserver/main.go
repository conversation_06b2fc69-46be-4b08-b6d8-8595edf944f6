package main

import (
	"flag"

	"github.com/precize/config"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/pserver/server"
	"github.com/precize/pserver/server/weaviate"
	"github.com/precize/transport"
)

func main() {

	var (
		appConfigPath = flag.String("config", "application.yml", "Path to application.yml")
		debug         = flag.Bool("debug", false, "Debug mode")
	)

	flag.Parse()

	logger.InitializeLogs("pserver", *debug)

	defaultConf, err := config.InitializeApplicationConfig(*appConfigPath)
	if err != nil {
		return
	} else if defaultConf {
		logger.Print(logger.INFO, "Application config could not be read. Starting with defaults", *appConfigPath)
	}

	if err = elastic.ConnectToElasticSearch(); err != nil {
		return
	}

	transport.SetHttpClient()

	if err = weaviate.InitializeWeaviateClient(); err != nil {
		logger.Print(logger.ERROR, "Failed to initialize Weaviate client: %v", err)
		return
	}

	server.StartProviderServer()
}

2025/09/10 16:12:35 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/10 16:12:35 Index iac_git_commits exists - 
2025/09/10 16:12:35 Index cfstack_templates exists - 
2025/09/10 16:12:35 Index arm_templates exists - 
2025/09/10 16:12:35 Index terraform_resources exists - 
2025/09/10 16:12:35 Index tf_commits exists - 
2025/09/10 16:12:35 Index tf_variables exists - 
2025/09/10 16:12:35 Index resource_context exists - 
2025/09/10 16:12:36 Index text_lookup exists - 
2025/09/10 16:12:36 Index ai_resources exists - 
2025/09/10 16:12:36 Index idp_events exists - 
2025/09/10 16:12:36 Index idp_users exists - 
2025/09/10 16:12:36 Index idp_apps exists - 
2025/09/10 16:12:36 Index idp_groups exists - 
2025/09/10 16:12:36 Index cloud_incidents exists - 
2025/09/10 16:12:36 Index jira_issues exists - 
2025/09/10 16:12:36 Index jira_data exists - 
2025/09/10 16:12:36 Index jira_resources exists - 
2025/09/10 16:12:36 Index precize_creations exists - 
2025/09/10 16:12:36 Index external_cloud_resources exists - 
2025/09/10 16:12:36 Initializing Weaviate client... - 
2025/09/10 16:12:36 Weaviate connection status:  - true - 
2025/09/10 16:12:36 Starting server on port 19090 - 
2025/09/10 16:13:16 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/10 16:13:17 Index iac_git_commits exists - 
2025/09/10 16:13:17 Index cfstack_templates exists - 
2025/09/10 16:13:17 Index arm_templates exists - 
2025/09/10 16:13:17 Index terraform_resources exists - 
2025/09/10 16:13:17 Index tf_commits exists - 
2025/09/10 16:13:17 Index tf_variables exists - 
2025/09/10 16:13:17 Index resource_context exists - 
2025/09/10 16:13:17 Index text_lookup exists - 
2025/09/10 16:13:17 Index ai_resources exists - 
2025/09/10 16:13:17 Index idp_events exists - 
2025/09/10 16:13:17 Index idp_users exists - 
2025/09/10 16:13:17 Index idp_apps exists - 
2025/09/10 16:13:17 Index idp_groups exists - 
2025/09/10 16:13:17 Index cloud_incidents exists - 
2025/09/10 16:13:17 Index jira_issues exists - 
2025/09/10 16:13:17 Index jira_data exists - 
2025/09/10 16:13:17 Index jira_resources exists - 
2025/09/10 16:13:17 Index precize_creations exists - 
2025/09/10 16:13:18 Index external_cloud_resources exists - 
2025/09/10 16:13:18 Initializing Weaviate client... - 
2025/09/10 16:13:18 Weaviate connection status:  - true - 
2025/09/10 16:13:18 Starting server on port 19090 - 
2025/09/10 19:33:17 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/10 19:33:17 Index iac_git_commits exists - 
2025/09/10 19:33:17 Index cfstack_templates exists - 
2025/09/10 19:33:17 Index arm_templates exists - 
2025/09/10 19:33:17 Index terraform_resources exists - 
2025/09/10 19:33:17 Index tf_commits exists - 
2025/09/10 19:33:17 Index tf_variables exists - 
2025/09/10 19:33:17 Index resource_context exists - 
2025/09/10 19:33:17 Index text_lookup exists - 
2025/09/10 19:33:17 Index ai_resources exists - 
2025/09/10 19:33:18 Index idp_events exists - 
2025/09/10 19:33:18 Index idp_users exists - 
2025/09/10 19:33:18 Index idp_apps exists - 
2025/09/10 19:33:18 Index idp_groups exists - 
2025/09/10 19:33:18 Index cloud_incidents exists - 
2025/09/10 19:33:18 Index jira_issues exists - 
2025/09/10 19:33:18 Index jira_data exists - 
2025/09/10 19:33:18 Index jira_resources exists - 
2025/09/10 19:33:18 Index precize_creations exists - 
2025/09/10 19:33:18 Index external_cloud_resources exists - 
2025/09/10 19:33:18 Initializing Weaviate client... - 
2025/09/10 19:33:18 Weaviate connection status:  - true - 
2025/09/10 19:33:18 Starting server on port 19090 - 
2025/09/10 19:39:36 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/10 19:39:36 Index iac_git_commits exists - 
2025/09/10 19:39:36 Index cfstack_templates exists - 
2025/09/10 19:39:36 Index arm_templates exists - 
2025/09/10 19:39:36 Index terraform_resources exists - 
2025/09/10 19:39:36 Index tf_commits exists - 
2025/09/10 19:39:36 Index tf_variables exists - 
2025/09/10 19:39:36 Index resource_context exists - 
2025/09/10 19:39:36 Index text_lookup exists - 
2025/09/10 19:39:36 Index ai_resources exists - 
2025/09/10 19:39:36 Index idp_events exists - 
2025/09/10 19:39:36 Index idp_users exists - 
2025/09/10 19:39:36 Index idp_apps exists - 
2025/09/10 19:39:36 Index idp_groups exists - 
2025/09/10 19:39:37 Index cloud_incidents exists - 
2025/09/10 19:39:37 Index jira_issues exists - 
2025/09/10 19:39:37 Index jira_data exists - 
2025/09/10 19:39:37 Index jira_resources exists - 
2025/09/10 19:39:37 Index precize_creations exists - 
2025/09/10 19:39:37 Index external_cloud_resources exists - 
2025/09/10 19:39:37 Initializing Weaviate client... - 
2025/09/10 19:39:37 Weaviate connection status:  - true - 
2025/09/10 19:39:37 Starting server on port 19090 - 
2025/09/10 20:08:34 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/10 20:08:34 Index iac_git_commits exists - 
2025/09/10 20:08:34 Index cfstack_templates exists - 
2025/09/10 20:08:34 Index arm_templates exists - 
2025/09/10 20:08:34 Index terraform_resources exists - 
2025/09/10 20:08:34 Index tf_commits exists - 
2025/09/10 20:08:34 Index tf_variables exists - 
2025/09/10 20:08:34 Index resource_context exists - 
2025/09/10 20:08:34 Index text_lookup exists - 
2025/09/10 20:08:34 Index ai_resources exists - 
2025/09/10 20:08:34 Index idp_events exists - 
2025/09/10 20:08:35 Index idp_users exists - 
2025/09/10 20:08:35 Index idp_apps exists - 
2025/09/10 20:08:35 Index idp_groups exists - 
2025/09/10 20:08:35 Index cloud_incidents exists - 
2025/09/10 20:08:35 Index jira_issues exists - 
2025/09/10 20:08:35 Index jira_data exists - 
2025/09/10 20:08:35 Index jira_resources exists - 
2025/09/10 20:08:35 Index precize_creations exists - 
2025/09/10 20:08:35 Index external_cloud_resources exists - 
2025/09/10 20:08:35 Initializing Weaviate client... - 
2025/09/10 20:08:35 Weaviate connection status:  - true - 
2025/09/10 20:08:35 Starting server on port 19090 - 
2025/09/10 20:26:44 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/10 20:26:44 Index iac_git_commits exists - 
2025/09/10 20:26:44 Index cfstack_templates exists - 
2025/09/10 20:26:44 Index arm_templates exists - 
2025/09/10 20:26:44 Index terraform_resources exists - 
2025/09/10 20:26:44 Index tf_commits exists - 
2025/09/10 20:26:44 Index tf_variables exists - 
2025/09/10 20:26:44 Index resource_context exists - 
2025/09/10 20:26:44 Index text_lookup exists - 
2025/09/10 20:26:44 Index ai_resources exists - 
2025/09/10 20:26:44 Index idp_events exists - 
2025/09/10 20:26:44 Index idp_users exists - 
2025/09/10 20:26:45 Index idp_apps exists - 
2025/09/10 20:26:45 Index idp_groups exists - 
2025/09/10 20:26:45 Index cloud_incidents exists - 
2025/09/10 20:26:45 Index jira_issues exists - 
2025/09/10 20:26:45 Index jira_data exists - 
2025/09/10 20:26:45 Index jira_resources exists - 
2025/09/10 20:26:45 Index precize_creations exists - 
2025/09/10 20:26:45 Index external_cloud_resources exists - 
2025/09/10 20:26:45 Initializing Weaviate client... - 
2025/09/10 20:26:45 Weaviate connection status:  - true - 
2025/09/10 20:26:45 Starting server on port 19090 - 
2025/09/10 20:26:51 [8] Failed to parse request JSON: %v - json: cannot unmarshal number into Go struct field SearchSimilarRequest.autoCut of type string - 
goroutine 8 [running]:
runtime/debug.Stack()
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/runtime/debug/stack.go:26 +0x64
github.com/precize/logger.Print({0x103f052ca, 0x5}, {0x1400020c1b0, 0x88}, {0x140000eb400, 0x1, 0x1})
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/logger/log.go:121 +0x950
github.com/precize/pserver/server/weaviate.SearchSimilar({0x104383668, 0x14000292000}, 0x140003a4f00)
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/pserver/server/weaviate/handlers.go:268 +0x408
net/http.HandlerFunc.ServeHTTP(0x104370ac0, {0x104383668, 0x14000292000}, 0x140003a4f00)
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2220 +0x40
github.com/precize/pserver/server.verifyMiddleware.func1({0x104383668, 0x14000292000}, 0x140003a4f00)
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/pserver/server/pserver.go:62 +0x4c
net/http.HandlerFunc.ServeHTTP(0x140007120f0, {0x104383668, 0x14000292000}, 0x140003a4f00)
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2220 +0x40
github.com/gorilla/mux.(*Router).ServeHTTP(0x140001ea300, {0x104383668, 0x14000292000}, 0x140003a4f00)
	/Users/<USER>/go/pkg/mod/github.com/gorilla/mux@v1.8.1/mux.go:212 +0x254
net/http.serverHandler.ServeHTTP({0x1400020a3c0}, {0x104383668, 0x14000292000}, 0x140003a4c80)
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:3210 +0x2b0
net/http.(*conn).serve(0x140000b83f0, {0x104384f08, 0x1400029ae60})
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2092 +0x1604
created by net/http.(*Server).Serve in goroutine 1
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:3360 +0x88c
2025/09/10 20:26:56 [8] Failed to parse request JSON: %v - invalid character '}' looking for beginning of object key string - 
goroutine 8 [running]:
runtime/debug.Stack()
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/runtime/debug/stack.go:26 +0x64
github.com/precize/logger.Print({0x103f052ca, 0x5}, {0x14000181880, 0x6a}, {0x140000eb400, 0x1, 0x1})
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/logger/log.go:121 +0x950
github.com/precize/pserver/server/weaviate.SearchSimilar({0x104383668, 0x1400015c000}, 0x140001588c0)
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/pserver/server/weaviate/handlers.go:268 +0x408
net/http.HandlerFunc.ServeHTTP(0x104370ac0, {0x104383668, 0x1400015c000}, 0x140001588c0)
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2220 +0x40
github.com/precize/pserver/server.verifyMiddleware.func1({0x104383668, 0x1400015c000}, 0x140001588c0)
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/pserver/server/pserver.go:62 +0x4c
net/http.HandlerFunc.ServeHTTP(0x1400000c2a0, {0x104383668, 0x1400015c000}, 0x140001588c0)
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2220 +0x40
github.com/gorilla/mux.(*Router).ServeHTTP(0x140001ea300, {0x104383668, 0x1400015c000}, 0x140001588c0)
	/Users/<USER>/go/pkg/mod/github.com/gorilla/mux@v1.8.1/mux.go:212 +0x254
net/http.serverHandler.ServeHTTP({0x1400020a3c0}, {0x104383668, 0x1400015c000}, 0x14000158640)
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:3210 +0x2b0
net/http.(*conn).serve(0x140000b83f0, {0x104384f08, 0x1400029ae60})
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2092 +0x1604
created by net/http.(*Server).Serve in goroutine 1
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:3360 +0x88c
2025/09/10 20:26:59 [8] Failed to parse request JSON: %v - json: cannot unmarshal number into Go struct field SearchSimilarRequest.autoCut of type string - 
goroutine 8 [running]:
runtime/debug.Stack()
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/runtime/debug/stack.go:26 +0x64
github.com/precize/logger.Print({0x103f052ca, 0x5}, {0x14000152bd0, 0x88}, {0x140000eb400, 0x1, 0x1})
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/logger/log.go:121 +0x950
github.com/precize/pserver/server/weaviate.SearchSimilar({0x104383668, 0x1400015c0e0}, 0x14000158c80)
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/pserver/server/weaviate/handlers.go:268 +0x408
net/http.HandlerFunc.ServeHTTP(0x104370ac0, {0x104383668, 0x1400015c0e0}, 0x14000158c80)
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2220 +0x40
github.com/precize/pserver/server.verifyMiddleware.func1({0x104383668, 0x1400015c0e0}, 0x14000158c80)
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/pserver/server/pserver.go:62 +0x4c
net/http.HandlerFunc.ServeHTTP(0x1400000c318, {0x104383668, 0x1400015c0e0}, 0x14000158c80)
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2220 +0x40
github.com/gorilla/mux.(*Router).ServeHTTP(0x140001ea300, {0x104383668, 0x1400015c0e0}, 0x14000158c80)
	/Users/<USER>/go/pkg/mod/github.com/gorilla/mux@v1.8.1/mux.go:212 +0x254
net/http.serverHandler.ServeHTTP({0x1400020a3c0}, {0x104383668, 0x1400015c0e0}, 0x14000158a00)
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:3210 +0x2b0
net/http.(*conn).serve(0x140000b83f0, {0x104384f08, 0x1400029ae60})
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2092 +0x1604
created by net/http.(*Server).Serve in goroutine 1
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:3360 +0x88c
2025/09/10 20:27:00 [8] Failed to parse request JSON: %v - json: cannot unmarshal number into Go struct field SearchSimilarRequest.autoCut of type string - 
goroutine 8 [running]:
runtime/debug.Stack()
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/runtime/debug/stack.go:26 +0x64
github.com/precize/logger.Print({0x103f052ca, 0x5}, {0x14000152cf0, 0x88}, {0x140000eb400, 0x1, 0x1})
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/logger/log.go:121 +0x950
github.com/precize/pserver/server/weaviate.SearchSimilar({0x104383668, 0x1400015c1c0}, 0x14000159040)
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/pserver/server/weaviate/handlers.go:268 +0x408
net/http.HandlerFunc.ServeHTTP(0x104370ac0, {0x104383668, 0x1400015c1c0}, 0x14000159040)
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2220 +0x40
github.com/precize/pserver/server.verifyMiddleware.func1({0x104383668, 0x1400015c1c0}, 0x14000159040)
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/pserver/server/pserver.go:62 +0x4c
net/http.HandlerFunc.ServeHTTP(0x1400000c360, {0x104383668, 0x1400015c1c0}, 0x14000159040)
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2220 +0x40
github.com/gorilla/mux.(*Router).ServeHTTP(0x140001ea300, {0x104383668, 0x1400015c1c0}, 0x14000159040)
	/Users/<USER>/go/pkg/mod/github.com/gorilla/mux@v1.8.1/mux.go:212 +0x254
net/http.serverHandler.ServeHTTP({0x1400020a3c0}, {0x104383668, 0x1400015c1c0}, 0x14000158dc0)
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:3210 +0x2b0
net/http.(*conn).serve(0x140000b83f0, {0x104384f08, 0x1400029ae60})
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2092 +0x1604
created by net/http.(*Server).Serve in goroutine 1
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:3360 +0x88c
2025/09/10 20:28:41 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/10 20:28:41 Index iac_git_commits exists - 
2025/09/10 20:28:41 Index cfstack_templates exists - 
2025/09/10 20:28:41 Index arm_templates exists - 
2025/09/10 20:28:41 Index terraform_resources exists - 
2025/09/10 20:28:41 Index tf_commits exists - 
2025/09/10 20:28:41 Index tf_variables exists - 
2025/09/10 20:28:41 Index resource_context exists - 
2025/09/10 20:28:41 Index text_lookup exists - 
2025/09/10 20:28:41 Index ai_resources exists - 
2025/09/10 20:28:41 Index idp_events exists - 
2025/09/10 20:28:41 Index idp_users exists - 
2025/09/10 20:28:41 Index idp_apps exists - 
2025/09/10 20:28:41 Index idp_groups exists - 
2025/09/10 20:28:41 Index cloud_incidents exists - 
2025/09/10 20:28:41 Index jira_issues exists - 
2025/09/10 20:28:41 Index jira_data exists - 
2025/09/10 20:28:41 Index jira_resources exists - 
2025/09/10 20:28:41 Index precize_creations exists - 
2025/09/10 20:28:41 Index external_cloud_resources exists - 
2025/09/10 20:28:41 Initializing Weaviate client... - 
2025/09/10 20:28:41 Weaviate connection status:  - true - 
2025/09/10 20:28:41 Starting server on port 19090 - 
2025/09/10 20:29:35 [41] Failed to parse request JSON: %v - invalid character '}' looking for beginning of object key string - 
goroutine 41 [running]:
runtime/debug.Stack()
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/runtime/debug/stack.go:26 +0x64
github.com/precize/logger.Print({0x1030293aa, 0x5}, {0x14000299490, 0x6b}, {0x140002a9400, 0x1, 0x1})
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/logger/log.go:121 +0x950
github.com/precize/pserver/server/weaviate.SearchSimilar({0x1034a7668, 0x140001221c0}, 0x1400013e640)
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/pserver/server/weaviate/handlers.go:268 +0x40c
net/http.HandlerFunc.ServeHTTP(0x103494ac0, {0x1034a7668, 0x140001221c0}, 0x1400013e640)
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2220 +0x40
github.com/precize/pserver/server.verifyMiddleware.func1({0x1034a7668, 0x140001221c0}, 0x1400013e640)
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/pserver/server/pserver.go:62 +0x4c
net/http.HandlerFunc.ServeHTTP(0x140002b21f8, {0x1034a7668, 0x140001221c0}, 0x1400013e640)
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2220 +0x40
github.com/gorilla/mux.(*Router).ServeHTTP(0x14000112000, {0x1034a7668, 0x140001221c0}, 0x1400013e640)
	/Users/<USER>/go/pkg/mod/github.com/gorilla/mux@v1.8.1/mux.go:212 +0x254
net/http.serverHandler.ServeHTTP({0x140001863c0}, {0x1034a7668, 0x140001221c0}, 0x1400013e3c0)
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:3210 +0x2b0
net/http.(*conn).serve(0x14000010c60, {0x1034a8f08, 0x14000212dc0})
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2092 +0x1604
created by net/http.(*Server).Serve in goroutine 1
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:3360 +0x88c
2025/09/10 20:39:13 [133] Failed to parse request JSON: %v - invalid character '}' looking for beginning of object key string - 
goroutine 133 [running]:
runtime/debug.Stack()
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/runtime/debug/stack.go:26 +0x64
github.com/precize/logger.Print({0x1030293aa, 0x5}, {0x1400009b810, 0x6c}, {0x14000127400, 0x1, 0x1})
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/logger/log.go:121 +0x950
github.com/precize/pserver/server/weaviate.SearchSimilar({0x1034a7668, 0x1400020a2a0}, 0x140003cbcc0)
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/pserver/server/weaviate/handlers.go:268 +0x40c
net/http.HandlerFunc.ServeHTTP(0x103494ac0, {0x1034a7668, 0x1400020a2a0}, 0x140003cbcc0)
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2220 +0x40
github.com/precize/pserver/server.verifyMiddleware.func1({0x1034a7668, 0x1400020a2a0}, 0x140003cbcc0)
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/pserver/server/pserver.go:62 +0x4c
net/http.HandlerFunc.ServeHTTP(0x14000116420, {0x1034a7668, 0x1400020a2a0}, 0x140003cbcc0)
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2220 +0x40
github.com/gorilla/mux.(*Router).ServeHTTP(0x14000112000, {0x1034a7668, 0x1400020a2a0}, 0x140003cbcc0)
	/Users/<USER>/go/pkg/mod/github.com/gorilla/mux@v1.8.1/mux.go:212 +0x254
net/http.serverHandler.ServeHTTP({0x140001863c0}, {0x1034a7668, 0x1400020a2a0}, 0x140003cba40)
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:3210 +0x2b0
net/http.(*conn).serve(0x140000a8b40, {0x1034a8f08, 0x14000212410})
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2092 +0x1604
created by net/http.(*Server).Serve in goroutine 1
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:3360 +0x88c
2025/09/10 20:56:27 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/10 20:56:27 Index iac_git_commits exists - 
2025/09/10 20:56:27 Index cfstack_templates exists - 
2025/09/10 20:56:27 Index arm_templates exists - 
2025/09/10 20:56:27 Index terraform_resources exists - 
2025/09/10 20:56:27 Index tf_commits exists - 
2025/09/10 20:56:27 Index tf_variables exists - 
2025/09/10 20:56:27 Index resource_context exists - 
2025/09/10 20:56:27 Index text_lookup exists - 
2025/09/10 20:56:27 Index ai_resources exists - 
2025/09/10 20:56:27 Index idp_events exists - 
2025/09/10 20:56:27 Index idp_users exists - 
2025/09/10 20:56:27 Index idp_apps exists - 
2025/09/10 20:56:27 Index idp_groups exists - 
2025/09/10 20:56:27 Index cloud_incidents exists - 
2025/09/10 20:56:27 Index jira_issues exists - 
2025/09/10 20:56:27 Index jira_data exists - 
2025/09/10 20:56:27 Index jira_resources exists - 
2025/09/10 20:56:28 Index precize_creations exists - 
2025/09/10 20:56:28 Index external_cloud_resources exists - 
2025/09/10 20:56:28 Initializing Weaviate client... - 
2025/09/10 20:56:28 Weaviate connection status:  - true - 
2025/09/10 20:56:28 Starting server on port 19090 - 
2025/09/10 20:58:33 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/10 20:58:33 Index iac_git_commits exists - 
2025/09/10 20:58:33 Index cfstack_templates exists - 
2025/09/10 20:58:33 Index arm_templates exists - 
2025/09/10 20:58:34 Index terraform_resources exists - 
2025/09/10 20:58:34 Index tf_commits exists - 
2025/09/10 20:58:34 Index tf_variables exists - 
2025/09/10 20:58:34 Index resource_context exists - 
2025/09/10 20:58:34 Index text_lookup exists - 
2025/09/10 20:58:34 Index ai_resources exists - 
2025/09/10 20:58:34 Index idp_events exists - 
2025/09/10 20:58:34 Index idp_users exists - 
2025/09/10 20:58:34 Index idp_apps exists - 
2025/09/10 20:58:34 Index idp_groups exists - 
2025/09/10 20:58:34 Index cloud_incidents exists - 
2025/09/10 20:58:34 Index jira_issues exists - 
2025/09/10 20:58:34 Index jira_data exists - 
2025/09/10 20:58:34 Index jira_resources exists - 
2025/09/10 20:58:34 Index precize_creations exists - 
2025/09/10 20:58:34 Index external_cloud_resources exists - 
2025/09/10 20:58:34 Initializing Weaviate client... - 
2025/09/10 20:58:34 Weaviate connection status:  - true - 
2025/09/10 20:58:34 Starting server on port 19090 - 

2025/09/09 15:22:27 Weaviate connection status: %v - true - 
2025/09/09 15:23:40 Application config could not be read. Starting with defaults -  - 
2025/09/09 15:23:40 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/09 15:23:40 Index iac_git_commits exists - 
2025/09/09 15:23:40 Index cfstack_templates exists - 
2025/09/09 15:23:40 Index arm_templates exists - 
2025/09/09 15:23:40 Index terraform_resources exists - 
2025/09/09 15:23:40 Index tf_commits exists - 
2025/09/09 15:23:40 Index tf_variables exists - 
2025/09/09 15:23:40 Index resource_context exists - 
2025/09/09 15:23:40 Index text_lookup exists - 
2025/09/09 15:23:40 Index ai_resources exists - 
2025/09/09 15:23:40 Index idp_events exists - 
2025/09/09 15:23:41 Index idp_users exists - 
2025/09/09 15:23:41 Index idp_apps exists - 
2025/09/09 15:23:41 Index idp_groups exists - 
2025/09/09 15:23:41 Index cloud_incidents exists - 
2025/09/09 15:23:41 Index jira_issues exists - 
2025/09/09 15:23:41 Index jira_data exists - 
2025/09/09 15:23:41 Index jira_resources exists - 
2025/09/09 15:23:41 Index precize_creations exists - 
2025/09/09 15:23:41 Index external_cloud_resources exists - 
2025/09/09 15:23:41 Starting server on port 19090 - 
2025/09/09 15:24:19 Weaviate connection status: %v - true - 
2025/09/09 15:24:19 Application config could not be read. Starting with defaults -  - 
2025/09/09 15:24:20 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/09 15:24:20 Index iac_git_commits exists - 
2025/09/09 15:24:20 Index cfstack_templates exists - 
2025/09/09 15:24:20 Index arm_templates exists - 
2025/09/09 15:24:20 Index terraform_resources exists - 
2025/09/09 15:24:20 Index tf_commits exists - 
2025/09/09 15:24:20 Index tf_variables exists - 
2025/09/09 15:24:20 Index resource_context exists - 
2025/09/09 15:24:20 Index text_lookup exists - 
2025/09/09 15:24:20 Index ai_resources exists - 
2025/09/09 15:24:20 Index idp_events exists - 
2025/09/09 15:24:20 Index idp_users exists - 
2025/09/09 15:24:20 Index idp_apps exists - 
2025/09/09 15:24:20 Index idp_groups exists - 
2025/09/09 15:24:20 Index cloud_incidents exists - 
2025/09/09 15:24:21 Index jira_issues exists - 
2025/09/09 15:24:21 Index jira_data exists - 
2025/09/09 15:24:21 Index jira_resources exists - 
2025/09/09 15:24:21 Index precize_creations exists - 
2025/09/09 15:24:21 Index external_cloud_resources exists - 
2025/09/09 15:24:21 Starting server on port 19090 - 
2025/09/09 15:25:30 Weaviate connection status: %v - true - 
2025/09/09 15:25:30 Application config could not be read. Starting with defaults -  - 
2025/09/09 15:25:30 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/09 15:25:30 Index iac_git_commits exists - 
2025/09/09 15:25:30 Index cfstack_templates exists - 
2025/09/09 15:25:30 Index arm_templates exists - 
2025/09/09 15:25:30 Index terraform_resources exists - 
2025/09/09 15:25:30 Index tf_commits exists - 
2025/09/09 15:25:30 Index tf_variables exists - 
2025/09/09 15:25:30 Index resource_context exists - 
2025/09/09 15:25:30 Index text_lookup exists - 
2025/09/09 15:25:31 Index ai_resources exists - 
2025/09/09 15:25:31 Index idp_events exists - 
2025/09/09 15:25:31 Index idp_users exists - 
2025/09/09 15:25:31 Index idp_apps exists - 
2025/09/09 15:25:31 Index idp_groups exists - 
2025/09/09 15:25:31 Index cloud_incidents exists - 
2025/09/09 15:25:31 Index jira_issues exists - 
2025/09/09 15:25:31 Index jira_data exists - 
2025/09/09 15:25:31 Index jira_resources exists - 
2025/09/09 15:25:31 Index precize_creations exists - 
2025/09/09 15:25:31 Index external_cloud_resources exists - 
2025/09/09 15:25:31 Starting server on port 19090 - 

2025/09/09 15:22:27 Weaviate connection status: %v - true - 
2025/09/09 15:23:40 Application config could not be read. Starting with defaults -  - 
2025/09/09 15:23:40 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/09 15:23:40 Index iac_git_commits exists - 
2025/09/09 15:23:40 Index cfstack_templates exists - 
2025/09/09 15:23:40 Index arm_templates exists - 
2025/09/09 15:23:40 Index terraform_resources exists - 
2025/09/09 15:23:40 Index tf_commits exists - 
2025/09/09 15:23:40 Index tf_variables exists - 
2025/09/09 15:23:40 Index resource_context exists - 
2025/09/09 15:23:40 Index text_lookup exists - 
2025/09/09 15:23:40 Index ai_resources exists - 
2025/09/09 15:23:40 Index idp_events exists - 
2025/09/09 15:23:41 Index idp_users exists - 
2025/09/09 15:23:41 Index idp_apps exists - 
2025/09/09 15:23:41 Index idp_groups exists - 
2025/09/09 15:23:41 Index cloud_incidents exists - 
2025/09/09 15:23:41 Index jira_issues exists - 
2025/09/09 15:23:41 Index jira_data exists - 
2025/09/09 15:23:41 Index jira_resources exists - 
2025/09/09 15:23:41 Index precize_creations exists - 
2025/09/09 15:23:41 Index external_cloud_resources exists - 
2025/09/09 15:23:41 Starting server on port 19090 - 
2025/09/09 15:24:19 Weaviate connection status: %v - true - 
2025/09/09 15:24:19 Application config could not be read. Starting with defaults -  - 
2025/09/09 15:24:20 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/09 15:24:20 Index iac_git_commits exists - 
2025/09/09 15:24:20 Index cfstack_templates exists - 
2025/09/09 15:24:20 Index arm_templates exists - 
2025/09/09 15:24:20 Index terraform_resources exists - 
2025/09/09 15:24:20 Index tf_commits exists - 
2025/09/09 15:24:20 Index tf_variables exists - 
2025/09/09 15:24:20 Index resource_context exists - 
2025/09/09 15:24:20 Index text_lookup exists - 
2025/09/09 15:24:20 Index ai_resources exists - 
2025/09/09 15:24:20 Index idp_events exists - 
2025/09/09 15:24:20 Index idp_users exists - 
2025/09/09 15:24:20 Index idp_apps exists - 
2025/09/09 15:24:20 Index idp_groups exists - 
2025/09/09 15:24:20 Index cloud_incidents exists - 
2025/09/09 15:24:21 Index jira_issues exists - 
2025/09/09 15:24:21 Index jira_data exists - 
2025/09/09 15:24:21 Index jira_resources exists - 
2025/09/09 15:24:21 Index precize_creations exists - 
2025/09/09 15:24:21 Index external_cloud_resources exists - 
2025/09/09 15:24:21 Starting server on port 19090 - 
2025/09/09 15:25:30 Weaviate connection status: %v - true - 
2025/09/09 15:25:30 Application config could not be read. Starting with defaults -  - 
2025/09/09 15:25:30 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/09 15:25:30 Index iac_git_commits exists - 
2025/09/09 15:25:30 Index cfstack_templates exists - 
2025/09/09 15:25:30 Index arm_templates exists - 
2025/09/09 15:25:30 Index terraform_resources exists - 
2025/09/09 15:25:30 Index tf_commits exists - 
2025/09/09 15:25:30 Index tf_variables exists - 
2025/09/09 15:25:30 Index resource_context exists - 
2025/09/09 15:25:30 Index text_lookup exists - 
2025/09/09 15:25:31 Index ai_resources exists - 
2025/09/09 15:25:31 Index idp_events exists - 
2025/09/09 15:25:31 Index idp_users exists - 
2025/09/09 15:25:31 Index idp_apps exists - 
2025/09/09 15:25:31 Index idp_groups exists - 
2025/09/09 15:25:31 Index cloud_incidents exists - 
2025/09/09 15:25:31 Index jira_issues exists - 
2025/09/09 15:25:31 Index jira_data exists - 
2025/09/09 15:25:31 Index jira_resources exists - 
2025/09/09 15:25:31 Index precize_creations exists - 
2025/09/09 15:25:31 Index external_cloud_resources exists - 
2025/09/09 15:25:31 Starting server on port 19090 - 
2025/09/09 15:30:35 Weaviate connection status: %v - true - 
2025/09/09 15:30:35 Application config could not be read. Starting with defaults -  - 
2025/09/09 15:30:35 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/09 15:30:35 Index iac_git_commits exists - 
2025/09/09 15:30:35 Index cfstack_templates exists - 
2025/09/09 15:30:35 Index arm_templates exists - 
2025/09/09 15:30:35 Index terraform_resources exists - 
2025/09/09 15:30:35 Index tf_commits exists - 
2025/09/09 15:30:35 Index tf_variables exists - 
2025/09/09 15:30:35 Index resource_context exists - 
2025/09/09 15:30:35 Index text_lookup exists - 
2025/09/09 15:30:35 Index ai_resources exists - 
2025/09/09 15:30:36 Index idp_events exists - 
2025/09/09 15:30:36 Index idp_users exists - 
2025/09/09 15:30:36 Index idp_apps exists - 
2025/09/09 15:30:36 Index idp_groups exists - 
2025/09/09 15:30:36 Index cloud_incidents exists - 
2025/09/09 15:30:36 Index jira_issues exists - 
2025/09/09 15:30:36 Index jira_data exists - 
2025/09/09 15:30:36 Index jira_resources exists - 
2025/09/09 15:30:36 Index precize_creations exists - 
2025/09/09 15:30:36 Index external_cloud_resources exists - 
2025/09/09 15:30:36 Starting server on port 19090 - 
2025/09/09 15:33:44 [58] Class '%s' not found - ResourceName - 
2025/09/09 15:39:31 [22] [{API Key: no api key found neither in request header: X-Openai-Api-Key nor in environment variable under OPENAI_APIKEY}][{API Key: no api key found neither in request header: X-Openai-Api-Key nor in environment variable under OPENAI_APIKEY}] Some objects failed to insert: %v - 
goroutine 22 [running]:
runtime/debug.Stack()
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/runtime/debug/stack.go:26 +0x64
github.com/precize/logger.Print({0x1059ff2f9, 0x5}, {0x140000a8240, 0x11c}, {0x140001613f0, 0x1, 0x1})
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/logger/log.go:121 +0x950
github.com/precize/pserver/server/weaviate.InsertData({0x105e7a6a8, 0x14000292000}, 0x14000230500)
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/pserver/server/weaviate/handlers.go:234 +0x9d0
net/http.HandlerFunc.ServeHTTP(0x105e67b28, {0x105e7a6a8, 0x14000292000}, 0x14000230500)
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2220 +0x40
github.com/precize/pserver/server.verifyMiddleware.func1({0x105e7a6a8, 0x14000292000}, 0x14000230500)
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/pserver/server/pserver.go:64 +0x4c
net/http.HandlerFunc.ServeHTTP(0x140001d0060, {0x105e7a6a8, 0x14000292000}, 0x14000230500)
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2220 +0x40
github.com/gorilla/mux.(*Router).ServeHTTP(0x140000e0000, {0x105e7a6a8, 0x14000292000}, 0x14000230500)
	/Users/<USER>/go/pkg/mod/github.com/gorilla/mux@v1.8.1/mux.go:212 +0x254
net/http.serverHandler.ServeHTTP({0x1400020c3c0}, {0x105e7a6a8, 0x14000292000}, 0x14000230000)
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:3210 +0x2b0
net/http.(*conn).serve(0x14000440090, {0x105e7bf28, 0x140000560a0})
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2092 +0x1604
created by net/http.(*Server).Serve in goroutine 1
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:3360 +0x88c
2025/09/09 15:40:49 Weaviate connection status: %v - true - 
2025/09/09 15:40:49 Application config could not be read. Starting with defaults -  - 
2025/09/09 15:40:49 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/09 15:40:49 Index iac_git_commits exists - 
2025/09/09 15:40:49 Index cfstack_templates exists - 
2025/09/09 15:40:49 Index arm_templates exists - 
2025/09/09 15:40:49 Index terraform_resources exists - 
2025/09/09 15:40:49 Index tf_commits exists - 
2025/09/09 15:40:49 Index tf_variables exists - 
2025/09/09 15:40:49 Index resource_context exists - 
2025/09/09 15:40:49 Index text_lookup exists - 
2025/09/09 15:40:49 Index ai_resources exists - 
2025/09/09 15:40:49 Index idp_events exists - 
2025/09/09 15:40:49 Index idp_users exists - 
2025/09/09 15:40:50 Index idp_apps exists - 
2025/09/09 15:40:50 Index idp_groups exists - 
2025/09/09 15:40:50 Index cloud_incidents exists - 
2025/09/09 15:40:50 Index jira_issues exists - 
2025/09/09 15:40:50 Index jira_data exists - 
2025/09/09 15:40:50 Index jira_resources exists - 
2025/09/09 15:40:50 Index precize_creations exists - 
2025/09/09 15:40:50 Index external_cloud_resources exists - 
2025/09/09 15:40:50 Starting server on port 19090 - 
2025/09/09 15:41:19 [30] [{API Key: no api key found neither in request header: X-Openai-Api-Key nor in environment variable under OPENAI_APIKEY}][{API Key: no api key found neither in request header: X-Openai-Api-Key nor in environment variable under OPENAI_APIKEY}] Some objects failed to insert: %v - 
goroutine 30 [running]:
runtime/debug.Stack()
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/runtime/debug/stack.go:26 +0x64
github.com/precize/logger.Print({0x10149f2f9, 0x5}, {0x14000162240, 0x11c}, {0x140002c93f0, 0x1, 0x1})
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/logger/log.go:121 +0x950
github.com/precize/pserver/server/weaviate.InsertData({0x10191a6a8, 0x14000292000}, 0x140002308c0)
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/pserver/server/weaviate/handlers.go:234 +0x9d0
net/http.HandlerFunc.ServeHTTP(0x101907b28, {0x10191a6a8, 0x14000292000}, 0x140002308c0)
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2220 +0x40
github.com/precize/pserver/server.verifyMiddleware.func1({0x10191a6a8, 0x14000292000}, 0x140002308c0)
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/pserver/server/pserver.go:64 +0x4c
net/http.HandlerFunc.ServeHTTP(0x1400001c7c8, {0x10191a6a8, 0x14000292000}, 0x140002308c0)
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2220 +0x40
github.com/gorilla/mux.(*Router).ServeHTTP(0x14000140000, {0x10191a6a8, 0x14000292000}, 0x140002308c0)
	/Users/<USER>/go/pkg/mod/github.com/gorilla/mux@v1.8.1/mux.go:212 +0x254
net/http.serverHandler.ServeHTTP({0x1400020c3c0}, {0x10191a6a8, 0x14000292000}, 0x14000230640)
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:3210 +0x2b0
net/http.(*conn).serve(0x140004419e0, {0x10191bf28, 0x1400029a410})
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2092 +0x1604
created by net/http.(*Server).Serve in goroutine 1
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:3360 +0x88c
2025/09/09 15:44:31 Weaviate connection status: %v - true - 
2025/09/09 15:45:04 Weaviate connection status: %v - true - 
2025/09/09 15:45:34 Got error connecting to elasticsearch - {Addresses:[http://**********:9200] Username: Password: CloudID: APIKey: ServiceToken: CertificateFingerprint: Header:map[] CACert:[] RetryOnStatus:[] DisableRetry:false EnableRetryOnTimeout:false MaxRetries:0 CompressRequestBody:false DiscoverNodesOnStart:false DiscoverNodesInterval:0s EnableMetrics:false EnableDebugLogger:false EnableCompatibilityMode:false DisableMetaHeader:false UseResponseCheckOnly:false RetryBackoff:<nil> Transport:<nil> Logger:<nil> Selector:<nil> ConnectionPoolFunc:<nil>} - dial tcp **********:9200: i/o timeout - 
goroutine 1 [running]:
runtime/debug.Stack()
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/runtime/debug/stack.go:26 +0x64
github.com/precize/logger.Print({0x105c973b9, 0x5}, {0x140000a0280, 0x248}, {0x1400059fe00, 0x2, 0x2})
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/logger/log.go:121 +0x950
github.com/precize/elastic.ConnectToElasticSearch()
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/elastic/elastic.go:75 +0x68c
main.main()
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/pserver/main.go:31 +0x1c4
2025/09/09 15:45:59 Weaviate connection status: %v - true - 
2025/09/09 15:46:26 Weaviate connection status: %v - true - 
2025/09/09 15:46:26 Application config could not be read. Starting with defaults -  - 
2025/09/09 15:46:27 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/09 15:46:27 Index iac_git_commits exists - 
2025/09/09 15:46:27 Index cfstack_templates exists - 
2025/09/09 15:46:27 Index arm_templates exists - 
2025/09/09 15:46:27 Index terraform_resources exists - 
2025/09/09 15:46:27 Index tf_commits exists - 
2025/09/09 15:46:27 Index tf_variables exists - 
2025/09/09 15:46:27 Index resource_context exists - 
2025/09/09 15:46:27 Index text_lookup exists - 
2025/09/09 15:46:27 Index ai_resources exists - 
2025/09/09 15:46:27 Index idp_events exists - 
2025/09/09 15:46:27 Index idp_users exists - 
2025/09/09 15:46:27 Index idp_apps exists - 
2025/09/09 15:46:27 Index idp_groups exists - 
2025/09/09 15:46:27 Index cloud_incidents exists - 
2025/09/09 15:46:27 Index jira_issues exists - 
2025/09/09 15:46:27 Index jira_data exists - 
2025/09/09 15:46:28 Index jira_resources exists - 
2025/09/09 15:46:28 Index precize_creations exists - 
2025/09/09 15:46:28 Index external_cloud_resources exists - 
2025/09/09 15:46:28 Starting server on port 19090 - 

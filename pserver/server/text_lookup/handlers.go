package textlookup

import (
	"encoding/json"
	"io/ioutil"
	"net/http"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

type AddAppsToTextLookupRequest struct {
	IgnoreAppNames []string `json:"ignoreAppNames"`
	TenantID       string   `json:"tenantID"`
}

type Response struct {
	Success bool   `json:"success"`
	Message string `json:"message,omitempty"`
	Data    any    `json:"data,omitempty"`
}

func AddIgnoreApps(w http.ResponseWriter, r *http.Request) {

	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	body, err := ioutil.ReadAll(r.Body)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to read request body: %v", err)
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}

	var req AddAppsToTextLookupRequest
	if err := json.Unmarshal(body, &req); err != nil {
		logger.Print(logger.ERROR, "Failed to parse request JSON: %v", err)
		http.Error(w, "Invalid JSON format", http.StatusBadRequest)
		return
	}

	if len(req.IgnoreAppNames) == 0 {
		http.Error(w, "ignoreAppNames list cannot be empty", http.StatusBadRequest)
		return
	}

	if req.TenantID == "" {
		http.Error(w, "tenantID is required", http.StatusBadRequest)
		return
	}

	bulkTextLookUpRequest := ""
	for _, app := range req.IgnoreAppNames {
		textLookupDocID := common.GenerateCombinedHashID("ignoreGlobalApp:::" + strings.ToLower(app))
		doc := common.TextLookupInsertDoc{
			Text:       app,
			HasName:    true,
			TenantID:   req.TenantID,
			InsertTime: elastic.DateTime(time.Now().UTC()),
			Category:   "Ignore AI Detected Global App",
		}

		textLookUpMetadata := `{"index": {"_id": "` + textLookupDocID + `"}}`
		textLookUpInsertDoc, err := json.Marshal(doc)
		if err != nil {
			logger.Print(logger.ERROR, "Got error marshalling document", err)
			continue
		}

		bulkTextLookUpRequest += textLookUpMetadata + "\n" + string(textLookUpInsertDoc) + "\n"
	}

	if len(bulkTextLookUpRequest) > 0 {
		if err := elastic.BulkDocumentsAPI(req.TenantID, elastic.TEXT_LOOKUP_INDEX, bulkTextLookUpRequest); err != nil {
			logger.Print(logger.ERROR, "Failed to add apps to text lookup: %v", err)
			http.Error(w, "Failed to add apps to text lookup", http.StatusInternalServerError)
			return
		}
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(Response{
		Success: true,
		Message: "Successfully added apps to text lookup",
	})
}

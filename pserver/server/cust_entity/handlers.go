package custentity

import (
	"encoding/json"
	"io/ioutil"
	"net/http"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

const (
	ORG_CONTEXT_TYPE   = "org"
	APP_PROPERTY_NAME  = "app"
	TEAM_PROPERTY_NAME = "team"
)

type EntityContextRequest struct {
	TenantID          string                           `json:"tenantId"`
	UpdatedBy         string                           `json:"updatedBy"`
	Overwrite         bool                             `json:"overwrite"`
	ContextProperties []common.EntityContextProperties `json:"contextProperties"`
}

type EntityContextResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message,omitempty"`
}

func UpdateCustomerEntityContext(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	body, err := ioutil.ReadAll(r.Body)
	if err != nil {
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}

	var request EntityContextRequest
	if err := json.Unmarshal(body, &request); err != nil {
		http.Error(w, "Failed to parse JSON request", http.StatusBadRequest)
		return
	}

	if request.TenantID == "" {
		http.Error(w, "TenantID is required", http.StatusBadRequest)
		return
	}

	if request.UpdatedBy == "" {
		http.Error(w, "UpdatedBy is required", http.StatusBadRequest)
		return
	}

	if len(request.ContextProperties) == 0 {
		http.Error(w, "At least one context property is required", http.StatusBadRequest)
		return
	}

	resourceContext := &common.ResourceContextInsertDoc{
		TenantID: request.TenantID,
	}

	docID := common.GenerateCombinedHashID(ORG_CONTEXT_TYPE, resourceContext.TenantID)

	orgContext, err := elastic.GetDocument(elastic.CUST_ENTITY_CONTEXT_INDEX, docID)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to get document", err)
		http.Error(w, "Failed to retrieve entity context", http.StatusInternalServerError)
		return
	}

	response := EntityContextResponse{Success: true}

	if len(orgContext) > 0 {
		orgContextJson, err := json.Marshal(orgContext)
		if err != nil {
			logger.Print(logger.ERROR, "Failed to marshal", err)
			http.Error(w, "Failed to process entity context", http.StatusInternalServerError)
			return
		}

		var orgEntityContext common.CustomerEntityContextDoc

		if err = json.Unmarshal(orgContextJson, &orgEntityContext); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", err)
			http.Error(w, "Failed to process entity context", http.StatusInternalServerError)
			return
		}

		if orgEntityContext.ContextProperties == nil {
			orgEntityContext.ContextProperties = []common.EntityContextProperties{}
		}

		for _, requestProperty := range request.ContextProperties {
			propertyFound := false

			for i, existingProperty := range orgEntityContext.ContextProperties {
				if existingProperty.PropertyName == requestProperty.PropertyName {
					tmp := orgEntityContext.ContextProperties[i]

					if len(requestProperty.Include) > 0 {

						if request.Overwrite {
							tmp.Include = append(requestProperty.Include, []string{}...)
						} else {
							tmp.Include = append(tmp.Include, requestProperty.Include...)
						}

						tmp.Include = common.RemoveDuplicatesFromStringSlice(tmp.Include)
					}

					if len(requestProperty.Exclude) > 0 {
						if request.Overwrite {
							tmp.Exclude = append(requestProperty.Exclude, []string{}...)
						} else {
							tmp.Exclude = append(tmp.Exclude, requestProperty.Exclude...)
						}

						tmp.Exclude = common.RemoveDuplicatesFromStringSlice(tmp.Exclude)
					}

					orgEntityContext.ContextProperties[i] = tmp
					propertyFound = true
					break
				}
			}

			if !propertyFound {

				requestProperty.Include = common.RemoveDuplicatesFromStringSlice(requestProperty.Include)
				requestProperty.Exclude = common.RemoveDuplicatesFromStringSlice(requestProperty.Exclude)
				orgEntityContext.ContextProperties = append(orgEntityContext.ContextProperties, requestProperty)
			}
		}

		if len(orgEntityContext.InsertTime) <= 0 {
			orgEntityContext.InsertTime = elastic.DateTime(time.Now().UTC())
		}

		orgEntityContext.UpdatedBy = request.UpdatedBy
		orgEntityContext.UpdateTime = elastic.DateTime(time.Now().UTC())

		if _, err = elastic.InsertDocument(resourceContext.TenantID, elastic.CUST_ENTITY_CONTEXT_INDEX, orgEntityContext, docID); err != nil {
			logger.Print(logger.ERROR, "Failed to insert document", err)
			http.Error(w, "Failed to update entity context", http.StatusInternalServerError)
			return
		}

		response.Message = "Entity context updated successfully"
	} else {
		orgEntityContext := common.CustomerEntityContextDoc{
			InsertTime:        elastic.DateTime(time.Now().UTC()),
			TenantID:          resourceContext.TenantID,
			UpdateTime:        elastic.DateTime(time.Now().UTC()),
			ID:                docID,
			Type:              ORG_CONTEXT_TYPE,
			UpdatedBy:         request.UpdatedBy,
			ContextProperties: request.ContextProperties,
		}

		if _, err = elastic.InsertDocument(resourceContext.TenantID, elastic.CUST_ENTITY_CONTEXT_INDEX, orgEntityContext, docID); err != nil {
			logger.Print(logger.ERROR, "Failed to insert document", err)
			http.Error(w, "Failed to create entity context", http.StatusInternalServerError)
			return
		}

		response.Message = "Entity context created successfully"
	}

	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)
}

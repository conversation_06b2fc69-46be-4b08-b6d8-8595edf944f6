package server

import (
	"net/http"

	"github.com/gorilla/mux"

	"github.com/precize/email"
	"github.com/precize/logger"
	"github.com/precize/pserver/server/auth"
	custentity "github.com/precize/pserver/server/cust_entity"
	"github.com/precize/pserver/server/jira"
	textlookup "github.com/precize/pserver/server/text_lookup"
	"github.com/precize/pserver/server/weaviate"
)

func StartProviderServer() {

	defer func() {
		if r := recover(); r != nil {
			logger.Print(logger.ERROR, "Panic occured", r)
			email.SendPanicEmail("pserver")
		}
	}()

	r := mux.NewRouter()

	r.HandleFunc("/provider/auth/validate", auth.Validate).Methods("POST")

	r.HandleFunc("/provider/jira/issue/create", jira.CreateJiraIssue).Methods("POST")
	r.HandleFunc("/provider/jira/project/list", jira.ListJiraProjects).Methods("POST")
	r.HandleFunc("/provider/jira/issuetypefield/list", jira.ListJiraIssueTypeFields).Methods("POST")

	r.HandleFunc("/provider/custentity/update", custentity.UpdateCustomerEntityContext).Methods("POST")
	r.HandleFunc("/provider/text-lookup/ignore-apps", textlookup.AddIgnoreApps).Methods("POST")

	r.HandleFunc("/provider/weaviate/class/create", weaviate.CreateClass).Methods("POST")
	r.HandleFunc("/provider/weaviate/class/get", weaviate.GetClass).Methods("POST")
	r.HandleFunc("/provider/weaviate/class/delete", weaviate.DeleteClass).Methods("POST")
	r.HandleFunc("/provider/weaviate/data/insert", weaviate.InsertData).Methods("POST")
	r.HandleFunc("/provider/weaviate/search/all", weaviate.SearchAll).Methods("POST")
	r.HandleFunc("/provider/weaviate/search/id", weaviate.SearchByID).Methods("POST")
	r.HandleFunc("/provider/weaviate/search/similar", weaviate.SearchSimilar).Methods("POST")
	r.HandleFunc("/provider/weaviate/delete/id", weaviate.DeleteByID).Methods("POST")
	r.HandleFunc("/provider/weaviate/delete/query", weaviate.DeleteByQuery).Methods("POST")

	r.Use(verifyMiddleware)

	// Start the server
	logger.Print(logger.INFO, "Starting server on port 19090")
	err := http.ListenAndServe(":19090", r)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to start server", err)
		return
	}
}

func verifyMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {

		// TODO: Any additional verifications
		next.ServeHTTP(w, r)
	})
}

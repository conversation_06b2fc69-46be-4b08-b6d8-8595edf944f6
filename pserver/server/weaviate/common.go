package weaviate

import "github.com/weaviate/weaviate-go-client/v5/weaviate/filters"

func buildWhereFilter(filter SearchFilter) *filters.WhereBuilder {
	whereFilter := filters.Where().WithPath([]string{filter.Path})

	switch filter.Operator {
	case "Equal":
		return whereFilter.WithOperator(filters.Equal).WithValueString(filter.Value)
	case "NotEqual":
		return whereFilter.WithOperator(filters.NotEqual).WithValueString(filter.Value)
	case "GreaterThan":
		return whereFilter.WithOperator(filters.GreaterThan).WithValueString(filter.Value)
	case "GreaterThanEqual":
		return whereFilter.WithOperator(filters.GreaterThanEqual).WithValueString(filter.Value)
	case "LessThan":
		return whereFilter.WithOperator(filters.LessThan).WithValueString(filter.Value)
	case "LessThanEqual":
		return whereFilter.WithOperator(filters.LessThanEqual).WithValueString(filter.Value)
	case "Like":
		return whereFilter.WithOperator(filters.Like).WithValueString(filter.Value)
	case "WithinGeoRange":
		return whereFilter.WithOperator(filters.WithinGeoRange).WithValueString(filter.Value)
	default:
		return whereFilter.WithOperator(filters.Equal).WithValueString(filter.Value)
	}
}

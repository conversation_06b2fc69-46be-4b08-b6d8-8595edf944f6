package weaviate

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"strings"

	"github.com/precize/common"
	"github.com/precize/config"
	"github.com/precize/logger"
	"github.com/weaviate/weaviate-go-client/v5/weaviate"
	"github.com/weaviate/weaviate-go-client/v5/weaviate/graphql"
	"github.com/weaviate/weaviate/entities/models"
)

var weaviateClient *weaviate.Client

func init() {

	logger.InitializeLogs("pserver", false)

	host := config.AppConfig.Weaviate.Host
	if host == "" {
		host = "localhost:8080"

	}
	scheme := config.AppConfig.Weaviate.Scheme
	if scheme == "" {
		scheme = "http"

	}

	openAIAPIKey := config.AppConfig.Weaviate.OpenAIAPIKey

	cfg := weaviate.Config{
		Host:   host,
		Scheme: scheme,
		Headers: map[string]string{
			"X-OpenAI-Api-Key": openAIAPIKey,
		},
	}

	client, err := weaviate.NewClient(cfg)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to init Weaviate client: %v", err)
		return
	}

	ctx := context.Background()
	live, err := client.Misc().LiveChecker().Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "Weaviate not reachable: %v", err)
		return
	}

	logger.Print(logger.INFO, "Weaviate connection status: %v", live)
	weaviateClient = client
	weaviateClient = client
}

func CreateClass(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	body, err := io.ReadAll(r.Body)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to read request body: %v", err)
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}

	var req CreateClassRequest
	if err := json.Unmarshal(body, &req); err != nil {
		logger.Print(logger.ERROR, "Failed to parse request JSON: %v", err)
		http.Error(w, "Invalid JSON format", http.StatusBadRequest)
		return
	}

	if req.ClassName == "" {
		http.Error(w, "className is required", http.StatusBadRequest)
		return
	}

	if req.VectorName == "" {
		http.Error(w, "vectorName is required", http.StatusBadRequest)
		return
	}
	if len(req.Properties) == 0 {
		http.Error(w, "properties list cannot be empty", http.StatusBadRequest)
		return
	}

	if req.ModelName == "" {
		req.ModelName = "text-embedding-3-small"
	}

	if req.Dimensions == 0 {
		req.Dimensions = 1536
	}

	if req.VectorIndexType == "" {
		req.VectorIndexType = "hnsw"
	}

	ctx := context.Background()

	if weaviateClient == nil {
		logger.Print(logger.ERROR, "Weaviate client not initialized")
		http.Error(w, "Weaviate client not initialized", http.StatusInternalServerError)
		return
	}

	exists, err := weaviateClient.Schema().ClassExistenceChecker().WithClassName(req.ClassName).Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to check class existence: %v", err)
		http.Error(w, "Failed to check class existence", http.StatusInternalServerError)
		return
	}

	if exists {
		logger.Print(logger.INFO, "Class '%s' already exists, skipping creation", req.ClassName)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(Response{
			Success: true,
			Message: fmt.Sprintf("Class '%s' already exists, skipping creation", req.ClassName),
		})

		return
	}

	vectorizer := map[string]interface{}{
		"text2vec-openai": map[string]interface{}{
			"properties": req.Properties,
			"model":      req.ModelName,
			"dimensions": req.Dimensions,
		},
	}

	class := &models.Class{
		Class: req.ClassName,
		VectorConfig: map[string]models.VectorConfig{
			req.VectorName: {
				Vectorizer:      vectorizer,
				VectorIndexType: req.VectorIndexType,
			},
		},
	}

	err = weaviateClient.Schema().ClassCreator().WithClass(class).Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to create class in weaviate: %v", err)
		http.Error(w, "Failed to create class", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(Response{
		Success: true,
		Message: fmt.Sprintf("Successfully created class '%s'", req.ClassName),
	})
}

func InsertData(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	body, err := io.ReadAll(r.Body)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to read request body: %v", err)
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}

	var req InsertDataRequest
	if err := json.Unmarshal(body, &req); err != nil {
		logger.Print(logger.ERROR, "Failed to parse request JSON: %v", err)
		http.Error(w, "Invalid JSON format", http.StatusBadRequest)
		return
	}

	if req.ClassName == "" {
		http.Error(w, "className is required", http.StatusBadRequest)
		return
	}
	if len(req.Objects) == 0 {
		http.Error(w, "objects list cannot be empty", http.StatusBadRequest)
		return
	}

	ctx := context.Background()
	if weaviateClient == nil {
		logger.Print(logger.ERROR, "Weaviate client not initialized")
		http.Error(w, "Weaviate client not initialized", http.StatusInternalServerError)
		return
	}

	batcher := weaviateClient.Batch().ObjectsBatcher()
	for _, obj := range req.Objects {
		batcher = batcher.WithObjects(&models.Object{
			Class:      req.ClassName,
			Properties: obj,
		})
	}

	batchRes, err := batcher.Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to execute batch insert: %v", err)
		http.Error(w, "Failed to insert data", http.StatusInternalServerError)
		return
	}

	var errorMsgs []string
	for _, res := range batchRes {
		if res.Result.Errors != nil {
			for _, batchErr := range res.Result.Errors.Error {
				if batchErr != nil {
					errorMsgs = append(errorMsgs, fmt.Sprintf("%v", *batchErr))
				}
			}
		}
	}

	if len(errorMsgs) > 0 {
		logger.Print(logger.ERROR, "Some objects failed to insert: %v", errorMsgs)
		http.Error(w, fmt.Sprintf("Some objects failed to insert: %v", errorMsgs), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(Response{
		Success: true,
		Message: fmt.Sprintf("Successfully inserted %d objects into class '%s'", len(req.Objects), req.ClassName),
	})
}

func SearchSimilar(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	body, err := ioutil.ReadAll(r.Body)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to read request body: %v", err)
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}

	var req SearchSimilarRequest
	if err := json.Unmarshal(body, &req); err != nil {
		logger.Print(logger.ERROR, "Failed to parse request JSON: %v", err)
		http.Error(w, "Invalid JSON format", http.StatusBadRequest)
		return
	}

	if req.ClassName == "" {
		http.Error(w, "className is required", http.StatusBadRequest)
		return
	}

	if req.Query == "" {
		http.Error(w, "query is required", http.StatusBadRequest)
		return
	}

	if len(req.Fields) == 0 {
		http.Error(w, "fields list cannot be empty", http.StatusBadRequest)
		return
	}

	if req.Alpha == nil {
		req.Alpha = common.Float32Ptr(1.0)
	}

	if req.Limit == 0 {
		req.Limit = 10
	}

	ctx := context.Background()
	if weaviateClient == nil {
		logger.Print(logger.ERROR, "Weaviate client not initialized")
		http.Error(w, "Weaviate client not initialized", http.StatusInternalServerError)
		return
	}

	gqlBuilder := weaviateClient.GraphQL().Get().
		WithClassName(req.ClassName).
		WithHybrid(weaviateClient.GraphQL().HybridArgumentBuilder().WithQuery(req.Query).WithAlpha(*req.Alpha)).
		WithLimit(req.Limit)

	for _, fieldName := range req.Fields {
		gqlBuilder = gqlBuilder.WithFields(graphql.Field{Name: fieldName})
	}

	for _, filter := range req.Filters {
		whereFilter := buildWhereFilter(filter)
		gqlBuilder = gqlBuilder.WithWhere(whereFilter)
	}

	result, err := gqlBuilder.Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "Search failed: %v", err)
		http.Error(w, "Search failed", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(Response{
		Success: true,
		Message: fmt.Sprintf("Successfully searched class '%s'", req.ClassName),
		Data:    result,
	})
}

func DeleteByID(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	body, err := io.ReadAll(r.Body)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to read request body: %v", err)
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}

	var req DeleteByIDRequest
	if err := json.Unmarshal(body, &req); err != nil {
		logger.Print(logger.ERROR, "Failed to parse request JSON: %v", err)
		http.Error(w, "Invalid JSON format", http.StatusBadRequest)
		return
	}

	if req.ClassName == "" {
		http.Error(w, "className is required", http.StatusBadRequest)
		return
	}

	if req.ID == "" {
		http.Error(w, "id is required", http.StatusBadRequest)
		return
	}

	ctx := context.Background()
	if weaviateClient == nil {
		logger.Print(logger.ERROR, "Weaviate client not initialized")
		http.Error(w, "Weaviate client not initialized", http.StatusInternalServerError)
		return
	}

	err = weaviateClient.Data().Deleter().
		WithClassName(req.ClassName).
		WithID(req.ID).
		Do(ctx)

	if err != nil {
		logger.Print(logger.ERROR, "Failed to delete object: %v", err)
		http.Error(w, "Failed to delete object", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(Response{
		Success: true,
		Message: fmt.Sprintf("Successfully deleted object with ID '%s' from class '%s'", req.ID, req.ClassName),
	})
}

func DeleteByQuery(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	body, err := io.ReadAll(r.Body)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to read request body: %v", err)
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}

	var req DeleteByQueryRequest
	if err := json.Unmarshal(body, &req); err != nil {
		logger.Print(logger.ERROR, "Failed to parse request JSON: %v", err)
		http.Error(w, "Invalid JSON format", http.StatusBadRequest)
		return
	}

	if req.ClassName == "" {
		http.Error(w, "className is required", http.StatusBadRequest)
		return
	}

	if len(req.Filters) == 0 {
		http.Error(w, "filters list cannot be empty", http.StatusBadRequest)
		return
	}

	if req.Output == "" {
		req.Output = "minimal"
	}

	ctx := context.Background()
	if weaviateClient == nil {
		logger.Print(logger.ERROR, "Weaviate client not initialized")
		http.Error(w, "Weaviate client not initialized", http.StatusInternalServerError)
		return
	}

	batchDeleter := weaviateClient.Batch().ObjectsBatchDeleter().
		WithClassName(req.ClassName).
		WithOutput(req.Output)

	for _, filter := range req.Filters {
		whereFilter := buildWhereFilter(filter)
		batchDeleter = batchDeleter.WithWhere(whereFilter)
	}

	response, err := batchDeleter.Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to execute batch delete: %v", err)
		http.Error(w, "Failed to execute batch delete", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(Response{
		Success: true,
		Message: fmt.Sprintf("Successfully executed batch delete on class '%s'", req.ClassName),
		Data:    response,
	})
}

func DeleteClass(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	body, err := io.ReadAll(r.Body)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to read request body: %v", err)
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}

	var req DeleteClassRequest
	if err := json.Unmarshal(body, &req); err != nil {
		logger.Print(logger.ERROR, "Failed to parse request JSON: %v", err)
		http.Error(w, "Invalid JSON format", http.StatusBadRequest)
		return
	}

	if req.ClassName == "" {
		http.Error(w, "className is required", http.StatusBadRequest)
		return
	}

	ctx := context.Background()
	if weaviateClient == nil {
		logger.Print(logger.ERROR, "Weaviate client not initialized")
		http.Error(w, "Weaviate client not initialized", http.StatusInternalServerError)
		return
	}

	err = weaviateClient.Schema().ClassDeleter().WithClassName(req.ClassName).Do(ctx)
	if err != nil {
		if strings.Contains(err.Error(), "400") || strings.Contains(strings.ToLower(err.Error()), "not found") {
			logger.Print(logger.INFO, "Class '%s' does not exist, deletion skipped", req.ClassName)
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			json.NewEncoder(w).Encode(Response{
				Success: true,
				Message: fmt.Sprintf("Class '%s' does not exist, deletion skipped", req.ClassName),
			})
			return
		}

		logger.Print(logger.ERROR, "Failed to delete class: %v", err)
		http.Error(w, "Failed to delete class", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(Response{
		Success: true,
		Message: fmt.Sprintf("Successfully deleted class '%s'", req.ClassName),
	})
}

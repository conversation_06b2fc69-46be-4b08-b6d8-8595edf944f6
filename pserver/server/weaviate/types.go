package weaviate

type Response struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
}

type CreateClassRequest struct {
	ClassName    string                  `json:"className"`
	Description  string                  `json:"description,omitempty"`
	Properties   []ClassProperty         `json:"properties"`
	VectorConfig map[string]VectorConfig `json:"vectorConfig"`
}

type ClassProperty struct {
	Name     string   `json:"name"`
	DataType []string `json:"dataType"`
}

type VectorConfig struct {
	Vectorizer        map[string]any `json:"vectorizer"`
	VectorIndexType   string         `json:"vectorIndexType"`
	VectorIndexConfig any            `json:"vectorIndexConfig,omitempty"`
}

type InsertDataRequest struct {
	ClassName string           `json:"className"`
	Objects   []map[string]any `json:"objects"`
}

type SearchSimilarRequest struct {
	ClassName string         `json:"className"`
	Query     string         `json:"query"`
	Fields    []string       `json:"fields"`
	Alpha     *float32       `json:"alpha"`
	Filters   []SearchFilter `json:"filters"`
	Limit     int            `json:"limit"`
}

type SearchFilter struct {
	Path     string `json:"path"`
	Operator string `json:"operator"`
	Value    string `json:"value"`
}

type DeleteByIDRequest struct {
	ClassName string `json:"className"`
	ID        string `json:"id"`
}

type DeleteByQueryRequest struct {
	ClassName string         `json:"className"`
	Filters   []SearchFilter `json:"filters"`
	Output    string         `json:"output,omitempty"`
}

type DeleteClassRequest struct {
	ClassName string `json:"className"`
}

type GetClassRequest struct {
	ClassName string `json:"className"`
}

type SearchAllRequest struct {
	ClassName     string         `json:"className"`
	Fields        []string       `json:"fields"`
	BatchSize     int            `json:"batchSize,omitempty"`
	Limit         int            `json:"limit,omitempty"`
	Cursor        string         `json:"cursor,omitempty"`
	IncludeVector bool           `json:"includeVector,omitempty"`
	Filters       []SearchFilter `json:"filters,omitempty"`
}

type SearchByIDRequest struct {
	ClassName string `json:"className"`
	ID        string `json:"id"`
}

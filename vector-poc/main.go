package main

import (
	"context"
	"fmt"

	"github.com/weaviate/weaviate-go-client/v5/weaviate"
	"github.com/weaviate/weaviate-go-client/v5/weaviate/filters"
	"github.com/weaviate/weaviate-go-client/v5/weaviate/graphql"
	"github.com/weaviate/weaviate/entities/models"
)

func main() {
	ctx := context.Background()

	client, err := initClient(ctx)
	if err != nil {
		panic(err)
	}

	// err = createSchema(ctx, client)
	// if err != nil {
	// 	panic(err)
	// }

	// objects := generateResourceObjects()
	// err = insertResources(ctx, client, objects)
	// if err != nil {
	// 	panic(err)
	// }

	searchResources(ctx, client, "eks cluster", "0R8Da4gBoELr5xpoQ6Y3", "1000")
	// searchResources(ctx, client, "storage bucket", "DfOPRYoB9TBTHED5xyZs", "3000")
}

func initClient(ctx context.Context) (*weaviate.Client, error) {
	cfg := weaviate.Config{
		Host:   "localhost:8080", // or localhost:18090 if using forwarding
		Scheme: "http",
		Headers: map[string]string{
			"X-OpenAI-Api-Key": "***********************************************************************************************************************************************************************",
		},
	}
	client, err := weaviate.NewClient(cfg)
	if err != nil {
		return nil, err
	}

	live, err := client.Misc().LiveChecker().Do(ctx)
	if err != nil {
		fmt.Println("Connection Failed!!", err)
		return nil, err
	}

	fmt.Printf("%v", live)

	return client, nil
}

func createSchema(ctx context.Context, client *weaviate.Client) error {
	class := &models.Class{
		Class: "ResourceName",
		VectorConfig: map[string]models.VectorConfig{
			"rscname_vector": {
				Vectorizer: map[string]interface{}{
					"text2vec-openai": map[string]interface{}{
						"properties": []string{"name"},
						"model":      "text-embedding-3-small",
						"dimensions": 1536,
					},
				},
				VectorIndexType: "hnsw",
			},
		},
	}

	// Delete if exists (for reruns)
	err := client.Schema().ClassDeleter().WithClassName("ResourceName").Do(ctx)
	if err != nil {
		fmt.Println(err)
	}

	err = client.Schema().ClassCreator().WithClass(class).Do(ctx)
	if err != nil {
		return err
	}

	return nil
}

func generateResourceObjects() []map[string]interface{} {
	tenant1 := "0R8Da4gBoELr5xpoQ6Y3"
	tenant2 := "DfOPRYoB9TBTHED5xyZs"

	awsResources := []string{
		"avinash-test-bucket", "prod-data-archive-bucket", "temp-logs-to-delete",
		"ec2-dev-instance-01", "vpc-network-prod-west1", "iam-role-ci-runner",
		"lambda-image-resize-func", "rds-customer-prod", "dynamodb-user-sessions",
		"eks-cluster-payments-prod", "elb-apigateway-prod",
	}

	gcpResources := []string{
		"avinash-test-storage", "prod-exports-bucket", "temp-storage-to-delete",
		"compute-dev-instance1", "vpc-network-central-prod", "iam-role-data-pipeline",
		"cloudfn-generate-reports", "sql-customer-prod-db", "bigtable-session-store",
		"gke-cluster-analytics-prod", "lb-apigateway",
	}

	var objs []map[string]interface{}

	for _, name := range awsResources {
		objs = append(objs, map[string]interface{}{"name": name, "tenantId": tenant1, "serviceId": "1000", "json": "abcdcdcdcdcdcd"})
	}
	for _, name := range gcpResources {
		objs = append(objs, map[string]interface{}{"name": name, "tenantId": tenant1, "serviceId": "3000"})
	}

	for _, name := range awsResources {
		objs = append(objs, map[string]interface{}{"name": name, "tenantId": tenant2, "serviceId": "1000"})
	}
	for _, name := range gcpResources {
		objs = append(objs, map[string]interface{}{"name": name, "tenantId": tenant2, "serviceId": "3000"})
	}

	return objs
}

func insertResources(ctx context.Context, client *weaviate.Client, objects []map[string]interface{}) error {
	batcher := client.Batch().ObjectsBatcher()
	for _, obj := range objects {
		batcher = batcher.WithObjects(&models.Object{
			Class:      "ResourceName",
			Properties: obj,
		})
	}

	batchRes, err := batcher.Do(ctx)
	if err != nil {
		return err
	}
	for _, res := range batchRes {
		if res.Result.Errors != nil {
			for _, err := range res.Result.Errors.Error {
				if err != nil {
					return fmt.Errorf("insert error: %s", *err)
				}
			}
		}
	}
	return nil
}

func searchResources(ctx context.Context, client *weaviate.Client, query, tenantId, serviceId string) {
	result, err := client.GraphQL().Get().
		WithClassName("ResourceName").
		WithFields(graphql.Field{Name: "name"}, graphql.Field{Name: "tenantId"}, graphql.Field{Name: "serviceId"}).
		// WithNearText(client.GraphQL().NearTextArgBuilder().
		// 	WithConcepts([]string{query}).WithDistance(0.7)).
		WithHybrid(client.GraphQL().HybridArgumentBuilder().WithQuery(query).WithAlpha(1)).
		WithWhere(filters.Where().
			WithPath([]string{"tenantId"}).
			WithOperator(filters.Equal).
			WithValueString(tenantId)).
		WithWhere(filters.Where().
			WithPath([]string{"serviceId"}).
			WithOperator(filters.Equal).
			WithValueString(serviceId)).
		WithLimit(10).
		Do(ctx)

	if err != nil {
		fmt.Println("Search error:", err)
		return
	}

	fmt.Printf("\n🔍 Results for query '%s' (tenant=%s, service=%s):\n%v\n", query, tenantId, serviceId, result)
}

package transport

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/precize/config"
	"github.com/precize/logger"
)

const (
	serverQAToken   = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIlQzMlOTElQzIlODglMkYlQzMlOTElQzIlOUQlMkIlQzIlOENrJUMzJTk4JTIxJTE0JUMyJUI3WCVDMyU4MyVDMiVCQSVDMiVCRiVDMyU5QSVDMyVCMiVDMiVBNSU1QyVDMiVBMiU0MCUyQyUyM3glQzMlOEMlMEElMjklQzMlOTglQzMlOEYlQzIlOEElQzIlOEIlQzMlOUYlQzMlQUElQzIlOTQlQzIlOUUlQzIlODQlQzIlQjYlMTgiLCJhdXRoIjoiU3lzdGVtIEFkbWluLFJPTEVfQU5PTllNT1VTIiwiZXhwIjoxNzc2NzYzNDQyfQ.YS0YJHA3FY11DFxUTckxvYFrIJmycf_fCjFMM12bSy5OOI1lgi3Tvbp7vgVbSCRhXqpvuxyua_c9d1pBHVRuTQ"
	serverProdToken = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIlQzMlOTElQzIlODglMkYlQzMlOTElQzIlOUQlMkIlQzIlOENrJUMzJTk4JTIxJTE0JUMyJUI3WCVDMyU4MyVDMiVCQSVDMiVCRiVDMyU5QSVDMyVCMiVDMiVBNSU1QyVDMiVBMiU0MCUyQyUyM3glQzMlOEMlMEElMjklQzMlOTglQzMlOEYlQzIlOEElQzIlOEIlQzMlOUYlQzMlQUElQzIlOTQlQzIlOUUlQzIlODQlQzIlQjYlMTgiLCJhdXRoIjoiU3lzdGVtIEFkbWluLFJPTEVfQU5PTllNT1VTIiwiZXhwIjoxNzc2NzYzNzM3fQ.uJWvZS9_hm4Ds8GsGJ4jiNdB_3XvU65JjHaF4YZzr5kZrLzHW476WYik3fTBwZEhARH5Fbse1iKKHQhVs7sKkg"
)

var (
	serverAPIToken string
	HttpClient     *http.Client
	baseURL        string
)

type ServerResponseInfo struct {
	Message   string `json:"message"`
	Status    int    `json:"status"`
	Error     string `json:"error"`
	ErrorCode int    `json:"errorCode"`
}

type RequestOptions struct {
	HandledErrorCodes []int
	ErrorMsg          string
}

type RequestOption func(*RequestOptions)

func WithHandledErrorCodes(codes ...int) RequestOption {
	return func(opts *RequestOptions) {
		opts.HandledErrorCodes = codes
	}
}

func WithHandledErrorMsg(msg string) RequestOption {
	return func(opts *RequestOptions) {
		opts.ErrorMsg = msg
	}
}

func SetHttpClient() {

	serverAPIToken = serverQAToken
	scanServer := config.AppConfig.ScanServer

	if scanServer.Port != 0 {
		baseURL = fmt.Sprintf("%s://%s:%d", scanServer.Scheme, scanServer.Host, scanServer.Port)
	} else if config.AppConfig.Server.Port != 0 {
		baseURL = fmt.Sprintf("http://localhost:%d", config.AppConfig.Server.Port)
	}

	if config.Environment == config.PROD_ENV {
		serverAPIToken = serverProdToken
	}

	HttpClient = &http.Client{
		Timeout: 2 * time.Minute,
		Transport: &http.Transport{
			MaxIdleConns:    10,
			IdleConnTimeout: time.Duration(5) * time.Minute,
		},
	}
}

func SendRequestToServer(method, url string, urlParams map[string]string, reqBody io.Reader) ([]byte, error) {

	serverHost := baseURL
	if len(serverHost) <= 0 {
		serverHost = "http://localhost:8889"
	}

	url = serverHost + url
	headers := make(map[string]string)
	headers["Authorization"] = "Bearer " + serverAPIToken
	return SendRequest(method, url, urlParams, headers, reqBody)
}

func SendRequest(method, url string, urlParams, headers map[string]string, reqBody io.Reader, opts ...RequestOption) ([]byte, error) {

	var options RequestOptions
	for _, opt := range opts {
		opt(&options)
	}

	req, err := http.NewRequest(method, url, reqBody)
	if err != nil {
		logger.Print(logger.ERROR, "Got error creating new request", err)
		return nil, err
	}

	for headerKey, headerValue := range headers {
		req.Header.Set(headerKey, headerValue)
	}

	if method == "POST" && req.Header.Get("Content-Type") == "" {
		req.Header.Set("Content-Type", "application/json")
	}

	q := req.URL.Query()
	for k, v := range urlParams {
		q.Add(k, v)
	}
	req.URL.RawQuery = q.Encode()

	resp, err := HttpClient.Do(req)
	if err != nil {
		if options.ErrorMsg != "" && strings.Contains(err.Error(), options.ErrorMsg) {
			return nil, err
		}
		logger.Print(logger.ERROR, "Got error sending request", err)
		return nil, err
	}

	defer resp.Body.Close()

	statusOK := resp.StatusCode >= 200 && resp.StatusCode < 300
	if !statusOK {
		respBody, _ := ioutil.ReadAll(resp.Body)
		reqJson, _ := json.Marshal(reqBody)
		err = errors.New("Status code: " + strconv.Itoa(resp.StatusCode) + " - " + string(respBody))

		if !slices.Contains(options.HandledErrorCodes, resp.StatusCode) {
			logger.Print(logger.ERROR, "Got error code from server", err, req, string(reqJson))
		}

		return nil, err
	}

	b, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logger.Print(logger.ERROR, "Got error reading response", err)
		return nil, err
	}

	return b, nil
}

package incidents

import (
	"strings"

	"github.com/precize/common"
	"github.com/precize/common/context"
)

func ReprioritiseBasedOnOwner(pData *PrioritisationData, crsDoc common.CloudResourceStoreDoc) {

	ownerCount := 0
	for _, owner := range crsDoc.Owner {
		if strings.Contains(owner, strings.Trim(contextutils.EX_EMPLOYEE_PREFIX, " ")) {
			//  ex-employee
			ownerCount++
			SetLikelihood(pData.PrecizeLikelihood, common.VERY_LIKELY_LIKELIHOOD,
				EXEMPLOYEE_CRITERIA, pData, true)
			break
		} else if strings.Contains(owner, "NONE") {
			// unknown employee
			if pData.IsResourceReprioritisation {
				SetImpact(pData.PrecizeImpact, common.MODERATE_IMPACT,
					OWNERABSENT_CRITERIA, pData, true)
			} else if pData.SourceImpact == impactStringToNumber[common.NEGLIGIBLE_IMPACT] {
				SetImpact(pData.PrecizeImpact, common.MINOR_IMPACT,
					OWNERABSENT_CRITERIA, pData, true)
				break
			} else if pData.SourceImpact == impactStringToNumber[common.MINOR_IMPACT] {
				if pData.IsResourceProd {
					SetImpact(pData.PrecizeImpact, common.MODERATE_IMPACT,
						OWNERABSENT_CRITERIA, pData, true)
				} else {
					SetImpact(pData.PrecizeImpact, common.MINOR_IMPACT,
						OWNERABSENT_CRITERIA, pData, true)
				}
				break
			} else if pData.SourceImpact == impactStringToNumber[common.MODERATE_IMPACT] {
				if pData.IsResourceProd {
					SetImpact(pData.PrecizeImpact, common.SIGNIFICANT_IMPACT,
						OWNERABSENT_CRITERIA, pData, true)
				} else {
					SetImpact(pData.PrecizeImpact, common.MODERATE_IMPACT,
						OWNERABSENT_CRITERIA, pData, true)
				}

				break
			}
		} else {
			// valid employee
			ownerCount++
		}
	}

	if pData.IsResourceReprioritisation {
		return
	}

	pData.OwnerCount = ownerCount

	var noOwnerPercentage float64
	if pData.tenantValues.resourceCount > 0 {
		noOwnerPercentage = float64(pData.accountValues.noOwnersCount) / float64(pData.tenantValues.resourceCount) * 100
	}

	if noOwnerPercentage > 60 {
		// If more than 60% resources have no owners
		SetLikelihood(pData.PrecizeLikelihood, common.LIKELY_LIKELIHOOD, NOOWNERACC_CRITERIA, pData, true)
	} else if noOwnerPercentage > 20 {
		// If more than 20 - 60% resources have no owners
		SetLikelihood(pData.PrecizeLikelihood, common.POSSIBLE_LIKELIHOOD, MODERATE_NOOWNERACC_CRITERIA, pData, true)

	} else {
		// Most resources have owner
		if pData.IsResourceProd && (pData.OpenToInternet || pData.SensitiveData) {
			DecreaseLikelihoodByOneLevel(pData.PrecizeLikelihood, FEWNOOWNERACC_CRITERIA, pData, false)
		} else if pData.IsResourceProd {
			SetLikelihood(pData.PrecizeLikelihood, common.UNLIKELY_LIKELIHOOD, FEWNOOWNERACC_CRITERIA, pData, false)
		} else {
			SetLikelihood(pData.PrecizeLikelihood, common.VERY_UNLIKELY_LIKELIHOOD, FEWNOOWNERACC_CRITERIA, pData, false)
		}
	}
}

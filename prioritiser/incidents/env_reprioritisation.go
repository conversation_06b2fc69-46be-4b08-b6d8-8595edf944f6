package incidents

import (
	"strings"

	"github.com/precize/common"
	"github.com/precize/common/context"
)

func ReprioritiseBasedOnEnv(pData *PrioritisationData, crsDoc common.CloudResourceStoreDoc) {

	var isSandbox, isQA bool

	for _, env := range crsDoc.Environment {
		if strings.Contains(env, contextutils.PROD_ENV) {
			// Increase impact for prod env
			pData.IsResourceProd = true
			if pData.OpenToInternet {
				SetImpactWithoutAvg(common.SIGNIFICANT_IMPACT, PRODENV_CRITERIA, pData, true)
			} else {
				SetImpact(pData.PrecizeImpact, common.SIGNIFICANT_IMPACT, PRODENV_CRITERIA, pData, true)
			}
			return
		}
		if strings.Contains(env, contextutils.SANDBOX_ENV) || strings.Contains(env, contextutils.DEV_ENV) {
			isSandbox = true
		}
		if strings.Contains(env, contextutils.STAGING_ENV) {
			isQA = true
		}

		if pData.IsResourceReprioritisation {
			if isQA {
				SetImpactWithoutAvg(common.MODERATE_IMPACT, DEVENV_CRITERIA, pData, true)
				return
			} else if isSandbox {
				SetImpactWithoutAvg(common.MINOR_IMPACT, SANDBOXENV_CRITERIA, pData, false)
				return
			}

			return
		}

		if isQA || isSandbox {

			var prodPercentage float64
			if pData.accountValues.totalEnvCount > 0 {
				prodPercentage = float64(pData.accountValues.prodEnvCount) / float64(pData.accountValues.totalEnvCount) * 100
			}

			if isSandbox {
				if strings.Contains(env, contextutils.SANDBOX_ENV) {
					pData.PriorityCriteria[SANDBOXENV_CRITERIA] = false
				} else if strings.Contains(env, contextutils.DEV_ENV) {
					pData.PriorityCriteria[DEVENV_CRITERIA] = false
				}
			} else if isQA {
				pData.PriorityCriteria[QAENV_CRITERIA] = false
			}

			if prodPercentage >= 10 {
				// If more than 10% of resource are in Prod
				if isQA {
					SetImpact(pData.PrecizeImpact, common.MODERATE_IMPACT, QAPRODACCENV_CRITERIA, pData, true)
					return
				} else if isSandbox {
					SetImpact(pData.PrecizeImpact, common.MINOR_IMPACT, QAPRODACCENV_CRITERIA, pData, false)
					return
				}
			} else {
				// If less than 10% of resource are in Prod
				if isQA {
					SetImpact(pData.PrecizeImpact, common.MINOR_IMPACT, QANONPRODACCENV_CRITERIA, pData, false)
					return
				} else if isSandbox {
					SetImpact(pData.PrecizeImpact, common.NEGLIGIBLE_IMPACT, QANONPRODACCENV_CRITERIA, pData, false)
					return
				}
			}
		}
	}
}

func ReprioritiseCostBasedOnEnv(pData *PrioritisationData, crsDoc common.CloudResourceStoreDoc) {

	for _, env := range crsDoc.Environment {
		if strings.Contains(env, contextutils.PROD_ENV) {
			pData.IsResourceProd = true
			pData.PrecizeRisk = DecreaseRiskByOneLevel(pData.PrecizeRisk)
			pData.PriorityCriteria[PRODENV_CRITERIA] = false
		}
		if strings.Contains(env, contextutils.SANDBOX_ENV) || strings.Contains(env, contextutils.DEV_ENV) || strings.Contains(env, contextutils.STAGING_ENV) {
			if pData.PrecizeRisk != common.HIGH_RISK {
				pData.PrecizeRisk = IncreaseRiskByOneLevel(pData.PrecizeRisk)
			}
			pData.PriorityCriteria[DEVENV_CRITERIA] = true
		}
	}
}

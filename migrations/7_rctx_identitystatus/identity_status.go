package main

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/precize/common"
	contextutils "github.com/precize/common/context"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

func AddIdentityStatusForDeletedRctx() {

	oldestDate := time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)
	endDate := time.Date(2025, 9, 3, 0, 20, 40, 477000000, time.UTC)

	currentDate := endDate

	for currentDate.After(oldestDate) {

		prevMonth := time.Date(currentDate.Year(), currentDate.Month()-1, 1, 0, 0, 0, 0, time.UTC)
		if prevMonth.Before(oldestDate) {
			prevMonth = oldestDate
		}

		logger.Print(logger.INFO, fmt.Sprintf("Processing records from %s to %s",
			prevMonth.Format(time.RFC3339), currentDate.Format(time.RFC3339)))

		processMonthlyBatch(prevMonth, currentDate)

		currentDate = prevMonth
	}

	logger.Print(logger.INFO, "Migration complete")
}

func processMonthlyBatch(startDate, endDate time.Time) {
	var (
		searchAfter                any
		bulkResourceContextRequest strings.Builder
		recordsCount               int
	)

	rctxQuery := fmt.Sprintf(`{
		"query": {
			"bool": {
				"must": [
					{
						"range": {
							"updatedTime": {
								"gte": "%s",
								"lt": "%s"
							}
						}
					},
					{
						"exists": {
							"field": "updatedTime"
						}
					}
				]
			}
		}
	}`, startDate.Format(time.RFC3339), endDate.Format(time.RFC3339))

	for {
		rctxDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.RESOURCE_CONTEXT_INDEX}, rctxQuery, searchAfter)
		if err != nil {
			return
		}

		if len(rctxDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		for docID, rctxDoc := range rctxDocs {
			var rctxRscDoc common.ResourceContextInsertDoc
			jsonBytes, er := json.Marshal(rctxDoc)
			if er != nil {
				return
			}
			err = json.Unmarshal(jsonBytes, &rctxRscDoc)
			if err != nil {
				return
			}

			for i, rctx := range rctxRscDoc.DefinedOwners {
				addIdentityStatus(&rctx)
				rctxRscDoc.DefinedOwners[i] = rctx
			}
			for i, rctx := range rctxRscDoc.CodeOwners {
				addIdentityStatus(&rctx)
				rctxRscDoc.CodeOwners[i] = rctx
			}

			for i, rctx := range rctxRscDoc.DerivedOwners {
				addIdentityStatus(&rctx)
				rctxRscDoc.DerivedOwners[i] = rctx
			}

			for i, rctx := range rctxRscDoc.InheritedOwners {
				addIdentityStatus(&rctx)
				rctxRscDoc.InheritedOwners[i] = rctx
			}

			for i, rctx := range rctxRscDoc.CostOwners {
				addIdentityStatus(&rctx)
				rctxRscDoc.CostOwners[i] = rctx
			}

			for i, rctx := range rctxRscDoc.SecurityOwners {
				addIdentityStatus(&rctx)
				rctxRscDoc.SecurityOwners[i] = rctx
			}

			for i, rctx := range rctxRscDoc.OpsOwners {
				addIdentityStatus(&rctx)
				rctxRscDoc.OpsOwners[i] = rctx
			}

			resourceContextInsertMetadata := `{"index": {"_id": "` + docID + `"}}`
			resourceContextInsertDoc, err := json.Marshal(rctxRscDoc)
			if err != nil {
				logger.Print(logger.ERROR, "Got error marshalling document", err)
				return
			}

			bulkResourceContextRequest.WriteString(resourceContextInsertMetadata)
			bulkResourceContextRequest.WriteString("\n")
			bulkResourceContextRequest.Write(resourceContextInsertDoc)
			bulkResourceContextRequest.WriteString("\n")
			recordsCount++

		}

		if recordsCount > 0 {

			if err := elastic.BulkDocumentsAPI("", elastic.RESOURCE_CONTEXT_INDEX, bulkResourceContextRequest.String()); err != nil {
				return
			}

			logger.Print(logger.INFO, "Resource context bulk API Successful for "+strconv.Itoa(recordsCount)+" records	")

			recordsCount = 0
			bulkResourceContextRequest.Reset()
		}
	}
}

func addIdentityStatus(rctx *common.ResourceContextItem) {

	var (
		identityId               = ""
		serviceIdentity          = false
		nonHumanIdentitySuffixes = []string{
			contextutils.AWSSERVICE_USER_SUFFIX,
			contextutils.ACCOUNT_USER_SUFFIX,
			contextutils.IAM_USER_SUFFIX,
			contextutils.IAM_ROLE_SUFFIX,
			contextutils.APP_USER_SUFFIX,
			contextutils.SERVICEACCOUNT_USER_SUFFIX,
		}
	)

	if strings.Contains(rctx.Name, "<") && strings.Contains(rctx.Name, ">") {
		if common.HasAnySuffix(rctx.Name, nonHumanIdentitySuffixes) {

			serviceIdentity = true
			identityId = common.RemoveSuffixes(rctx.Name, nonHumanIdentitySuffixes)
		}
	}

	if len(identityId) <= 0 {
		// for identity id's with only name
		identityId = rctx.Name
	}

	if serviceIdentity {
		rctx.IdentityStatus = common.IntPtr(common.VALID_IDENTITY_STATUS)
	} else if strings.HasPrefix(identityId, contextutils.EX_EMPLOYEE_PREFIX) {
		rctx.IdentityStatus = common.IntPtr(common.EX_EMPLOYEE_IDENTITY_STATUS)
	} else if strings.HasPrefix(identityId, contextutils.INVALID_EMPLOYEE_PREFIX) {
		rctx.IdentityStatus = common.IntPtr(common.AMBIGUOUS_IDENTITY_STATUS)
	} else {
		rctx.IdentityStatus = common.IntPtr(common.CURRENT_EMPLOYEE_IDENTITY_STATUS)
	}

	return

}

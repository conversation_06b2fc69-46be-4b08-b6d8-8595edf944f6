package main

import (
	"flag"
	"os"
	"os/signal"
	"syscall"

	"github.com/precize/common"
	"github.com/precize/config"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/transport"
)

// Date - 04/09/2025
// Target - RCTX
// This migration is to add identity status for deleted rctx items

func main() {
	var (
		appConfigPath = flag.String("config", "application.yml", "Path to application.yml")
	)

	flag.Parse()

	logger.InitializeLogs("migration", false)

	defaultConf, err := config.InitializeApplicationConfig(*appConfigPath)

	if err != nil {
		return
	} else if defaultConf {
		logger.Print(logger.INFO, "Application config could not be read. Starting with defaults", *appConfigPath)
	}

	if err = elastic.ConnectToElasticSearch(); err != nil {
		return
	}

	transport.SetHttpClient()
	common.InitializeOpenAI()
	gracefullyShutDown()

	AddIdentityStatusForDeletedRctx()
	os.Exit(0)
}

func gracefullyShutDown() {

	sigs := make(chan os.Signal, 1)

	signal.Notify(sigs, syscall.SIGINT, syscall.SIGTERM)

	go func() {

		sig := <-sigs
		logger.Print(logger.INFO, "Signal received", sig)
		os.Exit(1)
	}()
}

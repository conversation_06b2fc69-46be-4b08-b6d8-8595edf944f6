package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"os"
	"os/signal"
	"strings"
	"syscall"

	"github.com/precize/common"
	"github.com/precize/config"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/transport"
)

// Date - 08/19/2025
// Target - IDENTITIES index
// This migration is to identify records where identityId equals primaryEmail (with case),
// collect these identityIds, and then mark records as deleted where identityId
// matches these values and meets additional criteria

func main() {
	var (
		appConfigPath = flag.String("config", "application.yml", "Path to application.yml")
	)

	flag.Parse()

	logger.InitializeLogs("migration", false)

	defaultConf, err := config.InitializeApplicationConfig(*appConfigPath)
	if err != nil {
		return
	} else if defaultConf {
		logger.Print(logger.INFO, "Application config could not be read. Starting with defaults", *appConfigPath)
	}

	if err = elastic.ConnectToElasticSearch(); err != nil {
		return
	}

	transport.SetHttpClient()
	common.InitializeOpenAI()
	gracefullyShutDown()

	identityIds, affectedCount, err := FixPrimaryEmails()
	if err != nil {
		logger.Print(logger.ERROR, "Error fixing primary emails", err)
		os.Exit(1)
	}

	logger.Print(logger.INFO, fmt.Sprintf("Found %d identities with primaryEmail=identityId", len(identityIds)))
	logger.Print(logger.INFO, fmt.Sprintf("Marked %d derived identities as deleted", affectedCount))

	os.Exit(0)
}

func gracefullyShutDown() {
	sigs := make(chan os.Signal, 1)
	signal.Notify(sigs, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		sig := <-sigs
		logger.Print(logger.INFO, "Signal received", sig)
		os.Exit(1)
	}()
}

func FixPrimaryEmails() ([]string, int, error) {
	query := map[string]any{
		"query": map[string]any{
			"bool": map[string]any{
				"must": []map[string]any{
					{
						"wildcard": map[string]any{
							"primaryEmail.keyword": "*",
						},
					},
					{
						"match": map[string]any{
							"deleted": "false",
						},
					},
				},
			},
		},
	}

	queryBytes, err := json.Marshal(query)
	if err != nil {
		logger.Print(logger.ERROR, "Error marshalling query to JSON", err)
		return nil, 0, err
	}

	var (
		searchAfter  any
		identityIds  []string
		processCount int
		updateCount  int
		bulkRequest  string
	)

	for {
		identitiesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.IDENTITIES_INDEX}, string(queryBytes), searchAfter)
		if err != nil {
			logger.Print(logger.ERROR, "Error executing search query", err)
			return identityIds, 0, err
		}

		if len(identitiesDocs) == 0 {
			break
		}

		searchAfter = sortResponse
		processCount += len(identitiesDocs)

		for _, identityDoc := range identitiesDocs {
			identityId, hasIdentityId := identityDoc["identityId"].(string)
			primaryEmail, hasPrimaryEmail := identityDoc["primaryEmail"].(string)
			docId, _ := identityDoc["id"].(string)

			if !hasIdentityId || !hasPrimaryEmail {
				continue
			}

			if strings.ToLower(identityId) == strings.ToLower(primaryEmail) {
				identityIds = append(identityIds, primaryEmail)

				updateMetadata := `{"update": {"_id": "` + docId + `"}}`
				updateDoc := `{"doc": {"primaryEmail": ""}}`

				bulkRequest += updateMetadata + "\n" + updateDoc + "\n"
				updateCount++

				if updateCount%1000 == 0 {
					err = elastic.BulkDocumentsAPI("", elastic.IDENTITIES_INDEX, bulkRequest)
					if err != nil {
						logger.Print(logger.ERROR, "Error executing bulk update", err)
						return identityIds, 0, err
					}

					logger.Print(logger.INFO, fmt.Sprintf("Bulk updated %d primaryEmail documents", updateCount))
					bulkRequest = ""
				}
			}
		}

		logger.Print(logger.INFO, fmt.Sprintf("Processed %d documents, found %d matching identityId=primaryEmail", processCount, len(identityIds)))
	}

	if len(bulkRequest) > 0 {
		err = elastic.BulkDocumentsAPI("", elastic.IDENTITIES_INDEX, bulkRequest)
		if err != nil {
			logger.Print(logger.ERROR, "Error executing final bulk update", err)
			return identityIds, 0, err
		}

		logger.Print(logger.INFO, fmt.Sprintf("Final bulk update executed for remaining %d primaryEmail documents", updateCount%1000))
	}

	if len(identityIds) == 0 {
		logger.Print(logger.INFO, "No matching identityIds found, skipping phase 2")
		return identityIds, 0, nil
	}

	logger.Print(logger.INFO, fmt.Sprintf("Starting phase 2: marking derived identities for %d identityIds", len(identityIds)))

	var affectedCount int
	bulkRequest = ""

	for i := 0; i < len(identityIds); i += 50 {
		end := i + 50
		if end > len(identityIds) {
			end = len(identityIds)
		}

		batchIds := identityIds[i:end]

		derivedQuery := map[string]any{
			"query": map[string]any{
				"bool": map[string]any{
					"must": []map[string]any{
						{
							"terms": map[string]any{
								"identityId.keyword": batchIds,
							},
						},
						{
							"match": map[string]any{
								"deleted": "false",
							},
						},
						{
							"match": map[string]any{
								"type.keyword": "DERIVED_IDENTITY",
							},
						},
						{
							"wildcard": map[string]any{
								"additionalInfo.keyword": "*createdBy*",
							},
						},
						{
							"wildcard": map[string]any{
								"additionalInfo.keyword": "*enhancer*",
							},
						},
					},
					"must_not": []map[string]any{
						{
							"wildcard": map[string]any{
								"additionalInfo.keyword": "*resCtxId*",
							},
						},
					},
				},
			},
			"size": 1000,
		}

		derivedQueryBytes, err := json.Marshal(derivedQuery)
		if err != nil {
			logger.Print(logger.ERROR, "Error marshalling derived query to JSON", err)
			return identityIds, affectedCount, err
		}

		var derivedSearchAfter any

		for {
			derivedDocs, derivedSortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.IDENTITIES_INDEX}, string(derivedQueryBytes), derivedSearchAfter)
			if err != nil {
				logger.Print(logger.ERROR, "Error executing derived search query", err)
				return identityIds, affectedCount, err
			}

			if len(derivedDocs) == 0 {
				break
			}

			derivedSearchAfter = derivedSortResponse

			for _, derivedDoc := range derivedDocs {
				docId, _ := derivedDoc["id"].(string)

				updateMetadata := `{"update": {"_id": "` + docId + `"}}`
				updateDoc := `{"doc": {"deleted": true}}`

				bulkRequest += updateMetadata + "\n" + updateDoc + "\n"
				affectedCount++

				if affectedCount%1000 == 0 {
					err = elastic.BulkDocumentsAPI("", elastic.IDENTITIES_INDEX, bulkRequest)
					if err != nil {
						logger.Print(logger.ERROR, "Error executing derived bulk update", err)
						return identityIds, affectedCount, err
					}

					logger.Print(logger.INFO, fmt.Sprintf("Marked %d derived identities as deleted", affectedCount))
					bulkRequest = ""
				}
			}
		}
	}

	if len(bulkRequest) > 0 {
		err = elastic.BulkDocumentsAPI("", elastic.IDENTITIES_INDEX, bulkRequest)
		if err != nil {
			logger.Print(logger.ERROR, "Error executing final derived bulk update", err)
			return identityIds, affectedCount, err
		}

		logger.Print(logger.INFO, fmt.Sprintf("Final derived bulk update executed for remaining %d documents", affectedCount%1000))
	}

	return identityIds, affectedCount, nil
}

package main

import (
	"flag"
	"os"
	"os/signal"
	"syscall"

	"github.com/precize/common"
	"github.com/precize/config"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/transport"
)

// Date - 30/07/2025
// Target - Text Lookup
// This migration is to update the hasName field in text lookup

func main() {
	var (
		appConfigPath = flag.String("config", "application.yml", "Path to application.yml")
		txtID         = flag.String("txtID", "", "Text ID to run migration for")
		hasName       = flag.Bool("hasName", true, "true/false")
	)

	flag.Parse()

	logger.InitializeLogs("migration", false)

	if len(*txtID) <= 0 {
		logger.Print(logger.ERROR, "Txt ID or hasName not specified")
		os.Exit(1)
	}

	defaultConf, err := config.InitializeApplicationConfig(*appConfigPath)
	if err != nil {
		return
	} else if defaultConf {
		logger.Print(logger.INFO, "Application config could not be read. Starting with defaults", *appConfigPath)
	}

	if err = elastic.ConnectToElasticSearch(); err != nil {
		return
	}

	transport.SetHttpClient()
	common.InitializeOpenAI()

	gracefullyShutDown()

	UpdateTextLookupHasName(*txtID, *hasName)
	os.Exit(0)
}

func gracefullyShutDown() {

	sigs := make(chan os.Signal, 1)

	signal.Notify(sigs, syscall.SIGINT, syscall.SIGTERM)

	go func() {

		sig := <-sigs
		logger.Print(logger.INFO, "Signal received", sig)
		os.Exit(1)
	}()
}

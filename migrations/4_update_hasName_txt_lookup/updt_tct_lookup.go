package main

import (
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

func UpdateTextLookupHasName(txtID string, hasName bool) {

	logger.Print(logger.INFO, "Updating text lookup for txtID", txtID)

	if err := elastic.UpdateDocument(
		elastic.TEXT_LOOKUP_INDEX,
		txtID,
		struct {
			Update bool `json:"hasName"`
		}{
			Update: hasName,
		},
	); err != nil {
		logger.Print(logger.ERROR, "Error updating text lookup", err)
	}

	logger.Print(logger.INFO, "Updated text lookup for txtID", txtID)
	return
}

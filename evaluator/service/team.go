package service

import (
	analyzerCommon "github.com/precize/evaluator/common"
)

func (rtcxItem *ResourceContextWrapper) DeriveTeamContextCategory() {

	rctxInsertDoc := rtcxItem.ResourceContextInsertDoc
	customerAddedContext := map[string]struct{}{}

	if len(rctxInsertDoc.DefinedTeam) <= 0 && len(rctxInsertDoc.DerivedTeam) <= 0 && len(rctxInsertDoc.InheritedTeam) <= 0 {
		rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.TEAM_NoContextDerived)
		return
	}

	if len(rctxInsertDoc.DefinedTeam) > 0 {
		for _, definedTeam := range rctxInsertDoc.DefinedTeam {
			customerAddedContext[definedTeam.IdentityId] = struct{}{}
			break
		}
	}

	if len(customerAddedContext) <= 0 {
		rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.TEAM_AllContextDerived)
		return
	} else {
		for team := range customerAddedContext {
			foundTeam := false
			addnTeamDerived := false

			for _, definedTeam := range rctxInsertDoc.DefinedTeam {
				if definedTeam.IdentityId == team {
					foundTeam = true
					break
				} else {
					addnTeamDerived = true
				}
			}

			if foundTeam {
				continue
			}

			for _, derivedTeam := range rctxInsertDoc.DerivedTeam {
				if derivedTeam.IdentityId == team {
					foundTeam = true
					break
				} else {
					addnTeamDerived = true
				}
			}

			if foundTeam {
				continue
			}

			for _, inheritedTeam := range rctxInsertDoc.InheritedTeam {
				if inheritedTeam.IdentityId == team {
					foundTeam = true
					break
				} else {
					addnTeamDerived = true
				}
			}

			if !foundTeam && (len(rctxInsertDoc.DefinedTeam) > 0 || len(rctxInsertDoc.DerivedTeam) > 0) {
				rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.TEAM_ContradictoryContext)
				return
			}

			if !addnTeamDerived {
				rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.TEAM_NOADDNCONTEXTDERIVED)
				return
			}
		}
	}

	rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.TEAM_ExtraContextAdded)
	return
}

package service

import (
	"github.com/precize/common"
)

type ResourceContextWrapper struct {
	ResourceContextInsertDoc *common.ResourceContextInsertDoc
}

func DeriveContextLabels(resourceContextDoc *common.ResourceContextInsertDoc) {

	resourceContextWrapper := &ResourceContextWrapper{
		ResourceContextInsertDoc: resourceContextDoc,
	}

	resourceContextWrapper.DeriveOwnerContextCategory()
	resourceContextWrapper.DeriveTeamContextCategory()
	resourceContextWrapper.DeriveAppContextCategory()
	resourceContextWrapper.DeriveSoftwareComponentContextCategory()
	resourceContextWrapper.DeriveEnvContextCategory()
	resourceContextWrapper.DeriveDeploymentContextCategory()
	resourceContextWrapper.DeriveSensitivityContextCategory()
	resourceContextWrapper.DeriveComplianceContextCategory()
	resourceContextWrapper.DeriveTTLContextCategory()

	return
}

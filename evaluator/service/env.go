package service

import (
	"strings"

	"github.com/precize/common"
	"github.com/precize/common/context"
	analyzerCommon "github.com/precize/evaluator/common"
)

func (rctxItem *ResourceContextWrapper) DeriveEnvContextCategory() {

	rctxInsertDoc := rctxItem.ResourceContextInsertDoc
	customerAddedContext := map[string]struct{}{}

	if len(rctxInsertDoc.DefinedEnv) <= 0 && len(rctxInsertDoc.DerivedEnv) <= 0 && len(rctxInsertDoc.InheritedEnv) <= 0 {
		rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.ENV_NoContextDerived)
		return
	}

	if len(rctxInsertDoc.DefinedEnv) > 0 {
		for _, definedEnv := range rctxInsertDoc.DefinedEnv {
			if strings.Contains(definedEnv.Type, contextutils.TAG_PREFIX) {
				customerAddedContext[definedEnv.IdentityId] = struct{}{}
			}

			if strings.Contains(definedEnv.Type, common.CUSTOMER_DEFINED_USER_TYPE) {
				if len(definedEnv.Desc) > 0 && !strings.Contains(definedEnv.Desc, "support_") && !strings.Contains(definedEnv.Desc, "<EMAIL>") {
					customerAddedContext[definedEnv.IdentityId] = struct{}{}
				}
			}
		}
	}

	if len(customerAddedContext) <= 0 {
		rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.ENV_AllContextDerived)
		return
	} else {
		for env := range customerAddedContext {
			foundEnv := false
			addnEnvDerived := false

			for _, definedEnv := range rctxInsertDoc.DefinedEnv {
				if !strings.Contains(definedEnv.Type, contextutils.TAG_PREFIX) && !strings.Contains(definedEnv.Type, common.CUSTOMER_DEFINED_USER_TYPE) {
					if definedEnv.IdentityId == env {
						foundEnv = true
						break
					} else {
						addnEnvDerived = true
					}
				}
			}

			if foundEnv {
				continue
			}

			for _, derivedEnv := range rctxInsertDoc.DerivedEnv {
				if derivedEnv.IdentityId == env {
					foundEnv = true
					break
				} else {
					addnEnvDerived = true
				}
			}

			if foundEnv {
				continue
			}

			for _, inheritedEnv := range rctxInsertDoc.InheritedEnv {
				if inheritedEnv.IdentityId == env {
					foundEnv = true
					break
				} else {
					addnEnvDerived = true
				}
			}

			if !foundEnv && (len(rctxInsertDoc.DefinedEnv) > 0 || len(rctxInsertDoc.DerivedEnv) > 0) {
				rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.ENV_ContradictoryContext)
				return
			}

			if !addnEnvDerived {
				rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.ENV_NOADDNCONTEXTDERIVED)
				return
			}
		}
	}

	rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.ENV_ExtraContextAdded)
	return

}

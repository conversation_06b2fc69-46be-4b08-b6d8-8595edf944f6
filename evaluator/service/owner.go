package service

import (
	"strings"

	"github.com/precize/common"
	"github.com/precize/common/context"
	analyzerCommon "github.com/precize/evaluator/common"
)

func (rctxItem *ResourceContextWrapper) DeriveOwnerContextCategory() {

	rctxInsertDoc := rctxItem.ResourceContextInsertDoc
	customerAddedContext := map[string]struct{}{}

	if len(rctxInsertDoc.DefinedOwners) <= 0 && len(rctxInsertDoc.DerivedOwners) <= 0 && len(rctxInsertDoc.CodeOwners) <= 0 && len(rctxInsertDoc.OpsOwners) <= 0 {
		rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.OWNER_NoContextDerived)
		return
	}

	if len(rctxInsertDoc.DefinedOwners) > 0 {

		for _, definedOwner := range rctxInsertDoc.DefinedOwners {
			if strings.Contains(definedOwner.Type, contextutils.TAG_PREFIX) {
				customerAddedContext[definedOwner.IdentityId] = struct{}{}
			}

			if strings.Contains(definedOwner.Type, common.CUSTOMER_DEFINED_USER_TYPE) {
				if len(definedOwner.Desc) > 0 && !strings.Contains(definedOwner.Desc, "support_") && !strings.Contains(definedOwner.Desc, "<EMAIL>") {
					customerAddedContext[definedOwner.IdentityId] = struct{}{}
				}
			}
		}
	}

	if len(customerAddedContext) <= 0 {
		rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.OWNER_AllContextDerived)
		return
	} else {

		for owner := range customerAddedContext {
			foundOwner := false
			addnOwnerDerived := false
			for _, definedOwner := range rctxInsertDoc.DefinedOwners {
				if !strings.Contains(definedOwner.Type, contextutils.TAG_PREFIX) && !strings.Contains(definedOwner.Type, common.CUSTOMER_DEFINED_USER_TYPE) {
					if definedOwner.IdentityId == owner || definedOwner.ChildIdentityID == owner {
						foundOwner = true
						break
					} else {
						addnOwnerDerived = true
					}
				}
			}

			if foundOwner {
				continue
			}

			for _, derivedOwner := range rctxInsertDoc.DerivedOwners {
				if derivedOwner.IdentityId == owner || derivedOwner.ChildIdentityID == owner {
					foundOwner = true
					break
				} else {
					addnOwnerDerived = true
				}
			}

			if foundOwner {
				continue
			}

			for _, codeOwner := range rctxInsertDoc.CodeOwners {
				if codeOwner.IdentityId == owner || codeOwner.ChildIdentityID == owner {
					foundOwner = true
					break
				} else {
					addnOwnerDerived = true
				}
			}

			if foundOwner {
				continue
			}

			for _, opsOwner := range rctxInsertDoc.OpsOwners {
				if opsOwner.IdentityId == owner || opsOwner.ChildIdentityID == owner {
					foundOwner = true
					break
				} else {
					addnOwnerDerived = true
				}
			}

			if foundOwner {
				continue
			}

			for _, secOwner := range rctxInsertDoc.SecurityOwners {
				if secOwner.IdentityId == owner || secOwner.ChildIdentityID == owner {
					foundOwner = true
					break
				} else {
					addnOwnerDerived = true
				}
			}

			if !foundOwner && (len(rctxInsertDoc.DefinedOwners) > 0 || len(rctxInsertDoc.DerivedOwners) > 0) {
				rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.OWNER_ContradictoryContext)
				return
			}

			if !addnOwnerDerived {
				rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.OWNER_NOADDNCONTEXTDERIVED)
				return
			}
		}
	}

	rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.OWNER_ExtraContextAdded)
	return
}

package service

import (
	analyzerCommon "github.com/precize/evaluator/common"
)

func (rtcxItem *ResourceContextWrapper) DeriveComplianceContextCategory() {

	rctxInsertDoc := rtcxItem.ResourceContextInsertDoc
	customerAddedContext := map[string]struct{}{}

	if len(rctxInsertDoc.DefinedCompliance) <= 0 && len(rctxInsertDoc.DerivedCompliance) <= 0 && len(rctxInsertDoc.InheritedCompliance) <= 0 {
		rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.COMPLIANCE_NoContextDerived)
		return
	}

	if len(rctxInsertDoc.DefinedCompliance) > 0 {
		for _, definedCompliance := range rctxInsertDoc.DefinedCompliance {
			customerAddedContext[definedCompliance.IdentityId] = struct{}{}
			break
		}
	}

	if len(customerAddedContext) <= 0 {
		rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.COMPLIANCE_AllContextDerived)
		return
	} else {

		for compliance := range customerAddedContext {
			foundCompliance := false
			addnComplianceDerived := false

			for _, definedCompliance := range rctxInsertDoc.DefinedCompliance {
				if definedCompliance.IdentityId == compliance {
					foundCompliance = true
					break
				} else {
					addnComplianceDerived = true
				}
			}

			if foundCompliance {
				continue
			}

			for _, derivedCompliance := range rctxInsertDoc.DerivedCompliance {
				if derivedCompliance.IdentityId == compliance {
					foundCompliance = true
					break
				} else {
					addnComplianceDerived = true
				}
			}

			if foundCompliance {
				continue
			}

			for _, inheritedCompliance := range rctxInsertDoc.InheritedCompliance {
				if inheritedCompliance.IdentityId == compliance {
					foundCompliance = true
					break
				} else {
					addnComplianceDerived = true
				}
			}

			if !foundCompliance && (len(rctxInsertDoc.DefinedCompliance) > 0 || len(rctxInsertDoc.DerivedCompliance) > 0) {
				rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.COMPLIANCE_ContradictoryContext)
				return
			}

			if !addnComplianceDerived {
				rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.COMPLIANCE_NOADDNCONTEXTDERIVED)
				return
			}
		}
	}

	rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.COMPLIANCE_ExtraContextAdded)
	return
}

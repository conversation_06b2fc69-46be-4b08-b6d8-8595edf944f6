package service

import (
	analyzerCommon "github.com/precize/evaluator/common"
)

func (rtcxItem *ResourceContextWrapper) DeriveTTLContextCategory() {

	rctxInsertDoc := rtcxItem.ResourceContextInsertDoc
	customerAddedContext := map[string]struct{}{}

	if len(rctxInsertDoc.DefinedTTL) <= 0 && len(rctxInsertDoc.DerivedTTL) <= 0 {
		rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.TTL_NoContextDerived)
		return
	}

	if len(rctxInsertDoc.DefinedTTL) > 0 {
		for _, definedTTL := range rctxInsertDoc.DefinedTTL {
			customerAddedContext[definedTTL.IdentityId] = struct{}{}
			break
		}
	}

	if len(customerAddedContext) <= 0 {
		rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.TTL_AllContextDerived)
		return
	} else {
		for ttl := range customerAddedContext {
			foundTTL := false
			addnTTLDerived := false

			for _, definedTTL := range rctxInsertDoc.DefinedTTL {
				if definedTTL.IdentityId == ttl {
					foundTTL = true
					break
				} else {
					addnTTLDerived = true
				}
			}

			if foundTTL {
				continue
			}

			for _, derivedTTL := range rctxInsertDoc.DerivedTTL {
				if derivedTTL.IdentityId == ttl {
					foundTTL = true
					break
				} else {
					addnTTLDerived = true
				}
			}

			if !foundTTL && (len(rctxInsertDoc.DefinedTTL) > 0 || len(rctxInsertDoc.DerivedTTL) > 0) {
				rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.TTL_ContradictoryContext)
				return
			}

			if !addnTTLDerived {
				rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.TTL_NOADDNCONTEXTDERIVED)
				return
			}
		}
	}

	rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.TTL_ExtraContextAdded)
	return
}

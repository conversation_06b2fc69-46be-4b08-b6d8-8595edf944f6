package controller

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"sync"

	"github.com/precize/common"
	"github.com/precize/evaluator/service"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

const (
	MAX_RESOURCE_THREAD = 10
)

func ContextEvaluator(tenantID, lastCollectedAt, serviceID string) {

	logger.Print(logger.INFO, "Processing started for resource context", []string{tenantID})

	resourcesAggQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"term":{"lastCollectedAt.keyword":` + lastCollectedAt + `}},{"term":{"serviceId":` + serviceID + `}}]}},"size":0,"aggs":{"account_aggregation":{"terms":{"field":"account.keyword","size":10000}}}}`
	aggResponse, err := elastic.ExecuteSearchForAggregation([]string{elastic.RESOURCE_CONTEXT_INDEX}, resourcesAggQuery)
	if err != nil {
		logger.Print(logger.ERROR, "Error executing aggregation query", []string{err.Error()})
		return
	}

	accountIds := make([]string, 0)

	if accAggs, ok := aggResponse["account_aggregation"].(map[string]any); ok {
		if accBuckets, ok := accAggs["buckets"].([]any); ok {
			for _, accBucket := range accBuckets {
				if accBucketMap, ok := accBucket.(map[string]any); ok {
					if rg, ok := accBucketMap["key"].(string); ok {
						accountIds = append(accountIds, rg)
					}
				}
			}
		}
	}

	sem := make(chan struct{}, MAX_RESOURCE_THREAD)

	var wg sync.WaitGroup

	for _, accountID := range accountIds {

		sem <- struct{}{}

		wg.Add(1)

		go func(tenantID, lastCollectedAt, serviceID, accID string) {
			defer func() {
				<-sem
				wg.Done()
			}()

			ProcesContext(tenantID, lastCollectedAt, serviceID, accID)
		}(tenantID, lastCollectedAt, serviceID, accountID)
	}

	wg.Wait()
	close(sem)

	logger.Print(logger.INFO, "Processing complete for resource context", []string{tenantID})
}

func ProcesContext(tenantID, lastCollectedAt, serviceID, accountID string) {

	logger.Print(logger.INFO, "Processing started for accountID", []string{tenantID}, accountID)

	var (
		searchAfter                                                          any
		resourcesQuery                                                       = `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"term":{"lastCollectedAt.keyword":` + lastCollectedAt + `}},{"term":{"serviceId":` + serviceID + `}},{"term":{"account.keyword":"` + accountID + `"}}]}}}`
		bulkResourceContextRequest, bulkCloudResourceRequest, bulkCRSRequest strings.Builder
		recordsCount                                                         int
		maxRecords                                                           = 10000
		bulkMutex                                                            sync.Mutex
	)

	for {

		resourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.RESOURCE_CONTEXT_INDEX}, resourcesQuery, searchAfter)
		if err != nil {
			return
		}

		if len(resourcesDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		logger.Print(logger.INFO, "Resources fetched", len(resourcesDocs))

		for docID, resourcesDoc := range resourcesDocs {

			rscContextDocBytes, err := json.Marshal(resourcesDoc)
			if err != nil {
				logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
				return
			}

			var resourceContextDoc common.ResourceContextInsertDoc

			if err = json.Unmarshal(rscContextDocBytes, &resourceContextDoc); err != nil {
				logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err)
				return
			}

			service.DeriveContextLabels(&resourceContextDoc)

			resourceContextInsertMetadata := `{"index": {"_id": "` + docID + `"}}`
			resourceContextInsertDoc, err := json.Marshal(resourceContextDoc)
			if err != nil {
				logger.Print(logger.ERROR, "Got error marshalling document", err)
				return
			}

			crsDocID := common.GenerateCombinedHashIDCaseSensitive(tenantID, lastCollectedAt, fmt.Sprintf("%d", resourceContextDoc.ServiceID), strings.ToLower(resourceContextDoc.Account), strings.ToLower(resourceContextDoc.ResourceID), resourceContextDoc.ResourceType)

			cloudResourceUpdateMetadata := `{"update": {"_id": "` + resourceContextDoc.CloudResourceDocID + `"}}`
			cloudResourceStoreUpdateMetadata := `{"update": {"_id": "` + crsDocID + `"}}`

			var cloudResourceUpdateDoc strings.Builder
			cloudResourceUpdateDoc.WriteString(`{"doc":{"contextLabels":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(resourceContextDoc.ContextLabels, `","`))
			cloudResourceUpdateDoc.WriteString(`"]}}`)

			bulkMutex.Lock()
			bulkResourceContextRequest.WriteString(resourceContextInsertMetadata)
			bulkResourceContextRequest.WriteString("\n")
			bulkResourceContextRequest.Write(resourceContextInsertDoc)
			bulkResourceContextRequest.WriteString("\n")

			bulkCloudResourceRequest.WriteString(cloudResourceUpdateMetadata)
			bulkCloudResourceRequest.WriteString("\n")
			bulkCloudResourceRequest.WriteString(cloudResourceUpdateDoc.String())
			bulkCloudResourceRequest.WriteString("\n")

			bulkCRSRequest.WriteString(cloudResourceStoreUpdateMetadata)
			bulkCRSRequest.WriteString("\n")
			bulkCRSRequest.WriteString(cloudResourceUpdateDoc.String())
			bulkCRSRequest.WriteString("\n")

			recordsCount++

			if recordsCount >= maxRecords {

				if err := elastic.BulkDocumentsAPI(tenantID, elastic.RESOURCE_CONTEXT_INDEX, bulkResourceContextRequest.String()); err != nil {
					bulkMutex.Unlock()
					return
				}

				logger.Print(logger.INFO, "Resource context bulk API Successful for "+strconv.Itoa(recordsCount)+" records	", []string{tenantID}, lastCollectedAt)

				if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_RESOURCES_INDEX, bulkCloudResourceRequest.String()); err != nil {
					bulkMutex.Unlock()
					return
				}

				logger.Print(logger.INFO, "Cloud resource bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID}, lastCollectedAt)

				if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_RESOURCE_STORE_INDEX, bulkCRSRequest.String()); err != nil {
					bulkMutex.Unlock()
					return
				}

				logger.Print(logger.INFO, "Cloud resource store bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID}, lastCollectedAt)

				recordsCount = 0
				bulkResourceContextRequest.Reset()
				bulkCloudResourceRequest.Reset()
				bulkCRSRequest.Reset()
			}

			bulkMutex.Unlock()
			resourceContextInsertDoc = nil
		}
	}

	if recordsCount > 0 {
		if err := elastic.BulkDocumentsAPI(tenantID, elastic.RESOURCE_CONTEXT_INDEX, bulkResourceContextRequest.String()); err != nil {
			return
		}

		logger.Print(logger.INFO, "Resource context bulk API Successful for "+strconv.Itoa(recordsCount)+" records	", []string{tenantID}, lastCollectedAt)

		if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_RESOURCES_INDEX, bulkCloudResourceRequest.String()); err != nil {
			bulkMutex.Unlock()
			return
		}

		logger.Print(logger.INFO, "Cloud resource bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID}, lastCollectedAt)

		if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_RESOURCE_STORE_INDEX, bulkCRSRequest.String()); err != nil {
			bulkMutex.Unlock()
			return
		}

		logger.Print(logger.INFO, "Cloud resource store bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID}, lastCollectedAt)

		recordsCount = 0
		bulkResourceContextRequest.Reset()
		bulkCloudResourceRequest.Reset()
		bulkCRSRequest.Reset()
	}

	logger.Print(logger.INFO, "Processing completed for account", []string{tenantID}, accountID)
}
